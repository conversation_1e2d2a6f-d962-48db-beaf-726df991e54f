//package com.ctrip.car.market.coupon.restful.business;
//
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.sql.SQLException;
//import java.sql.Timestamp;
//
//import com.ctrip.car.market.coupon.restful.cache.LabelCache;
//import com.ctrip.car.market.coupon.restful.contract.*;
//import com.ctrip.car.market.coupon.restful.dto.ImgDTO;
//import com.ctrip.car.market.coupon.restful.qconfig.QConfigHelper;
//import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
//import com.ctrip.car.market.coupon.restful.dto.LabelDto;
//import com.ctrip.car.market.job.common.entity.CpnLabelDO;
//import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
//import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
//import com.ctrip.car.market.coupon.restful.dto.QualityServicesDto;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.Before;
//import org.junit.After;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.exceptions.base.MockitoException;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.junit4.PowerMockRunnerDelegate;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import java.util.*;
//
///**
// * QueryQualityServicesInfoBusiness Tester.
// *
// * <AUTHOR> name>
// * @version 1.0
// * @since <pre>ʮ�� 24, 2020</pre>
// */
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//@PrepareForTest({SDMarketingServiceClient.class, BaseUtils.class, QConfigHelper.class})
//@SuppressStaticInitializationFor({"com.ctrip.car.market.framework.DesensitizeUtil", "com.ctrip.car.market.coupon.restful.utils.LazyHolders"})
////阻止静态代码块运行
//
//public class QueryQualityServicesInfoBusinessTest {
//
//    @Mock
//    private QualityServicesDto configs;
//    @Mock
//    private QualityServicesDto newconfigs;
//    @Mock
//    private LabelCache labelCache;
//    @InjectMocks
//    private QueryQualityServicesInfoBusiness queryQualityServicesInfoBusiness;
//
//
//    @Before
//    public void before() throws Exception {
//        MockitoAnnotations.initMocks(this);
//        PowerMockito.mockStatic(BaseUtils.class);
//
//        PowerMockito.mockStatic(QConfigHelper.class);
//        Mockito.when(QConfigHelper.getChineseTips(Mockito.anyString())).thenReturn("1");
//
//        Map<Long, LabelDto> cpnLabelMap = new HashMap<>();
//        cpnLabelMap.put(10L, new LabelDto());
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
//
//    @Test
//    public void testGetLabel() throws Exception {
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenReturn(new CpnLabelDO(){{setCode(12L);}});
//        CpnLabelDO label = queryQualityServicesInfoBusiness.getLabel(12L);
//        Assert.assertEquals("12", label.getCode().toString());
//    }
//
//    @Test
//    public void testGetLabel2() throws Exception {
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenThrow(new MockitoException("error"));
//        CpnLabelDO label = queryQualityServicesInfoBusiness.getLabel(12L);
//        Assert.assertNull(label);
//    }
//
//    @Test
//    public void testFindLabelListNew() throws Exception {
//        List<LabelDto> labelList = new ArrayList<>();
//        labelList.add(new LabelDto(){{setLabelId(1L);setIcon("11");setIconDesc("desc");}});
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenReturn(new CpnLabelDO(){{setCode(12L);setIsActive(true);}});
//        Mockito.when(newconfigs.getLabels()).thenReturn(labelList);
//        List<EasyLifeLabel> labelList1 = queryQualityServicesInfoBusiness.findLabelList(1);
//        Assert.assertEquals(1, labelList1.size());
//    }
//
//    @Test
//    public void testFindLabelListNew3() throws Exception {
//        List<LabelDto> labelList = new ArrayList<>();
//        labelList.add(new LabelDto(){{setLabelId(1L);setIcon("11");setIconDesc("desc");}});
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenReturn(null);
//        Mockito.when(newconfigs.getLabels()).thenReturn(labelList);
//        List<EasyLifeLabel> labelList1 = queryQualityServicesInfoBusiness.findLabelList(1);
//        Assert.assertEquals(0, labelList1.size());
//    }
//
//    @Test
//    public void testFindLabelListNew2() throws Exception {
//        List<LabelDto> labelList = new ArrayList<>();
//        labelList.add(new LabelDto(){{setLabelId(1L);setIcon("11");setIconDesc("desc");}});
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenReturn(new CpnLabelDO(){{setCode(12L);setIsActive(false);}});
//        Mockito.when(newconfigs.getLabels()).thenReturn(labelList);
//        List<EasyLifeLabel> labelList1 = queryQualityServicesInfoBusiness.findLabelList(1);
//        Assert.assertEquals(0, labelList1.size());
//    }
//
//    @Test
//    public void testFindLabelListOld() throws Exception {
//        List<LabelDto> labelList = new ArrayList<>();
//        List<ImgDTO> getDescImages = new ArrayList<>();
//        getDescImages.add(new ImgDTO());
//        labelList.add(new LabelDto(){{setLabelId(1L);setIcon("11");setIconDesc("desc");setDescImages(getDescImages);}});
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenReturn(new CpnLabelDO(){{setCode(12L);setIsActive(true);}});
//        Mockito.when(configs.getLabels()).thenReturn(labelList);
//        List<EasyLifeLabel> labelList1 = queryQualityServicesInfoBusiness.findLabelList(0);
//        Assert.assertEquals(1, labelList1.size());
//    }
//
//    @Test
//    public void testQueryLabelList() throws Exception {
//        List<LabelDto> labelList = new ArrayList<>();
//        List<ImgDTO> getDescImages = new ArrayList<>();
//        getDescImages.add(new ImgDTO());
//        labelList.add(new LabelDto(){{setLabelId(1L);setIcon("11");setIconDesc("desc");setDescImages(getDescImages);}});
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenReturn(new CpnLabelDO(){{setCode(12L);setIsActive(true);}});
//        Mockito.when(configs.getLabels()).thenReturn(labelList);
//        QueryLabelListResponseType responseType = queryQualityServicesInfoBusiness.queryLabelList(new QueryLabelListRequestType());
//        Assert.assertEquals(responseType.getResultCode().toString(), "200");
//    }
//
//    @Test
//    public void testQueryQualityServicesInfo() {
//        List<LabelDto> labelList = new ArrayList<>();
//        List<ImgDTO> getDescImages = new ArrayList<>();
//        getDescImages.add(new ImgDTO());
//        labelList.add(new LabelDto(){{setLabelId(1L);setIcon("11");setIconDesc("desc");setDescImages(getDescImages);}});
//        Mockito.when(labelCache.getLabel(Mockito.anyLong())).thenReturn(new CpnLabelDO(){{setCode(12L);setIsActive(true);}});
//        Mockito.when(configs.getLabels()).thenReturn(labelList);
//        QueryQualityServicesInfoResponseType responseType = queryQualityServicesInfoBusiness.queryQualityServicesInfo(new QueryQualityServicesInfoRequestType());
//        Assert.assertEquals(responseType.getResultCode().toString(), "200");
//    }
//
//
//}