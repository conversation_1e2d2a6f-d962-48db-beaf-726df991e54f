//package com.ctrip.car.market.coupon.restful.business;
//import com.ctrip.car.market.coupon.restful.contract.*;
//import com.ctrip.car.market.coupon.restful.qconfig.QConfigHelper;
//import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
//import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
//
//import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
//import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.Before;
//import org.junit.After;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.junit4.PowerMockRunnerDelegate;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
///**
// * CornerSignBusiness Tester.
// *
// * <AUTHOR> name>
// * @version 1.0
// * @since <pre>ʮ�� 24, 2020</pre>
// */
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//@PrepareForTest({SDMarketingServiceClient.class, BaseUtils.class, QConfigHelper.class})
//@SuppressStaticInitializationFor("com.ctrip.car.market.framework.DesensitizeUtil")//阻止静态代码块运行
//
//public class CornerSignBusinessTest {
//
//    @InjectMocks
//    CornerSignBusiness cornerSignBusiness;
//
//    @Mock
//    private CouponServiceQConfig couponServiceQConfig;
//
//    @Before
//    public void before() throws Exception {
//        MockitoAnnotations.initMocks(this);
//        PowerMockito.mockStatic(BaseUtils.class);
//        PowerMockito.mockStatic(QConfigHelper.class);
//        Mockito.when(QConfigHelper.getChineseTips(Mockito.anyString())).thenReturn("1");
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
//    /**
//     * Method: getCornerSign(GetCornerSignRequestType request)
//     */
//
//
//
//    /**
//     * Method: queryHomeIndexConfig(QueryHomeIndexConfigRequestType request)
//     */
//    @Test
//    public void testQueryHomeIndexConfig() throws Exception {
//        QueryHomeIndexConfigRequestType request = new QueryHomeIndexConfigRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setUnion(0);
//
//        QueryHomeIndexConfigResponseType responseType = new QueryHomeIndexConfigResponseType();
//        Mockito.when(couponServiceQConfig.getCtripHomeConfig()).thenReturn("asd");
//        Assert.assertNotNull(cornerSignBusiness.queryHomeIndexConfig(request));
//        request.setUnion(1);
//        Mockito.when(couponServiceQConfig.getCtripHomeConfig()).thenReturn("asd");
//        Assert.assertNotNull(cornerSignBusiness.queryHomeIndexConfig(request));
//    }
//
//
//}
