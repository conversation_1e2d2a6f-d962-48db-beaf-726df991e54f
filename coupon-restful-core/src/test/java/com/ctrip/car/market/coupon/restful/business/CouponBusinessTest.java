//package com.ctrip.car.market.coupon.restful.business;
//
//import com.ctrip.basebiz.accounts.mobile.request.filter.util.AccountsMobileRequestUtils;
//import com.ctrip.car.api8961.service.soa.gen.Api8961Client;
//import com.ctrip.car.api8961.service.soa.gen.OfferOrderOAuthCheckRequestType;
//import com.ctrip.car.api8961.service.soa.gen.OfferOrderOAuthCheckResponseType;
//import com.ctrip.car.market.client.contract.CollectCouponCodeRequestType;
//import com.ctrip.car.market.client.contract.CollectCouponCodeResponseType;
//import com.ctrip.car.market.coupon.restful.contract.QueryCouponRequestType;
//import com.ctrip.car.market.coupon.restful.contract.*;
//import com.ctrip.car.market.coupon.restful.contract.QueryCouponResponseType;
//import com.ctrip.car.market.coupon.restful.dto.CouponConfigDTO;
//import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
//import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.account.AccountInfoProxy;
//import com.ctrip.car.market.coupon.restful.qconfig.QConfigHelper;
//import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
//import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
//import com.ctrip.car.market.mergecouponservice.interfaces.common.ResponseResult;
//import com.ctrip.car.market.mergecouponservice.interfaces.service.CarMergeCouponServiceClient;
//import com.ctrip.car.market.mergecouponservice.interfaces.service.dto.*;
//import com.ctrip.car.market.mergecouponservice.interfaces.service.message.*;
//import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
//import com.ctrip.soa.platform.account.promocodeservice.v1.PromocodeServiceClient;
//import com.ctriposs.baiji.rpc.common.types.ResponseStatusType;
//import com.ctriposs.baiji.rpc.common.util.ServiceUtils;
//import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
//import com.google.common.collect.Lists;
//import credis.java.client.util.JsonUtil;
//import org.junit.After;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.junit4.PowerMockRunnerDelegate;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.lang.reflect.Method;
//import java.math.BigDecimal;
//import java.util.*;
//
//import static org.mockito.Matchers.any;
//
///**
// * CouponBusiness Tester.
// *
// * <AUTHOR> name>
// * @version 1.0
// * @since <pre>ʮ�� 24, 2020</pre>
// */
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//@PrepareForTest({CarMergeCouponServiceClient.class, SDMarketingServiceClient.class, BaseUtils.class, Api8961Client.class, JsonUtil.class, QConfigHelper.class})
//@SuppressStaticInitializationFor("com.ctrip.car.market.framework.DesensitizeUtil")//阻止静态代码块运行
//public class CouponBusinessTest {
//
//    @InjectMocks
//    private CouponBusiness couponBusiness;
//
//
//    @Mock
//    Api8961Client api8961Client;
//
//    @Mock
//    public AccountInfoProxy accountInfoProxy;
//
//
//    @Mock
//    private CouponServiceQConfig couponServiceQConfig;
//    @Mock
//    CarMergeCouponServiceClient carMergeCouponServiceClient;
//    @Mock
//    SDMarketingServiceClient client;
//    Calendar start = Calendar.getInstance();
//    Calendar end = Calendar.getInstance();
//
//
//
//
//    @Before
//    public void before() throws Exception {
//        start.add(Calendar.DAY_OF_YEAR, -2);
//        end.add(Calendar.DAY_OF_YEAR, 4);
//        MockitoAnnotations.initMocks(this);
//        carMergeCouponServiceClient = Mockito.mock(CarMergeCouponServiceClient.class);
//        ReflectionTestUtils.setField(couponBusiness, "carMergeCouponServiceClient", carMergeCouponServiceClient);
//        PowerMockito.mockStatic(BaseUtils.class);
//
//
//        PowerMockito.mockStatic(QConfigHelper.class);
//        Mockito.when(QConfigHelper.getChineseTips(Mockito.anyString())).thenReturn("1");
//
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
//
//    private com.ctrip.car.market.mergecouponservice.interfaces.service.message.QueryCouponResponseType initQueryCouponResponseType() {
//
//        com.ctrip.car.market.mergecouponservice.interfaces.service.message.QueryCouponResponseType responseType = new com.ctrip.car.market.mergecouponservice.interfaces.service.message.QueryCouponResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult());
//        com.ctrip.car.market.mergecouponservice.interfaces.service.dto.CouponDisplayDTO couponDisplayDTO = new com.ctrip.car.market.mergecouponservice.interfaces.service.dto.CouponDisplayDTO();
//        couponDisplayDTO.setCouponCode("12");
//        couponDisplayDTO.setName("12");
//        couponDisplayDTO.setStartDate(start);
//        couponDisplayDTO.setDisableDate(end);
//        couponDisplayDTO.setDeductionStrategy(Lists.newArrayList());
//        couponDisplayDTO.setPenaltyValue(new BigDecimal("0"));
//        couponDisplayDTO.setCouponStatus(1);
//        couponDisplayDTO.setDeductionStrategyTypeID(1);
//        couponDisplayDTO.setPromotionId(10);
//        couponDisplayDTO.setPromotionMethodID(1);
//        couponDisplayDTO.setICard("1");
//        couponDisplayDTO.setUseLongDescribtion("1");
//        couponDisplayDTO.setRedName("1");
//        couponDisplayDTO.setPromotionName("1");
//        couponDisplayDTO.setPriority(0);
//        couponDisplayDTO.setIsBestPromotionId(false);
//        couponDisplayDTO.setUpGradePromotionId(0);
//        couponDisplayDTO.setCurrencyEnumValue(com.ctrip.car.market.mergecouponservice.interfaces.service.dto.CurrencyEnumValueType.AED);
//        couponDisplayDTO.setUrlDec("1");
//        couponDisplayDTO.setAppUrl("1");
//        couponDisplayDTO.setH5Url("1");
//        couponDisplayDTO.setOnlineUrl("1");
//        couponDisplayDTO.setOverlayFlag(0);
//
//
//        responseType.setCouponList(Arrays.asList(couponDisplayDTO));
//        ExtraGroupInfoDTO extraGroupInfoDTO = new ExtraGroupInfoDTO();
//        extraGroupInfoDTO.setGroupId("1212");
//        extraGroupInfoDTO.setDisplayName("121");
//
//
//        responseType.setExtraGroupInfos(Arrays.asList(extraGroupInfoDTO));
//        return responseType;
//    }
//
//    /**
//     * Method: queryCoupon(QueryCouponRequestType request)
//     */
//    @Test
//    public void testQueryCouponTel() throws Exception {
//        QueryCouponRequestType request = new QueryCouponRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setGiftNo("1");
//        request.setGid("1");
//        request.setTel("11");
//        request.setPid(10);
//        request.setPromotionList(Arrays.asList(2334, 56655, 44545));
//        request.setPsize(10);
//        request.setPidx(1);
//        request.setIdCard("1212");
//        request.setSceneList(Arrays.asList(34, 65, 2));
//        request.setSource(0);
//        request.setProductCategory(34);
//        request.setProductPattern(0);
//        request.setUseStation("40");
//        request.setCstaus(0);
//        request.setSort(1);
//        request.setIswechat(false);
//        request.setCurrencyEnumValues(Arrays.asList(com.ctrip.car.market.coupon.restful.contract.CurrencyEnumValueType.CAD));
//        request.setLocal("zh_cn");
//        request.setTicket("23");
//        request.setUnionType(0);
//        request.setQgid("");
//
//
//
//        Mockito.when(carMergeCouponServiceClient.queryCoupon(any(com.ctrip.car.market.mergecouponservice.interfaces.service.message.QueryCouponRequestType.class))).thenReturn(initQueryCouponResponseType());
//
//
//        Assert.assertEquals(couponBusiness.queryCoupon(request).getResultCode().toString(), "-3");
//    }
//
//
//    @Test
//    public void testQueryCouponUid() throws Exception {
//        QueryCouponRequestType request = new QueryCouponRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setGiftNo("1");
//        request.setGid("1");
//        request.setTel("11");
//        request.setPid(10);
//        request.setPromotionList(Arrays.asList(2334, 56655, 44545));
//        request.setPsize(10);
//        request.setPidx(1);
//        request.setIdCard("1212");
//        request.setSceneList(Arrays.asList(34, 65, 2));
//        request.setSource(0);
//        request.setProductCategory(34);
//        request.setProductPattern(0);
//        request.setUseStation("40");
//        request.setCstaus(0);
//        request.setSort(1);
//        request.setIswechat(false);
//        request.setCurrencyEnumValues(Arrays.asList(com.ctrip.car.market.coupon.restful.contract.CurrencyEnumValueType.CAD));
//        request.setLocal("zh_cn");
//        request.setTicket("23");
//        request.setUnionType(0);
//        request.setQgid("");
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn("4545");
//
//
//        Mockito.when(carMergeCouponServiceClient.queryCoupon(any(com.ctrip.car.market.mergecouponservice.interfaces.service.message.QueryCouponRequestType.class))).thenReturn(initQueryCouponResponseType());
//
//        Assert.assertEquals(couponBusiness.queryCoupon(request).getResultCode().toString(), "-3");
//    }
//
//
//    @Test
//    public void testQueryCouponAccountUid() throws Exception {
//        QueryCouponRequestType request = new QueryCouponRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setGiftNo("1");
//        request.setGid("1");
//        request.setTel("11");
//        request.setPid(10);
//        request.setPromotionList(Arrays.asList(2334, 56655, 44545));
//        request.setPsize(10);
//        request.setPidx(1);
//        request.setIdCard("1212");
//        request.setSceneList(Arrays.asList(34, 65, 2));
//        request.setSource(0);
//        request.setProductCategory(34);
//        request.setProductPattern(0);
//        request.setUseStation("40");
//        request.setCstaus(0);
//        request.setSort(0);
//        request.setIswechat(false);
//        request.setCurrencyEnumValues(Arrays.asList(com.ctrip.car.market.coupon.restful.contract.CurrencyEnumValueType.CAD));
//        request.setLocal("zh_cn");
//        request.setTicket("23");
//        request.setUnionType(0);
//        request.setQgid("");
//        Mockito.when(accountInfoProxy.getAccountInfoByTicket(request.getHead(), ServiceUtils.getExtensionData(request, AccountsMobileRequestUtils.MobileAuthTokenExtensionKey), request.getTicket())).thenReturn("4545");
//        Mockito.when(carMergeCouponServiceClient.queryCoupon(any(com.ctrip.car.market.mergecouponservice.interfaces.service.message.QueryCouponRequestType.class))).thenReturn(initQueryCouponResponseType());
//
//        Assert.assertEquals(couponBusiness.queryCoupon(request).getResultCode().toString(), "-3");
//    }
//
//
//    @Test
//    public void testQueryCouponQunar() throws Exception {
//        QueryCouponRequestType request = new QueryCouponRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setGiftNo("1");
//        request.setGid("1");
//        request.setTel("11");
//        request.setPid(10);
//        request.setPromotionList(Arrays.asList(2334, 56655, 44545));
//        request.setPsize(10);
//        request.setPidx(1);
//        request.setIdCard("1212");
//        request.setSceneList(Arrays.asList(34, 65, 2));
//        request.setSource(0);
//        request.setProductCategory(34);
//        request.setProductPattern(0);
//        request.setUseStation("40");
//        request.setCstaus(0);
//        request.setSort(0);
//        request.setIswechat(false);
//        request.setCurrencyEnumValues(Arrays.asList(com.ctrip.car.market.coupon.restful.contract.CurrencyEnumValueType.CAD));
//        request.setLocal("zh_cn");
//        request.setTicket("23");
//        request.setUnionType(0);
//        request.setQgid("");
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn("3434");
//        Mockito.when(carMergeCouponServiceClient.queryCoupon(any(com.ctrip.car.market.mergecouponservice.interfaces.service.message.QueryCouponRequestType.class))).thenReturn(initQueryCouponResponseType());
//
//        Assert.assertEquals(couponBusiness.queryCoupon(request).getResultCode().toString(), "-3");
//    }
//
//    @Test
//    public void testQueryCoupon() throws Exception {
//        QueryCouponRequestType request = new QueryCouponRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setGiftNo("1");
//        request.setGid("1");
//        request.setTel("11");
//        request.setPid(10);
//        request.setPromotionList(Arrays.asList(2334, 56655, 44545));
//        request.setPsize(10);
//        request.setPidx(1);
//        request.setIdCard("1212");
//        request.setSceneList(Arrays.asList(34, 65, 2));
//        request.setSource(0);
//        request.setProductCategory(34);
//        request.setProductPattern(0);
//        request.setUseStation("40");
//        request.setCstaus(0);
//        request.setSort(0);
//        request.setIswechat(false);
//        request.setCurrencyEnumValues(Arrays.asList(com.ctrip.car.market.coupon.restful.contract.CurrencyEnumValueType.AUD));
//        request.setLocal("zh_cn");
//        request.setTicket("23");
//        request.setUnionType(0);
//        request.setQgid("");
//
//        Assert.assertEquals(couponBusiness.queryCoupon(request).getResultCode().toString(), "-3");
//    }
//
//
//    /**
//     * Method: receiveCouponsByGroupID(ReceiveCouponsByGroupIDRequestType request)
//     */
//    @Test
//    public void testReceiveCouponsByGroupID() throws Exception {
//        ReceiveCouponsByGroupIDRequestType request = new ReceiveCouponsByGroupIDRequestType();
//        request.setHead(new MobileRequestHead() {{
//            this.cid = "23";
//        }});
//        request.setReqhead(new RestRequestHeader());
//        request.setCityID(0);
//        request.setGid("1");
//        request.setChl(0);
//        request.setClientChannel(0);
//        request.setSn("1");
//        request.setCip("1");
//        request.setOrderID(0L);
//        request.setTel("11");
//        request.setIntlCode("1");
//        request.setIcard("1");
//        request.setUname("1");
//        request.setScene(1);
//        request.setAsync(false);
//        request.setWeChatOpenid("1");
//        request.setDistributid("1");
//        request.setLocale("");
//        request.setIschat(false);
//        request.setTicket("1");
//        request.setUnionType(0);
//        request.setQgid("1");
//
//        SendCouponsByGroupIdResponseType responseType = new SendCouponsByGroupIdResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult() {{
//            this.setReturnCode("200");
//        }});
//        CouponItemDTO couponItemDTO = new CouponItemDTO();
//        couponItemDTO.setCouponID(1110L);
//        couponItemDTO.setCouponCode("2323");
//        couponItemDTO.setPromotionId(10);
//        couponItemDTO.setRedPageName("232");
//        couponItemDTO.setEndDate(start);
//        couponItemDTO.setStartDate(end);
//
//
//        responseType.setCouponItems(Arrays.asList(couponItemDTO));
//        responseType.setUrl("");
//        responseType.setAppUrl("");
//
//
//        Mockito.when(carMergeCouponServiceClient.sendCouponsByGroupId(any(SendCouponsByGroupIdRequestType.class))).thenReturn(responseType);
//
//        Assert.assertEquals(couponBusiness.receiveCouponsByGroupID(request).getResultCode().toString(), "-2");
//    }
//
//
//    @Test
//    public void testReceiveCouponsByGroupIDQunar() throws Exception {
//        ReceiveCouponsByGroupIDRequestType request = new ReceiveCouponsByGroupIDRequestType();
//        request.setHead(new MobileRequestHead() {{
//            this.cid = "23";
//        }});
//        request.setReqhead(new RestRequestHeader());
//        request.setCityID(0);
//        request.setGid("1");
//        request.setChl(0);
//        request.setClientChannel(0);
//        request.setSn("1");
//        request.setCip("1");
//        request.setOrderID(0L);
//        request.setTel("11");
//        request.setIntlCode("1");
//        request.setIcard("1");
//        request.setUname("1");
//        request.setScene(1);
//        request.setAsync(false);
//        request.setWeChatOpenid("1");
//        request.setDistributid("1");
//        request.setLocale("");
//        request.setIschat(false);
//        request.setTicket("1");
//        request.setUnionType(1);
//        request.setQgid("1");
//
//        SendCouponsByGroupIdResponseType responseType = new SendCouponsByGroupIdResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult() {{
//            this.setReturnCode("200");
//        }});
//        CouponItemDTO couponItemDTO = new CouponItemDTO();
//        couponItemDTO.setCouponID(1110L);
//        couponItemDTO.setCouponCode("2323");
//        couponItemDTO.setPromotionId(10);
//        couponItemDTO.setRedPageName("232");
//        couponItemDTO.setEndDate(start);
//        couponItemDTO.setStartDate(end);
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn("3434");
//
//
//        responseType.setCouponItems(Arrays.asList(couponItemDTO));
//        responseType.setUrl("");
//        responseType.setAppUrl("");
//
//
//        Mockito.when(carMergeCouponServiceClient.sendCouponsByGroupId(any(SendCouponsByGroupIdRequestType.class))).thenReturn(responseType);
//
//        Assert.assertEquals(couponBusiness.receiveCouponsByGroupID(request).getResultCode().toString(), "200");
//    }
//
//    @Test
//    public void testReceiveCouponsByGroupIDQunar3() throws Exception {
//        ReceiveCouponsByGroupIDRequestType request = new ReceiveCouponsByGroupIDRequestType();
//        request.setHead(new MobileRequestHead() {{
//            this.cid = "23";
//        }});
//        request.setReqhead(new RestRequestHeader());
//        request.setCityID(0);
//        request.setGid("1");
//        request.setChl(0);
//        request.setClientChannel(0);
//        request.setSn("1");
//        request.setCip("1");
//        request.setOrderID(0L);
//        request.setTel("11");
//        request.setIntlCode("1");
//        request.setIcard("1");
//        request.setUname("1");
//        request.setScene(1);
//        request.setAsync(false);
//        request.setWeChatOpenid("1");
//        request.setDistributid("1");
//        request.setLocale("");
//        request.setIschat(false);
//        request.setTicket("1");
//        request.setUnionType(0);
//        request.setQgid("1");
//
//        SendCouponsByGroupIdResponseType responseType = new SendCouponsByGroupIdResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult() {{
//            this.setReturnCode("200");
//        }});
//        CouponItemDTO couponItemDTO = new CouponItemDTO();
//        couponItemDTO.setCouponID(1110L);
//        couponItemDTO.setCouponCode("2323");
//        couponItemDTO.setPromotionId(10);
//        couponItemDTO.setRedPageName("232");
//        couponItemDTO.setEndDate(start);
//        couponItemDTO.setStartDate(end);
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn("");
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn(null);
//
//
//        responseType.setCouponItems(Arrays.asList(couponItemDTO));
//        responseType.setUrl("");
//        responseType.setAppUrl("");
//
//
//        Mockito.when(carMergeCouponServiceClient.sendCouponsByGroupId(any(SendCouponsByGroupIdRequestType.class))).thenReturn(responseType);
//
//        Assert.assertEquals(couponBusiness.receiveCouponsByGroupID(request).getResultCode().toString(), "-2");
//    }
//
//
//    @Test
//    public void testCollectCouponCodeByUid() throws Exception {
//        CollectCouponCodeByUidRequestType request = new CollectCouponCodeByUidRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setClientId("2323");
//        request.setCouponCode("3434");
//        request.setUnionType(0);
//        request.setUseStation("70");
//
//        Assert.assertEquals(couponBusiness.collectCouponCodeByUid(request).getResultCode().toString(), "-2");
//    }
//
//
//    @Test
//    public void testCollectCouponCodeByUid2() throws Exception {
//        CollectCouponCodeByUidRequestType request = new CollectCouponCodeByUidRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setClientId("2323");
//        request.setCouponCode("3434");
//        request.setUnionType(0);
//        request.setUseStation("70");
//        String uid = "dddd";
//
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn("2323");
//        CollectCouponCodeResponseType responseType = new CollectCouponCodeResponseType();
//        responseType.setCode(0);
//        responseType.setMessage("success");
//        OfferOrderOAuthCheckResponseType restOAuthCheckResponseType = new OfferOrderOAuthCheckResponseType();
//        Mockito.when(api8961Client.offerOrderOAuthCheck(any(OfferOrderOAuthCheckRequestType.class))).thenReturn(restOAuthCheckResponseType);
//        Mockito.when(client.collectCouponCode(any(CollectCouponCodeRequestType.class))).thenReturn(responseType);
//        Assert.assertEquals(couponBusiness.collectCouponCodeByUid(request).getResultCode().toString(), "0");
//    }
//
//
//    @Test
//    public void testCollectCouponCodeByUid3() throws Exception {
//        CollectCouponCodeByUidRequestType request = new CollectCouponCodeByUidRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setClientId("2323");
//        request.setCouponCode("3434");
//        request.setUnionType(0);
//        request.setUseStation("70");
//        String uid = "dddd";
//
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn(null);
//        Mockito.when(accountInfoProxy.getAccountInfoByTicket(any(), Mockito.anyString(), Mockito.anyString())).thenReturn(uid);
//        OfferOrderOAuthCheckResponseType restOAuthCheckResponseType = new OfferOrderOAuthCheckResponseType();
//        Mockito.when(api8961Client.offerOrderOAuthCheck(any(OfferOrderOAuthCheckRequestType.class))).thenReturn(restOAuthCheckResponseType);
//        CollectCouponCodeResponseType responseType = new CollectCouponCodeResponseType();
//        responseType.setCode(0);
//        responseType.setMessage("success");
//        Mockito.when(client.collectCouponCode(any(CollectCouponCodeRequestType.class))).thenReturn(responseType);
//        Assert.assertEquals(couponBusiness.collectCouponCodeByUid(request).getResultCode().toString(), "0");
//    }
//
//    @Test
//    public void testCollectCouponCodeByUid31() throws Exception {
//        CollectCouponCodeByUidRequestType request = new CollectCouponCodeByUidRequestType();
//        request.setHead(new MobileRequestHead());
//
//        request.setReqhead(new RestRequestHeader());
//        request.setClientId("2323");
//        request.setCouponCode("3434");
//        request.setUnionType(1);
//        request.setUseStation("70");
//        String uid = "dddd";
//        OfferOrderOAuthCheckResponseType restOAuthCheckResponseType = new OfferOrderOAuthCheckResponseType();
//        Mockito.when(api8961Client.offerOrderOAuthCheck(any(OfferOrderOAuthCheckRequestType.class))).thenReturn(restOAuthCheckResponseType);
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn(null);
//        Mockito.when(accountInfoProxy.getAccountInfoByTicket(any(), Mockito.anyString(), Mockito.anyString())).thenReturn(null);
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn(uid);
//
//        CollectCouponCodeResponseType responseType = new CollectCouponCodeResponseType();
//        responseType.setCode(0);
//        responseType.setMessage("success");
//        Mockito.when(client.collectCouponCode(any(CollectCouponCodeRequestType.class))).thenReturn(responseType);
//        Assert.assertEquals(couponBusiness.collectCouponCodeByUid(request).getResultCode().toString(), "0");
//    }
//
//    @Test
//    public void testCollectCouponCodeByUid4() throws Exception {
//        CollectCouponCodeByUidRequestType request = new CollectCouponCodeByUidRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setClientId("2323");
//        request.setCouponCode("3434");
//        request.setUnionType(0);
//        request.setUseStation("70");
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn("3434");
//        String uid = "dddd";
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn("2323");
//        Mockito.when(accountInfoProxy.getAccountInfoByTicket(any(), Mockito.anyString(), Mockito.anyString())).thenReturn("2323");
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn(uid);
//        OfferOrderOAuthCheckResponseType restOAuthCheckResponseType = new OfferOrderOAuthCheckResponseType();
//        Mockito.when(api8961Client.offerOrderOAuthCheck(any(OfferOrderOAuthCheckRequestType.class))).thenReturn(restOAuthCheckResponseType);
//        CollectCouponCodeResponseType responseType = new CollectCouponCodeResponseType();
//        responseType.setCode(0);
//        responseType.setMessage("success");
//        Mockito.when(client.collectCouponCode(any(CollectCouponCodeRequestType.class))).thenReturn(null);
//        Assert.assertEquals(couponBusiness.collectCouponCodeByUid(request).getResultCode().toString(), "200");
//    }
//
//    @Test
//    public void testCollectCouponCodeByUid5() throws Exception {
//        CollectCouponCodeByUidRequestType request = new CollectCouponCodeByUidRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setClientId("2323");
//        request.setUnionType(0);
//        request.setUseStation("70");
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn("3434");
//        String uid = "dddd";
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn("2323");
//        Mockito.when(accountInfoProxy.getAccountInfoByTicket(any(), Mockito.anyString(), Mockito.anyString())).thenReturn("2323");
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn(uid);
//        CollectCouponCodeResponseType responseType = new CollectCouponCodeResponseType();
//        responseType.setCode(0);
//        responseType.setMessage("success");
//        OfferOrderOAuthCheckResponseType restOAuthCheckResponseType = new OfferOrderOAuthCheckResponseType();
//        Mockito.when(api8961Client.offerOrderOAuthCheck(any(OfferOrderOAuthCheckRequestType.class))).thenReturn(restOAuthCheckResponseType);
//        Mockito.when(client.collectCouponCode(any(CollectCouponCodeRequestType.class))).thenReturn(null);
//        Assert.assertEquals(couponBusiness.collectCouponCodeByUid(request).getResultCode().toString(), "-1");
//    }
//
//    @Test
//    public void testCollectCouponCodeByUid6() throws Exception {
//        CollectCouponCodeByUidRequestType request = new CollectCouponCodeByUidRequestType();
//        request.setHead(new MobileRequestHead());
//        request.setReqhead(new RestRequestHeader());
//        request.setClientId("2323");
//        request.setUnionType(0);
//        request.setUseStation("70");
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn("3434");
//        String uid = "dddd";
//        Mockito.when(BaseUtils.getUidByCommon(request.getHead())).thenReturn("2323");
//        Mockito.when(accountInfoProxy.getAccountInfoByTicket(any(), Mockito.anyString(), Mockito.anyString())).thenReturn("2323");
//        Mockito.when(BaseUtils.getQunarUidByCommon(request.getReqhead())).thenReturn(uid);
//        OfferOrderOAuthCheckResponseType restOAuthCheckResponseType = new OfferOrderOAuthCheckResponseType();
//        Mockito.when(api8961Client.offerOrderOAuthCheck(any(OfferOrderOAuthCheckRequestType.class))).thenReturn(restOAuthCheckResponseType);
//        CollectCouponCodeResponseType responseType = new CollectCouponCodeResponseType();
//        responseType.setCode(0);
//        responseType.setMessage("success");
//        Mockito.when(client.collectCouponCode(any(CollectCouponCodeRequestType.class))).thenReturn(null);
//        Assert.assertEquals(couponBusiness.collectCouponCodeByUid(request).getResultCode().toString(), "-1");
//    }
//
//
//    @Test
//    public void testCheckCouponStrategy() throws Exception {
//        List<CouponConfigDTO> couponConfigs = new ArrayList<>();
//        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();
//        couponConfigDTO.setSendStatus(1);
//        couponConfigDTO.setReceiveCouponStatus(1);
//        couponConfigs.add(couponConfigDTO);
//        Mockito.when(couponServiceQConfig.getCouponConfigs()).thenReturn(couponConfigs);
//
//        Object[] testMethodArgs = {1};
//        Method method = CouponBusiness.class.getDeclaredMethod("checkCouponStrategy", int.class);
//        method.setAccessible(true);
//        CouponConfigDTO result = (CouponConfigDTO) method.invoke(couponBusiness, testMethodArgs);
//        Assert.assertEquals(result.getReceiveCouponStatus().toString(), "1");
//    }
//
//    @Test
//    public void testCheckCouponStrategy2() throws Exception {
//        List<CouponConfigDTO> couponConfigs = new ArrayList<>();
//        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();
//        couponConfigDTO.setSendStatus(1);
//        couponConfigDTO.setReceiveCouponStatus(1);
//        couponConfigs.add(couponConfigDTO);
//        Mockito.when(couponServiceQConfig.getCouponConfigs()).thenReturn(null);
//
//        Object[] testMethodArgs = {1};
//        Method method = CouponBusiness.class.getDeclaredMethod("checkCouponStrategy", int.class);
//        method.setAccessible(true);
//        CouponConfigDTO result = (CouponConfigDTO) method.invoke(couponBusiness, testMethodArgs);
//        Assert.assertTrue(result == null);
//    }
//
//
//    @Test
//    public void testQueryCouponByConfig() throws Exception {
//        QueryCouponByConfigRequestType requestType = new QueryCouponByConfigRequestType();
//        List<CouponConfigDTO> couponConfigs = new ArrayList<>();
//        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();
//        couponConfigDTO.setSendStatus(1);
//        couponConfigDTO.setReceiveCouponStatus(0);
//        couponConfigs.add(couponConfigDTO);
//        Mockito.when(couponServiceQConfig.getCouponConfigs()).thenReturn(couponConfigs);
//        requestType.setHead(new MobileRequestHead());
//        QueryCouponByConfigResponseType responseType = couponBusiness.queryCouponByConfig(requestType);
//
//        Assert.assertEquals(responseType.getResultCode().toString(), "200");
//    }
//
//    @Test
//    public void testcOMInitRCByGroupID() throws Exception {
//
//        COMInitRCByGroupIDResponseType responseType = couponBusiness.cOMInitRCByGroupID(new COMInitRCByGroupIDRequestType());
//
//        Assert.assertEquals(responseType.getResultcode().toString(), "3");
//    }
//
//    @Test
//    public void testreceiveImposingCoupon() throws Exception {
//
//        ReceiveImposingCouponResponseType responseType = couponBusiness.receiveImposingCoupon(new ReceiveImposingCouponRequestType());
//
//        Assert.assertEquals(responseType.getRencode(), "-1");
//    }
//
//
//    @Test
//    public void testqueryCoupon() throws Exception {
//
//        QueryCouponResponseType responseType = couponBusiness.queryCoupon(new QueryCouponRequestType());
//
//        Assert.assertEquals(responseType.getResultCode().intValue(), ResultOpenStatus.TEL_NOINFO.getCode());
//    }
//
//    @Test
//    public void testQueryCouponByConfig2() throws Exception {
//        QueryCouponByConfigRequestType requestType = new QueryCouponByConfigRequestType();
//        List<CouponConfigDTO> couponConfigs = new ArrayList<>();
//        CouponConfigDTO couponConfigDTO = new CouponConfigDTO();
//        couponConfigDTO.setSendStatus(1);
//        couponConfigDTO.setReceiveCouponStatus(0);
//        couponConfigs.add(couponConfigDTO);
//        Mockito.when(couponServiceQConfig.getCouponConfigs()).thenReturn(couponConfigs);
//        Mockito.when(couponServiceQConfig.getValueByKeyFromQconfig("inflow.section")).thenReturn("1212");
//        requestType.setHead(new MobileRequestHead());
//        QueryCouponByConfigResponseType responseType = couponBusiness.queryCouponByConfig(requestType);
//
//        Assert.assertEquals(responseType.getResultCode().toString(), "200");
//    }
//
//
//    @Test
//    public void testconvertTOSendCouponsByGroupIdRequestType() throws Exception {
//        ReceiveCouponsByGroupIDRequestType requestType = new ReceiveCouponsByGroupIDRequestType();
//        requestType.setCityID(11);
//        requestType.setQgid("1212");
//        requestType.setGid("23");
//        requestType.setDistributid("111");
//        requestType.setUnionType(1);
//        Object[] testMethodArgs = {requestType, "1"};
//        Method method = CouponBusiness.class.getDeclaredMethod("convertTOSendCouponsByGroupIdRequestType", ReceiveCouponsByGroupIDRequestType.class, String.class);
//        method.setAccessible(true);
//        SendCouponsByGroupIdRequestType result = (SendCouponsByGroupIdRequestType) method.invoke(couponBusiness, testMethodArgs);
//        Assert.assertEquals(result.getCityID().toString(), "11");
//    }
//
//    @Test
//    public void testconvertTOSendCouponsByGroupIdRequestType2() throws Exception {
//        ReceiveCouponsByGroupIDRequestType requestType = new ReceiveCouponsByGroupIDRequestType();
//        requestType.setCityID(11);
//        requestType.setQgid("1212");
//        requestType.setGid("23");
//        requestType.setChl(121);
//        requestType.setSn("34");
//        requestType.setDistributid("111");
//        Object[] testMethodArgs = {requestType, "1"};
//        Method method = CouponBusiness.class.getDeclaredMethod("convertTOSendCouponsByGroupIdRequestType", ReceiveCouponsByGroupIDRequestType.class, String.class);
//        method.setAccessible(true);
//        SendCouponsByGroupIdRequestType result = (SendCouponsByGroupIdRequestType) method.invoke(couponBusiness, testMethodArgs);
//        Assert.assertEquals(result.getCityID().toString(), "11");
//    }
//
//    @Test
//    public void testconvertTOSendCouponsByGroupIdRequestType3() throws Exception {
//        ReceiveCouponsByGroupIDRequestType requestType = new ReceiveCouponsByGroupIDRequestType();
//        requestType.setCityID(11);
//        requestType.setQgid("1212");
//        requestType.setGid("23");
//        requestType.setChl(121);
//        requestType.setSn("34");
//        requestType.setUnionType(0);
//        requestType.setDistributid("111");
//        Object[] testMethodArgs = {requestType, "1"};
//        Method method = CouponBusiness.class.getDeclaredMethod("convertTOSendCouponsByGroupIdRequestType", ReceiveCouponsByGroupIDRequestType.class, String.class);
//        method.setAccessible(true);
//        SendCouponsByGroupIdRequestType result = (SendCouponsByGroupIdRequestType) method.invoke(couponBusiness, testMethodArgs);
//        Assert.assertEquals(result.getCityID().toString(), "11");
//    }
//}
