//package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon;
//import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
//import com.ctriposs.baiji.rpc.common.types.ResponseStatusType;
//import com.ctrip.car.market.client.contract.SkinInfo;
//
//import com.ctrip.car.market.client.contract.QuerySkinsResponseType;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.Before;
//import org.junit.After;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.junit4.PowerMockRunnerDelegate;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//@SuppressStaticInitializationFor("com.ctrip.car.market.framework.DesensitizeUtil")
//public class SDMarketingServiceProxyTest {
//
//    @InjectMocks
//    private SDMarketingServiceProxy sdMarketingServiceProxy;
//
//    @Before
//    public void before() throws Exception {
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
//
//
//    /**
//     * Method: getSkins(String sourceFrom)
//     */
//    @Test
//    public void testGetSkins() throws Exception {
//        QuerySkinsResponseType responseType = new QuerySkinsResponseType();
//
//        SDMarketingServiceClient mock = PowerMockito.mock(SDMarketingServiceClient.class);
//        PowerMockito.when(mock.querySkins(Mockito.any())).thenReturn(responseType);
//
//        Assert.assertNull(sdMarketingServiceProxy.getSkins(""));
//
//    }
//
//
//}
