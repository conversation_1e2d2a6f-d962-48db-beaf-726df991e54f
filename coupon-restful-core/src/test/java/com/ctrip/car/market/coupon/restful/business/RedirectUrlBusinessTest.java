//package com.ctrip.car.market.coupon.restful.business;
//
//import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
//import com.ctrip.car.market.mergecouponservice.interfaces.common.ResponseResult;
//import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
//import com.ctriposs.baiji.rpc.common.types.ResponseStatusType;
//
//import com.google.common.collect.Lists;
//
//import com.ctrip.car.market.mergecouponservice.interfaces.common.RequestHeader;
//import com.ctrip.car.market.coupon.restful.contract.GetJumpUrlByPromotionIdRequestType;
//import com.ctrip.car.market.coupon.restful.contract.GetJumpUrlByPromotionIdResponseType;
//import com.ctrip.car.market.coupon.restful.dto.UrlInfoDTO;
//import com.ctrip.car.market.mergecouponservice.interfaces.service.message.GetRedirectUrlByPromotionIDRequestType;
//import com.ctrip.car.market.mergecouponservice.interfaces.service.message.GetRedirectUrlByPromotionIDResponseType;
//import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
//
//import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.PromocodeServiceProxy;
//import com.ctrip.car.market.mergecouponservice.interfaces.service.CarMergeCouponServiceClient;
//import org.junit.After;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.junit4.PowerMockRunnerDelegate;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.util.Arrays;
//
//import static org.junit.Assert.*;
//
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//@PrepareForTest({SDMarketingServiceClient.class, CarMergeCouponServiceClient.class,BaseUtils.class})
//@SuppressStaticInitializationFor("com.ctrip.car.market.framework.DesensitizeUtil")//阻止静态代码块运行
//
//public class RedirectUrlBusinessTest {
//    @InjectMocks
//    private RedirectUrlBusiness redirectUrlBusiness;
//
//
//    @Mock
//    PromocodeServiceProxy promocodeServiceProxy;
//    CarMergeCouponServiceClient carMergeCouponServiceClient;
//
//
//    @Before
//    public void before() throws Exception {
//        MockitoAnnotations.initMocks(this);
//        PowerMockito.mockStatic(BaseUtils.class);
//
//        carMergeCouponServiceClient = Mockito.mock(CarMergeCouponServiceClient.class);
//        ReflectionTestUtils.setField(redirectUrlBusiness, "carMergeCouponServiceClient", carMergeCouponServiceClient);
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
//
//    @Test
//    public void getJumpUrlByPromotionId() throws Exception {
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("");
//        request.setUnionType("00");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("");
//        requestType.setUnionType("0");
//
//
//        GetJumpUrlByPromotionIdResponseType result = new GetJumpUrlByPromotionIdResponseType();
//        UrlInfoDTO urlInfoDTO = new UrlInfoDTO();
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(urlInfoDTO);
//        Assert.assertNotNull(redirectUrlBusiness.getJumpUrlByPromotionId(request));
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        Assert.assertTrue(redirectUrlBusiness.getJumpUrlByPromotionId(request).getRnUrl()==null);
//
//
//    }
//
//    @Test
//    public void getJumpUrlByPromotionId2() throws Exception {
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("0");
//        request.setUnionType("0");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("0");
//        requestType.setUnionType("0");
//
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult());
//        responseType.setAppUrl("");
//        responseType.setH5Url("");
//        responseType.setOnlineUrl("");
//        responseType.setUrlDec("");
//        responseType.setWeChatUrl("");
//        responseType.setWeChatId("");
//        responseType.setRnUrl("111");
//        responseType.setBaiDuUrl("");
//        responseType.setBaiDuID("");
//        responseType.setAliPayUrl("");
//        responseType.setAliPayID("");
//
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(Mockito.any(GetRedirectUrlByPromotionIDRequestType.class))).thenReturn(responseType);
//        Assert.assertEquals(redirectUrlBusiness.getJumpUrlByPromotionId(request).getRnUrl(), "111");
//    }
//
//
//    @Test
//    public void getJumpUrlByPromotionId3() throws Exception {
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("0");
//        request.setUnionType("0");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("0");
//        requestType.setUnionType("0");
//
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult());
//        responseType.setAppUrl("111");
//        responseType.setH5Url("");
//        responseType.setOnlineUrl("");
//        responseType.setUrlDec("");
//        responseType.setWeChatUrl("");
//        responseType.setWeChatId("");
//        responseType.setBaiDuUrl("");
//        responseType.setBaiDuID("");
//        responseType.setAliPayUrl("");
//        responseType.setAliPayID("");
//
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(Mockito.any(GetRedirectUrlByPromotionIDRequestType.class))).thenReturn(responseType);
//        Assert.assertEquals(redirectUrlBusiness.getJumpUrlByPromotionId(request).getAPPUrl(), "111");
//    }
//
//    @Test
//    public void getJumpUrlByPromotionId4() throws Exception {
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("0");
//        request.setUnionType("0");
//        request.setHead(new MobileRequestHead());
//
//        GetJumpUrlByPromotionIdRequestType request1 = new GetJumpUrlByPromotionIdRequestType();
//        request.setGid("0");
//        request.setUnionType("0");
//        request.setHead(new MobileRequestHead());
//
//        GetJumpUrlByPromotionIdRequestType request2 = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(-1L);
//        request.setGid("0");
//        request.setUnionType("0");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("0");
//        requestType.setUnionType("0");
//
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult());
//        responseType.setAppUrl("111");
//        responseType.setH5Url("");
//        responseType.setOnlineUrl("");
//        responseType.setUrlDec("");
//        responseType.setWeChatUrl("");
//        responseType.setWeChatId("");
//        responseType.setBaiDuUrl("");
//        responseType.setBaiDuID("");
//        responseType.setAliPayUrl("");
//        responseType.setAliPayID("");
//        responseType.setRnUrl(null);
//
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(Mockito.any(GetRedirectUrlByPromotionIDRequestType.class))).thenReturn(responseType);
//        Assert.assertNotNull(redirectUrlBusiness.getJumpUrlByPromotionId(request));
//        Assert.assertNotNull(redirectUrlBusiness.getJumpUrlByPromotionId(request1));
//        Assert.assertNotNull(redirectUrlBusiness.getJumpUrlByPromotionId(request2));
//    }
//
//
//    @Test
//    public void getJumpUrlByPromotionIdForException() throws Exception {
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("0");
//        request.setUnionType("0");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("0");
//        requestType.setUnionType("0");
//
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        responseType.setResponseStatus(new ResponseStatusType());
//        responseType.setResponseResult(new ResponseResult());
//        responseType.setAppUrl("111");
//        responseType.setH5Url("");
//        responseType.setOnlineUrl("");
//        responseType.setUrlDec("");
//        responseType.setWeChatUrl("");
//        responseType.setWeChatId("");
//        responseType.setBaiDuUrl("");
//        responseType.setBaiDuID("");
//        responseType.setAliPayUrl("");
//        responseType.setAliPayID("");
//
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(Mockito.any(GetRedirectUrlByPromotionIDRequestType.class))).thenThrow(new Exception());
//        Assert.assertNull(redirectUrlBusiness.getJumpUrlByPromotionId(request).getAPPUrl());
//    }
//
//
//    @Test
//    public void getJumpUrl() throws Exception {
//        UrlInfoDTO urlInfoDTO = new UrlInfoDTO();
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(urlInfoDTO);
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("");
//        request.setUnionType("00");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("");
//        requestType.setUnionType("0");
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(requestType)).thenReturn(responseType);
//        Assert.assertEquals(redirectUrlBusiness.getJumpUrlByPromotionId(request).getAPPUrl(), responseType.getAppUrl());
//
//    }
//
//    @Test
//    public void getJumpUrl2() throws Exception {
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("00");
//        request.setUnionType("00");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("00");
//        requestType.setUnionType("0");
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        responseType.setAppUrl("111");
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(Mockito.any(GetRedirectUrlByPromotionIDRequestType.class))).thenReturn(responseType);
//        Assert.assertEquals(redirectUrlBusiness.getJumpUrlByPromotionId(request).getAPPUrl(), "111");
//
//    }
//
//
//    @Test
//    public void getJumpUrlByExpection() throws Exception {
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("00");
//        request.setUnionType("00");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("00");
//        requestType.setUnionType("0");
//        GetRedirectUrlByPromotionIDResponseType responseType = new GetRedirectUrlByPromotionIDResponseType();
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(Mockito.any(GetRedirectUrlByPromotionIDRequestType.class))).thenThrow(new Exception());
//        Assert.assertTrue(redirectUrlBusiness.getJumpUrlByPromotionId(request).getRnUrl()==null);
//
//    }
//
//    @Test
//    public void getJumpUrlByExpection2() throws Exception {
//        Mockito.when(promocodeServiceProxy.getPromotionStrategy(111)).thenReturn(null);
//        GetJumpUrlByPromotionIdRequestType request = new GetJumpUrlByPromotionIdRequestType();
//        request.setPid(111L);
//        request.setGid("00");
//        request.setUnionType("00");
//        request.setHead(new MobileRequestHead());
//
//        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
//        requestType.setRequestHeader(new RequestHeader());
//        requestType.setPromotionID(111);
//        requestType.setGroupID(0L);
//        requestType.setEncryptedGroupId("00");
//        requestType.setUnionType("0");
//        Mockito.when(carMergeCouponServiceClient.getRedirectUrlByPromotionID(Mockito.any(GetRedirectUrlByPromotionIDRequestType.class))).thenReturn(null);
//        Assert.assertTrue(redirectUrlBusiness.getJumpUrlByPromotionId(request).getRnUrl()==null);
//
//    }
//
//}
