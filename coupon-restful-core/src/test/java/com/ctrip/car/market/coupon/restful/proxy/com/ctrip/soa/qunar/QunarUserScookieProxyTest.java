//package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.qunar;
//
//import com.ctrip.car.market.coupon.restful.utils.QConfigUtil;
//import com.ctrip.framework.clogging.agent.log.ILog;
//import okhttp3.*;
//import org.apache.commons.lang3.StringUtils;
//import org.junit.BeforeClass;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.api.support.membermodification.MemberMatcher;
//import org.powermock.api.support.membermodification.MemberModifier;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.junit4.PowerMockRunnerDelegate;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//import java.lang.reflect.Field;
//import java.util.Map;
//import java.util.Objects;
//
//import static org.junit.Assert.*;
//import static org.mockito.Mockito.*;
//
//@RunWith(PowerMockRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//@PrepareForTest({ILog.class, QConfigUtil.class, OkHttpClient.class, Response.class, ResponseBody.class})
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//public class QunarUserScookieProxyTest {
//
//    @BeforeClass
//    public static void setUp() {
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "error", String.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "error", String.class, String.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "error", String.class, Throwable.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "error", String.class, String.class, Map.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "warn", String.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "warn", String.class, String.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "warn", String.class, Throwable.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "warn", String.class, String.class, Map.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "info", String.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "info", String.class, String.class)).toReturn(null);
//        MemberModifier.stub(MemberMatcher.method(ILog.class, "info", String.class, String.class, Map.class)).toReturn(null);
//
//        PowerMockito.mockStatic(QConfigUtil.class);
//        PowerMockito.mockStatic(Response.class);
//        PowerMockito.mockStatic(ResponseBody.class);
//    }
//
//    @Test
//    public void test_getUidByScookie() throws Exception {
//        String result = QunarUserScookieProxy.getUidByScookie(null);
//        assertTrue(StringUtils.isBlank(result));
//
//        when(QConfigUtil.getByFileAndKey(anyString(), anyString())).thenReturn("");
//        result = QunarUserScookieProxy.getUidByScookie("scookie");
//        assertTrue(StringUtils.isBlank(result));
//
//        when(QConfigUtil.getByFileAndKey(anyString(), anyString())).thenReturn("http://test");
//        Call mockCall = mock(Call.class);
//        MemberModifier.stub(MemberMatcher.method(OkHttpClient.class, "newCall", Request.class)).toReturn(mockCall);
//        when(mockCall.execute()).thenReturn(null);
//        result = QunarUserScookieProxy.getUidByScookie("scookie");
//        assertTrue(StringUtils.isBlank(result));
//
//        Response mockResponse = mock(Response.class);
//        when(mockCall.execute()).thenReturn(mockResponse);
//        result = QunarUserScookieProxy.getUidByScookie("scookie");
//        assertTrue(StringUtils.isBlank(result));
//
//        ResponseBody mockResponseBody = mock(ResponseBody.class);
//        when(mockResponse.body()).thenReturn(mockResponseBody);
//        MemberModifier.stub(MemberMatcher.method(ResponseBody.class, "string")).toReturn("");
//        result = QunarUserScookieProxy.getUidByScookie("scookie");
//        assertTrue(StringUtils.isBlank(result));
//
//        MemberModifier.stub(MemberMatcher.method(ResponseBody.class, "string")).toReturn("///");
//        result = QunarUserScookieProxy.getUidByScookie("scookie");
//        assertTrue(StringUtils.isBlank(result));
//
//        MemberModifier.stub(MemberMatcher.method(ResponseBody.class, "string")).toReturn("{}");
//        result = QunarUserScookieProxy.getUidByScookie("scookie");
//        assertTrue(StringUtils.isBlank(result));
//
//        MemberModifier.stub(MemberMatcher.method(ResponseBody.class, "string")).toReturn("{\"simpleUserInfo\":{\"uid\":10086}}");
//        result = QunarUserScookieProxy.getUidByScookie("scookie");
//        assertNotNull(result);
//        assertEquals("10086", result);
//    }
//
//}
