//package com.ctrip.car.market.coupon.restful.business;
//import com.ctrip.car.market.coupon.restful.qconfig.QConfigHelper;
//import com.ctrip.car.market.client.contract.ListPage;
//import com.ctrip.car.market.client.contract.HomePage;
//import com.ctrip.car.market.client.contract.DetailPage;
//import com.ctrip.car.market.client.contract.MergeHomePage;
//import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
//import com.google.common.collect.Lists;
//import com.ctrip.car.market.client.contract.SkinContent;
//
//import com.ctrip.car.market.client.contract.SkinInfo;
//import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
//import com.ctrip.car.market.coupon.restful.contract.QuerySkinsRequestType;
//import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.SDMarketingServiceProxy;
//import org.junit.Assert;
//import org.junit.Test;
//import org.junit.Before;
//import org.junit.After;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PowerMockIgnore;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.core.classloader.annotations.SuppressStaticInitializationFor;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.modules.junit4.PowerMockRunnerDelegate;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//
//@RunWith(PowerMockRunner.class)
//@PowerMockRunnerDelegate(SpringJUnit4ClassRunner.class)
//@PowerMockIgnore({"javax.management.*", "javax.net.ssl.*"})
//@SuppressStaticInitializationFor("com.ctrip.car.market.framework.DesensitizeUtil")
//@PrepareForTest({QConfigHelper.class})
//public class SkinBusinessTest {
//
//    @Mock
//    private CouponServiceQConfig couponServiceQConfig;
//
//    @Mock
//    private SDMarketingServiceProxy sdMarketingServiceProxy;
//
//    @InjectMocks
//    private SkinBusiness skinBusiness;
//
//    @Before
//    public void before() throws Exception {
//        PowerMockito.mockStatic(QConfigHelper.class);
//        Mockito.when(QConfigHelper.getChineseTips(Mockito.anyString())).thenReturn("1");
//    }
//
//    @After
//    public void after() throws Exception {
//    }
//
//    /**
//     * Method: querySkins(QuerySkinsRequestType request)
//     */
//    @Test
//    public void testQuerySkins() throws Exception {
//        QuerySkinsRequestType request = new QuerySkinsRequestType();
//        BaseRequest baseRequest = new BaseRequest();
//        baseRequest.setSourceFrom("ISD_C_APP");
//        request.setBaseRequest(baseRequest);
//
//        SkinInfo skinInfo = new SkinInfo();
//        skinInfo.setName("");
//        skinInfo.setType("");
//        skinInfo.setIsrepeat(false);
//        skinInfo.setPeriod(Lists.newArrayList());
//        skinInfo.setLocations(Lists.newArrayList());
//        skinInfo.setSkinContent(new SkinContent());
//        PowerMockito.when(sdMarketingServiceProxy.getSkins("ISD_C_APP")).thenReturn(skinInfo);
//
//        PowerMockito.when(couponServiceQConfig.getValueByKeyFromQconfig("skinSourceFroms")).thenReturn("ISD_C_APP");
//        Assert.assertEquals(skinBusiness.querySkins(request).getResultCode().toString(), "200");
//
//    }
//
//    @Test
//    public void testQuerySkins2() throws Exception {
//        QuerySkinsRequestType request = new QuerySkinsRequestType();
//        BaseRequest baseRequest = new BaseRequest();
//        baseRequest.setSourceFrom("ISD_C_APP");
//        request.setBaseRequest(baseRequest);
//
//        PowerMockito.when(sdMarketingServiceProxy.getSkins("ISD_C_APP")).thenReturn(null);
//        Assert.assertEquals(skinBusiness.querySkins(request).getResultCode().toString(), "200");
//    }
//
//
//    @Test
//    public void testQuerySkins3() throws Exception {
//        QuerySkinsRequestType request = new QuerySkinsRequestType();
//        BaseRequest baseRequest = new BaseRequest();
//        baseRequest.setSourceFrom("ISD_C_APP");
//        request.setBaseRequest(baseRequest);
//
//        SkinInfo skinInfo = new SkinInfo();
//        skinInfo.setName("");
//        skinInfo.setType("");
//        skinInfo.setIsrepeat(false);
//        skinInfo.setPeriod(Lists.newArrayList());
//        skinInfo.setLocations(Lists.newArrayList());
//
//        SkinContent skinContent = new SkinContent();
//        skinContent.setMergeHomePage(new MergeHomePage());
//        skinContent.setHomePage(new HomePage());
//        skinContent.setListPage(new ListPage());
//        skinContent.setDetailPage(new DetailPage());
//
//        skinInfo.setSkinContent(skinContent);
//        PowerMockito.when(sdMarketingServiceProxy.getSkins("ISD_C_APP")).thenReturn(skinInfo);
//
//        PowerMockito.when(couponServiceQConfig.getValueByKeyFromQconfig("skinSourceFroms")).thenReturn("ISD_C_APP");
//        Assert.assertEquals(skinBusiness.querySkins(request).getResultCode().toString(), "200");
//
//    }
//
//    @Test
//    public void testQuerySkins5() throws Exception {
//        QuerySkinsRequestType request = new QuerySkinsRequestType();
//        BaseRequest baseRequest = new BaseRequest();
//        request.setBaseRequest(baseRequest);
//        PowerMockito.when(sdMarketingServiceProxy.getSkins("ISD_C_APP")).thenReturn(null);
//        Assert.assertEquals(skinBusiness.querySkins(request).getResultCode().toString(), "1");
//    }
//
//}
