package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.commodity.vendor.query.service.contract.dto.SimpleVendorInfo
import com.ctrip.car.commodity.vendor.query.service.method.QueryVendorListToCacheResponseType
import com.ctrip.car.market.coupon.restful.cache.SeoCityCache
import com.ctrip.car.market.coupon.restful.cache.SeoInformationCache
import com.ctrip.car.market.coupon.restful.cache.SeoPoiCache
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache
import com.ctrip.car.market.coupon.restful.dto.CityPoiConfigItem
import com.ctrip.car.market.coupon.restful.dto.KeyValueDto
import com.ctrip.car.market.coupon.restful.dto.VendorCityDto
import com.ctrip.car.market.coupon.restful.dto.VendorCityItem
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.BasicDataProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.TmsProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi.GeoProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform.SeoPlatformProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.vendor.VendorProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO
import com.ctrip.car.osd.basicdataservice.dto.Airport
import com.ctrip.car.osd.basicdataservice.dto.GetAirportsResponseType
import com.ctrip.corp.foundation.translation.currency.util.CarCurrencyDisplayUtil
import com.ctrip.dcs.geo.domain.repository.CityRepository
import com.ctrip.dcs.geo.domain.repository.CountryRepository
import com.ctrip.dcs.geo.domain.repository.ProvinceRepository
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.dcs.geo.domain.value.Country
import com.ctrip.dcs.geo.domain.value.Province
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO
import com.google.common.collect.Maps
import org.apache.commons.collections.CollectionUtils
import org.apache.commons.lang3.StringUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoServiceSpockTest extends Specification {

    def seoCityCache = Mock(SeoCityCache)

    def seoInformationCache = Mock(SeoInformationCache)

    def seoPoiCache = Mock(SeoPoiCache)

    def countryRepository = Mock(CountryRepository)

    def cityRepository = Mock(CityRepository)

    def basicDataProxy = Mock(BasicDataProxy)

    def tmsProxy = Mock(TmsProxy)

    def vendorProxy = Mock(VendorProxy)

    def tripConfig = Mock(TripConfig)

    def provinceRepository = Mock(ProvinceRepository)

    def seoPlatformProxy = Mock(SeoPlatformProxy)

    def geoProxy = Mock(GeoProxy)

    def seoVendorCache = Mock(SeoVendorCache)

    def testInstance = new SeoService(
            seoCityCache: seoCityCache,
            seoInformationCache: seoInformationCache,
            seoPoiCache: seoPoiCache,
            countryRepository: countryRepository,
            cityRepository: cityRepository,
            basicDataProxy: basicDataProxy,
            tmsProxy: tmsProxy,
            vendorProxy: vendorProxy,
            tripConfig: tripConfig,
            provinceRepository: provinceRepository,
            seoPlatformProxy: seoPlatformProxy,
            geoProxy: geoProxy,
            seoVendorCache: seoVendorCache
    )

    MockedStatic<CarCurrencyDisplayUtil> carCurrencyDisplayUtilMockedStatic

    def setup() {
        carCurrencyDisplayUtilMockedStatic = Mockito.mockStatic(CarCurrencyDisplayUtil)
    }

    def cleanup() {
        carCurrencyDisplayUtilMockedStatic.close()
    }

    def "test getSiteUrl"() {
        expect:
        testInstance.getSiteUrl(url as String, locale as String) != null == result

        where:
        url                    | locale  || result
        null                   | null    || false
        "https://www.trip.com" | null    || true
        "https://www.trip.com" | "en-XX" || true
        "https://www.trip.com" | "en-US" || true
    }

    def "test getCountryName"() {
        given:
        countryRepository.findOne(_ as Long, _ as String) >> country
        seoPlatformProxy.querySeoName(_ as Integer, _ as String, _ as String) >> seoCountry

        expect:
        testInstance.getCountryName(1, "en-US") == result

        where:
        country                              | seoCountry || result
        null                                 | null       || ""
        new Country(translationName: "test") | null       || "test"
        new Country(translationName: "test") | "test"     || "test"
    }

    def "test getCityName"() {
        given:
        cityRepository.findOne(_ as Long, _ as String) >> city
        seoPlatformProxy.querySeoName(_ as Integer, _ as String, _ as String) >> seoCity

        expect:
        testInstance.getCityName(1, "en-US") == result

        where:
        city                              | seoCity || result
        null                              | null    || ""
        new City(translationName: "test") | null    || "test"
        new City(translationName: "test") | "test"  || "test"
    }

    def "test getCity"() {
        Map<Long, City> cityMap = Maps.newHashMap();
        cityMap.put(1L, new City(translationName: "test"))

        given:
        cityRepository.findMany(_ as Collection<Long>, _ as String) >> cityMap

        expect:
        testInstance.getCity([1L], locale as String) != null == result

        where:
        locale  || result
        "en-US" || true
    }

    def "test getProvinceName"() {
        given:
        provinceRepository.findOne(_ as Long, _ as String) >> province
        seoPlatformProxy.querySeoName(_ as Integer, _ as String, _ as String) >> seoProvince

        expect:
        testInstance.getProvinceName(1, "en-US") == result

        where:
        province                              | seoProvince || result
        null                                  | null        || ""
        new Province(translationName: "test") | null        || "test"
        new Province(translationName: "test") | "test"      || "test"
    }

    def "test getAirportName"() {
        given:
        basicDataProxy.getAirport(_ as String, _ as String) >> responseType
        seoPlatformProxy.querySeoName(_ as Integer, _ as String, _ as String) >> seoAirport

        expect:
        testInstance.getAirportName("test", "test") == result

        where:
        responseType                                                              | seoAirport || result
        new GetAirportsResponseType()                                             | null       || ""
        new GetAirportsResponseType(airports: [new Airport(airportName: "test")]) | null       || "test"
        new GetAirportsResponseType(airports: [new Airport(airportName: "test")]) | "test"     || "test"
    }

    def "test getVendorName"() {
        given:
        vendorProxy.queryVendor(_ as String) >> response

        expect:
        testInstance.getVendorName("test") == result

        where:
        response                                                                                 || result
        new QueryVendorListToCacheResponseType()                                                 || null
        new QueryVendorListToCacheResponseType(list: [new SimpleVendorInfo(vendorName: "test")]) || "test"
    }

    def "test getVehicleGroupName"() {
        given:
        tmsProxy.getTranslateValue(_ as String, _ as String) >> "test"
        tripConfig.getVehicleGroupKey() >> "test"

        expect:
        testInstance.getVehicleGroupName("1", vehicleName as String, "en-US") == result

        where:
        vehicleName || result
        "test"      || "test"
    }

    def "test queryHotDestination"() {
        given:
        seoPoiCache.queryByPoiCode(_ as Integer, _ as String) >> [new SeoHotDestinatioinfoDO()]
        seoPoiCache.queryByCity(_ as Integer) >> [new SeoHotDestinatioinfoDO()]
        seoPoiCache.queryByCountry(_ as Integer) >> [new SeoHotDestinatioinfoDO()]

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryHotDestination(countryId as Integer, cityId as Integer, poiType as Integer, poiCode as String)) == result

        where:
        countryId | cityId | poiType | poiCode || result
        1         | null   | null    | null    || true
        1         | 1      | null    | null    || true
        1         | 1      | 1       | "test"  || true
        null      | null   | null    | null    || false
    }

    def "test queryPoiType"() {
        given:
        tripConfig.getCityPoiConfigList() >> [new CityPoiConfigItem(poiCode: "test", poiType: 1)]

        expect:
        testInstance.queryPoiType(code as String) == result

        where:
        code   || result
        "123"  || 0
        "test" || 1
    }

    def "test queryCityDefaultAirport"() {
        given:
        tripConfig.getCityPoiConfigList() >> [new CityPoiConfigItem(poiType: 1, cityId: 1, poiCode: "test")]
        cityRepository.findOne(_ as Long) >> city

        expect:
        testInstance.queryCityDefaultAirport(poiCode as String, cityId as Integer) != null == result

        where:
        poiCode | cityId | city                    || result
        "test"  | 1      | null                    || false
        null    | 2      | null                    || false
        null    | 1      | null                    || true
        null    | 1      | new City(countryId: 1L) || true
    }

    def "test queryCityDefaultPoi"() {
        given:
        tripConfig.getCityPoiConfigList() >> [new CityPoiConfigItem(poiType: 2, cityId: 1, poiCode: "test")]

        expect:
        testInstance.queryCityDefaultPoi(poiCode as String, cityId as Integer, "test") != null == result

        where:
        poiCode | cityId || result
        "test"  | 1      || false
    }

    def "test queryPoiName"() {
        given:
        seoPlatformProxy.querySeoName(4, _ as String, _ as String) >> "test"
        geoProxy.getPoiDetail(_ as String, _ as String) >> poi

        expect:
        org.apache.commons.lang.StringUtils.isNotEmpty(testInstance.queryPoiName(poiType as Integer, "1", "test")) == result

        where:
        poiType | poi                               || result
        1       | null                              || true
        0       | new PlaceDetailsDTO(name: "test") || true
        0       | new PlaceDetailsDTO(name: "")     || false
    }

    def "test queryVendorHotCityDefault"() {
        given:
        seoVendorCache.queryVendorInformation(_ as String) >> vendorInformation
        tripConfig.getVendorCityList() >> vendorCityList

        expect:
        testInstance.queryVendorHotCityDefault(vendorCode as String).getPoiCode() == result

        where:
        vendorCode | vendorInformation                                                     | vendorCityList                                                                                                              || result
        "SD0001"   | []                                                                    | [new VendorCityDto(vendorCode: "SD0001", vendorName: "test", cityIdList: [new VendorCityItem(cityId: 1)])]                  || "LAX"
        "SD0001"   | []                                                                    | [new VendorCityDto(vendorCode: "SD0001", vendorName: "test", cityIdList: [new VendorCityItem(cityId: 1, poiCode: "TEST")])] || "TEST"
        "SD0001"   | [new SeoHotVendorInformationDO(vendorCode: "SD0001", poiCode: "SHA")] | [new VendorCityDto(vendorCode: "SD0001", vendorName: "test", cityIdList: [new VendorCityItem(cityId: 1, poiCode: "TEST")])] || "SHA"
    }
}
