package com.ctrip.car.market.coupon.restful.business.seovendor

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.QueryZonesRequestType
import com.ctrip.car.market.coupon.restful.contract.QueryZonesResponseType
import com.ctrip.car.market.coupon.restful.contract.SeoZone
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoPoiDetailRequestType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi.GeoProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform.SeoPlatformProxy
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO
import spock.lang.Specification

class SeoVendorPoiBusinessTest extends Specification {

    def service = Mock(SeoService)

    def seoVendorCache = Mock(SeoVendorCache)

    def carCrossRecommendedServiceProxy = Mock(CarCrossRecommendedServiceProxy)

    def seoPlatformProxy = Mock(SeoPlatformProxy)

    def geoProxy = Mock(GeoProxy)

    def testInstance = new SeoVendorPoiBusiness(
            service: service,
            seoVendorCache: seoVendorCache,
            carCrossRecommendedServiceProxy: carCrossRecommendedServiceProxy,
            seoPlatformProxy: seoPlatformProxy,
            geoProxy: geoProxy
    )

    def test() {
        given:
        seoVendorCache.queryVendor(_ as String) >> vendorDO
        service.queryVendorHotCityDefault(_ as String) >> vendorPoi
        service.queryVendorHotCityDefault(_ as String, _ as Integer) >> vendorPoi
        geoProxy.getPoiDetail(_ as String, _ as String) >> new PlaceDetailsDTO(carPlaceId: "1", latitude: BigDecimal.TWO, longitude: BigDecimal.TWO)
        carCrossRecommendedServiceProxy.queryZones(_ as QueryZonesRequestType) >> new QueryZonesResponseType(zone: new SeoZone(countryId: 1, cityId: 1, airportCode: "test", latitude: 1D, longitude: 1D))
        seoPlatformProxy.querySeoName(_ as int, _ as String, _ as String) >> "test"
        service.queryUrlName(_ as Long, _ as int) >> "test"

        expect:
        testInstance.queryPoiDetail(request as QuerySeoPoiDetailRequestType).getPoiDetail() != null === result

        where:
        request                                                                                               | vendorDO                                                   | vendorPoi                                                              | result
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001") | null                                                       | null                                                                  || false
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001") | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") | null                                                                  || false
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001") | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") | new SeoHotVendorInformationDO(poiType: 1, poiCode: "test", cityId: 1) || true
        new QuerySeoPoiDetailRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001") | new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test") | new SeoHotVendorInformationDO(poiType: 2, poiCode: "test", cityId: 1) || true
    }

}
