package com.ctrip.car.market.coupon.restful.business.tripCarCrossRecommend

import com.ctrip.car.market.common.utils.ABTestUtils
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.RecommendCarProduct
import com.ctrip.car.market.coupon.restful.contract.ServiceType
import com.ctrip.car.market.coupon.restful.contract.TrainInfo
import com.ctrip.car.market.coupon.restful.contract.TripCarCrossRecommendRequestType
import com.ctrip.car.market.coupon.restful.contract.TripRecommendSceneEnum
import com.ctrip.car.market.coupon.restful.dto.TrainPointOfInterest
import com.ctrip.car.market.coupon.restful.dto.TripUrlParams
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai.UserLabelAiProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.BasicDataProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripRecommendConfig
import com.ctrip.car.osd.basicdataservice.dto.PoiInfo
import com.ctrip.car.osd.shopping.api.entity.LocationRequestInfo
import com.ctrip.car.osd.shopping.api.entity.OsdLabelInfo
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsRequestType
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes
import com.ctrip.car.osd.shopping.api.entity.RecomdVehicleDTO
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctrip.model.QueryPoiDetailsResponseType
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.mockito.internal.creation.MockSettingsImpl
import org.mockito.internal.util.MockUtil
import org.mockito.mock.MockCreationSettings
import org.mockito.plugins.MockMaker
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import static org.mockito.Mockito.*

class TrainRentalCarBusinessTest extends Specification {
    @Shared
    def osdShoppingProxy = Mock(OsdShoppingProxy)
    @Shared
    def userLabelAiProxy = Mock(UserLabelAiProxy)
    @Shared
    def basicDataProxy = Mock(BasicDataProxy)
    @Shared
    def tripRecommendConfig = Mock(TripRecommendConfig)
    @Shared
    def testObj = new TrainRentalCarBusiness(tripRecommendConfig: tripRecommendConfig, osdShoppingProxy: osdShoppingProxy, userLabelAiProxy: userLabelAiProxy, basicDataProxy: basicDataProxy)


    @Shared
    MockedStatic<Shark> sharkMockedStatic
    @Shared
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setupSpec() {
        try {
            MockSettingsImpl impl = MockSettingsImpl.class.cast(withSettings());
            MockCreationSettings<SharkInitializer> creationSettings = impl.buildStatic(SharkInitializer.class)
            MockMaker.StaticMockControl<SharkInitializer> control = MockUtil.createStaticMock(SharkInitializer.class, creationSettings)
            control.disable()
        }catch (Exception ignore){

        }
        sharkInitializerMockedStatic = mockStatic(SharkInitializer.class)
        sharkMockedStatic = mockStatic(Shark)
        sharkMockedStatic.when {Shark.getByLocale(anyString(), anyString())}.thenReturn("TEST")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.card.title"), eq("zh-cn"))}.thenReturn("{\\\$}当地热门车型推荐")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.component.title"), eq("zh-cn"))}.thenReturn("租车")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.top.sale.recommendation"), eq("zh-cn"))}.thenReturn("热销")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.user.exclusive.recommendation"), eq("zh-cn"))}.thenReturn("火车票用户专享")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.family.recommendation"), eq("zh-cn"))}.thenReturn("适合家庭出行")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.card.title"), eq("zh-cn"))}.thenReturn("test")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.priority.coupon.service"), eq("zh-cn"))}.thenReturn("火车票用户最高减%1")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.new.guest.service"), eq("zh-cn"))}.thenReturn("新客券立减8%")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.traffic.service"), eq("zh-cn"))}.thenReturn("火车误点，为您免费留车")
        sharkMockedStatic.when {Shark.getByLocale(eq("trip.train.rental.interest.service"), eq("zh-cn"))}.thenReturn("快速客户服务支援")

        tripRecommendConfig.getTrainPriorityLabelId() >> "1"
        tripRecommendConfig.getTrainFamilyPeopleNum() >> 5
        tripRecommendConfig.getTrainFamilyLuggageNum() >> 2
        tripRecommendConfig.getTripAppListUrl() >> "https://www.fat1.qa.nt.tripqate.com/carhire/online/list?serverRoute=1&subEnv=fat6214&"
        tripRecommendConfig.getTripPcListUrl() >> "https://www.fat1.qa.nt.tripqate.com/carhire/online/list?serverRoute=1&subEnv=fat6214&"
        tripRecommendConfig.getFreeCancelLabelCode() >> "3000"
        tripRecommendConfig.getDiscountLabels() >> ["111"]
        tripRecommendConfig.getAbTestExpCode(any() as String) >> "sass"
        tripRecommendConfig.getOnlyHasPriority() >> "18"
        tripRecommendConfig.getHasPriorityAndNew() >> "25"
        tripRecommendConfig.getEuropeCity() >> 3
        tripRecommendConfig.getTrainPrivilege() >> 1
        tripRecommendConfig.getNewGuest() >> 2
        tripRecommendConfig.getDefaultCode() >> 4
        tripRecommendConfig.findTrainPointOfInterestByPriority(_ as Integer) >> new TrainPointOfInterest(appBackUrl: "xxx", pcBackUrl: "xxx", desc: "xxx", priority: 1, textSharkKey: "trip.train.rental.priority.coupon.service")
        tripRecommendConfig.getBlankList() >> ["B,C,D"]
    }

    def cleanupSpec() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }



    @Unroll
    def "fixReturnTimeTest"() {
        given: "设定相关方法入参"
        def instance = Calendar.getInstance();instance.setTimeInMillis(1736902800000);
        def instance2 = Calendar.getInstance();instance2.setTimeInMillis(1736902800000);
        def instance3 = Calendar.getInstance();instance3.setTimeInMillis(1736902800000);
        def instance5 = Calendar.getInstance();instance5.setTimeInMillis(1736871900000);
        when:
        def r1 = (Calendar)ReflectionTestUtils.invokeMethod(testObj, "fixReturnTime", instance, 0)
        def r2 = (Calendar)ReflectionTestUtils.invokeMethod(testObj, "fixReturnTime", instance2, 1)
        def r3 = (Calendar)ReflectionTestUtils.invokeMethod(testObj, "fixReturnTime", instance3, 2)
        def r5 = (Calendar)ReflectionTestUtils.invokeMethod(testObj, "fixReturnTime", instance5, 1)
        then:

        r1.getTimeInMillis() == 1737507600000
        r2.getTimeInMillis() == 1736899200000
        r3.getTimeInMillis() == 1737507600000
        r5.getTimeInMillis() == 1736866800000
    }

    @Unroll
    def "fixPickUpTimeTest"() {
        given: "设定相关方法入参"
        def instance = Calendar.getInstance();instance.setTimeInMillis(1736902800000);
        def instance2 = Calendar.getInstance();instance2.setTimeInMillis(1736871900000);
        def instance3 = Calendar.getInstance();instance3.setTimeInMillis(1736902800000);
        def instance4 = Calendar.getInstance();instance4.setTimeInMillis(1736902800000);

        when:
        def r1 = (Calendar)ReflectionTestUtils.invokeMethod(testObj, "fixPickUpTime", instance, 0)
        def r2 = (Calendar)ReflectionTestUtils.invokeMethod(testObj, "fixPickUpTime", instance2, 1)
        def r3 = (Calendar)ReflectionTestUtils.invokeMethod(testObj, "fixPickUpTime", instance3, 2)
        then:

        r1.getTimeInMillis() == 1736904600000
        r2.getTimeInMillis() == 1736874000000
        r3.getTimeInMillis() == 1736906400000
    }

    def "testGetInterestTextAndBackUrl"() {
        given: "设定相关方法入参"

        expect:
        ReflectionTestUtils.invokeMethod(testObj, "getInterestTextAndBackUrl", hasTrainPrivilege, isRentalCarNewGuest,isEurope) == expectedResult

        where: "表格方式验证多种分支调用场景"
        hasTrainPrivilege | isRentalCarNewGuest | isEurope  || expectedResult
        true              | false               | false     || 1
        false             | true                | false     || 2
        false             | false               | true      || 3
        false             | false               | false     || 4
    }

//    def "testGetModelRecommendation"() {
//        given: "设定相关方法入参"
//        def map = ["1": new RecomdProduct(vehicle: new RecomdVehicleDTO(vehicleKey: 1))]
//        def product1 = new RecommendCarProduct()
//        def product2 = new RecommendCarProduct()
//        def product3 = new RecommendCarProduct()
//        def product4 = new RecommendCarProduct()
//        when:
//        ReflectionTestUtils.invokeMethod(testObj, "getModelRecommendation", new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "1", vehicleKey: 1), labels: [new OsdLabelInfo(code: "1")]), "zh-cn", map, product1)
//        ReflectionTestUtils.invokeMethod(testObj, "getModelRecommendation", new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "2", passengerNo: 2, luggageNo: 2, vehicleKey: 1), labels: [new OsdLabelInfo(code: "1")]) , "zh-cn", map, product2)
//        ReflectionTestUtils.invokeMethod(testObj, "getModelRecommendation", new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "2", passengerNo: 5, luggageNo: 2, vehicleKey: 1), labels: [new OsdLabelInfo(code: "2")]) , "zh-cn", map, product3)
//        ReflectionTestUtils.invokeMethod(testObj, "getModelRecommendation", new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "2", passengerNo: 1, luggageNo: 1, vehicleKey: 1), labels: [new OsdLabelInfo(code: "2")]) , "zh-cn", map, product4)
//
//        then:
//        product1.getModelRecommendation() == "热销"
//        product2.getModelRecommendation() == "火车票用户专享"
//        product3.getModelRecommendation() == "适合家庭出行"
//        product4.getModelRecommendation() == null
//    }

    def "testBuildParams"() {
        given: "设定相关方法入参"

        expect:
        def method = (TripUrlParams)ReflectionTestUtils.invokeMethod(testObj, "buildParams", sourceCountryId, productRes, product, baseRequest)
        method.getSourceCountryId() == expectedResult
        where: "表格方式验证多种分支调用场景"

        sourceCountryId     | productRes                                                         | product                                                                            |  baseRequest            || expectedResult
        1                   | new RecomdProductRes(pickupLocation: new LocationRequestInfo())    | null                                                                               |  new BaseRequest()      || 1
        1                   | new RecomdProductRes(pickupLocation: new LocationRequestInfo())    | new RecomdProduct(price: new RecomdPriceDTO(), vehicle: new RecomdVehicleDTO())    |  new BaseRequest()      || 1
    }

//    def "testRecommend"() {
//        given: "设定相关方法入参"
//        ABTestUtils.getAB(_ as String, _ as String) >> "A"
//        basicDataProxy.getTrainPoi(_ as String, _ as String) >> new QueryPoiDetailsResponseType(poiInfos: [new PoiInfo(id: 1, code: "1", name: "name", address: "address", cityName: "name", cityId: 1, latitude: 111, longitude: 111, poiType: 1)])
//        osdShoppingProxy.queryRecommendProductsTripTrain(_ as QueryRecomdProductsRequestType) >> buildResponse()
//        userLabelAiProxy.judgeIsNewGuest(_ as String, _ as List<String>) >> true
//
//        expect:
//        testObj.recommend(requestType).getProductList().get(index).getModelRecommendation()
//
//
//        where: "表格方式验证多种分支调用场景"
//
//        requestType                                                                                                                                                                                                             | index              || expectedResult
//        new TripCarCrossRecommendRequestType(baseRequest: new BaseRequest(requestId: "1231321312312", locale: "zh-cn"), crossScene: TripRecommendSceneEnum.TRAIN,
//                productType: ServiceType.CAR_RENTAL, trainInfoList: [new TrainInfo(trainType: 0, departureCityId: 1, departureStation: "12", arrivalCityId: 1, arrivalStation: "13", departureTime: Calendar.getInstance(),
//        arrivalTime: Calendar.getInstance(), hasChildren: false, peopleNumber: 5, arrivalCityIsEurope: false, seasonStartTime: Calendar.getInstance())])                                                                        | 0                  || "热销"
//
//        new TripCarCrossRecommendRequestType(baseRequest: new BaseRequest(requestId: "1231321312312", locale: "zh-cn"), crossScene: TripRecommendSceneEnum.TRAIN,
//                productType: ServiceType.CAR_RENTAL, trainInfoList: [new TrainInfo(trainType: 0, departureCityId: 1, departureStation: "12", arrivalCityId: 1, arrivalStation: "13", departureTime: Calendar.getInstance(),
//                arrivalTime: Calendar.getInstance(), hasChildren: false, peopleNumber: 5, arrivalCityIsEurope: false, seasonStartTime: Calendar.getInstance())])                                                                        |1                  || "热销"
//
//        new TripCarCrossRecommendRequestType(baseRequest: new BaseRequest(requestId: "1231321312312", locale: "zh-cn"), crossScene: TripRecommendSceneEnum.TRAIN,
//                productType: ServiceType.CAR_RENTAL, trainInfoList: [new TrainInfo(trainType: 0, departureCityId: 1, departureStation: "12", arrivalCityId: 1, arrivalStation: "13", departureTime: Calendar.getInstance(),
//                arrivalTime: Calendar.getInstance(), hasChildren: false, peopleNumber: 5, arrivalCityIsEurope: false, seasonStartTime: Calendar.getInstance())])                                                                        | 2                  || "火车票用户专享"
//
//
//    }

    public QueryRecomdProductsResponseType buildResponse() {
        def response = new QueryRecomdProductsResponseType(sourceCountryId: 1, recomdProductResList: [
                new RecomdProductRes(products: [
                        new RecomdProduct(
                                price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.valueOf(123), currentOriginalDailyPrice: BigDecimal.valueOf(123), currency: "cny"),
                                vehicle: new RecomdVehicleDTO(vehicleName: "name", vehicleKey: "key", vendorCode: "code", imageUrl: "xx", groupCode: "1", groupName: "small", snm: 100, doorNo: 2, luggageNo: 2, passengerNo: 5),
                                labels: [new OsdLabelInfo(code: "1")]),

                        new RecomdProduct(
                                price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.valueOf(123), currentOriginalDailyPrice: BigDecimal.valueOf(123), currency: "cny"),
                                vehicle: new RecomdVehicleDTO(vehicleName: "name1", vehicleKey: "key1", vendorCode: "code1", imageUrl: "xx", groupCode: "12", groupName: "suv", snm: 100, doorNo: 1, luggageNo: 1, passengerNo: 5),
                                labels: [new OsdLabelInfo(code: "1")]),

                        new RecomdProduct(
                                price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.valueOf(123), currentOriginalDailyPrice: BigDecimal.valueOf(123), currency: "cny"),
                                vehicle: new RecomdVehicleDTO(vehicleName: "name2", vehicleKey: "key2", vendorCode: "code2", imageUrl: "xx", groupCode: "12", groupName: "suv", snm: 33, doorNo: 1, luggageNo: 1, passengerNo: 5),
                                labels: [new OsdLabelInfo(code: "1")]),

                        new RecomdProduct(
                                price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.valueOf(123), currentOriginalDailyPrice: BigDecimal.valueOf(123), currency: "cny"),
                                vehicle: new RecomdVehicleDTO(vehicleName: "name3", vehicleKey: "key3", vendorCode: "code3", imageUrl: "xx", groupCode: "12", groupName: "suv", snm: 33, doorNo: 1, luggageNo: 1, passengerNo: 5),
                                labels: [new OsdLabelInfo(code: "1"), new OsdLabelInfo(code: "3862")]),
                ],
                        pickupLocation: new LocationRequestInfo(locationName: "name", locationType: 1, cityId: 1, cityName: "city", date: Calendar.getInstance(), locationCode: "1", poi: new com.ctrip.car.osd.shopping.api.entity.PoiInfo(longitude: 11, latitude: 11)),
                        returnLocation: new LocationRequestInfo(locationName: "name", locationType: 1, cityId: 1, cityName: "city", date: Calendar.getInstance(), locationCode: "1", poi: new com.ctrip.car.osd.shopping.api.entity.PoiInfo(longitude: 11, latitude: 11)))
        ])
        return response
    }

    def "testRecommend2"() {
        given: "设定相关方法入参"

        expect:
        testObj.recommend(requestType).getBaseResponse().getCode() == expectedResult

        where: "表格方式验证多种分支调用场景"

        requestType                                                                                              || expectedResult
        new TripCarCrossRecommendRequestType()                                                                   || "-1"
        new TripCarCrossRecommendRequestType(baseRequest: new BaseRequest())                                     || "-1"
    }

}
