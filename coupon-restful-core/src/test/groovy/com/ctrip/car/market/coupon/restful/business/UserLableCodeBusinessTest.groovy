package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.common.utils.ABTestUtils
import com.ctrip.car.market.coupon.restful.cache.SeoCityCache
import com.ctrip.car.market.coupon.restful.contract.JumpImageInfo
import com.ctrip.car.market.coupon.restful.contract.RestRequestHeader
import com.ctrip.car.market.coupon.restful.contract.UserLabelInfo
import com.ctrip.car.market.coupon.restful.contract.queryUserLabelCodeRequestType
import com.ctrip.car.market.coupon.restful.contract.queryUserMktConditionRequestType
import com.ctrip.car.market.coupon.restful.contract.queryUserMktConditionResponseType
import com.ctrip.car.market.coupon.restful.dto.UserMktConditionDTO
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai.UserLabelAiProxy
import com.ctrip.car.market.coupon.restful.utils.BaseUtils
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig
import com.ctrip.car.market.coupon.restful.utils.JsonUtils
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import com.ctriposs.baiji.rpc.mobile.common.types.ExtensionFieldType
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import credis.java.client.util.JsonUtil
import org.mockito.MockedStatic
import org.mockito.Mockito
import soa.ctrip.com.tour.ai.user.label.LabelInfo
import spock.lang.*

import static org.mockito.ArgumentMatchers.any
import static org.mockito.ArgumentMatchers.anyString
import static org.mockito.Mockito.when

class UserLableCodeBusinessTest extends Specification {

    def userLabelAiProxy = Mock(UserLabelAiProxy)
    def serviceQConfig = Mock(CouponServiceQConfig)
    def userLableCodeBusiness = new UserLableCodeBusiness(userLabelAiProxy: userLabelAiProxy, serviceQConfig: serviceQConfig)
    List<UserMktConditionDTO> list = new ArrayList<>();
    MockedStatic<BaseUtils> baseUtilsMockedStatic
    MockedStatic<ABTestUtils> abTestUtilsMockedStatic

    def setup() {
        serviceQConfig.getUserMktConditions() >> list
        serviceQConfig.getUserMktConditions2() >> list
        serviceQConfig.getValueByKeyFromQconfig("shareABTestNumber") >> "shareABTestNumber";
        serviceQConfig.getValueByKeyFromQconfig("wechatBTestNumber") >> "wechatBTestNumber"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.wechat") >> "ISD_C_APP"

        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.app") >> "ISD_C_WX"
        baseUtilsMockedStatic = Mockito.mockStatic(BaseUtils.class)
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("test")

        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("test")

        abTestUtilsMockedStatic = Mockito.mockStatic(ABTestUtils.class)
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB(Mockito.anyString(), Mockito.anyString()))
                .thenReturn("AAAAA")

    }

    def cleanup() {
        baseUtilsMockedStatic.close()
        abTestUtilsMockedStatic.close()

    }

    @Unroll
    def "query User Label Code where request=#request then expect: #expectedResult"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])


        expect:
        userLableCodeBusiness.queryUserLabelCode(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 0) || true
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 1) || true

    }

    @Unroll
    def "query User Label Code where request=#request then expect: #expectedResult   nologin"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn(null)

        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn(null)

        expect:
        userLableCodeBusiness.queryUserLabelCode(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 0) || null
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 1) || null

    }

    @Unroll
    def "query User Label Code where request=#request then expect: #expectedResult   noResult"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> null

        expect:
        userLableCodeBusiness.queryUserLabelCode(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 0) || Boolean.FALSE
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 1) || Boolean.FALSE
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), null, 1)           || null

    }

    @Unroll
    def "test query User Mkt Condition"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> null >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("323434")

        serviceQConfig.getValueByKeyFromQconfig("shareABTestNumber") >> "";
        serviceQConfig.getValueByKeyFromQconfig("wechatBTestNumber");
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;


        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("3434")
        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "scenecode", "sourcefrom", 0, null, 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", null, "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "scenecode", "", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result3 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "", "sourcefrom", 1, ["labelId"], 0))

        queryUserMktConditionResponseType result4 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "", "scenecode", "sourcefrom", 0, ["labelId"], 0))

        queryUserMktConditionResponseType result5 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "", "scenecode", "ISD_C_WX", 0, ["labelId"], null))
        queryUserMktConditionResponseType result6 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", null, "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), null, "scenecode", "ISD_C_APP", 0, ["labelId"], 1))

        queryUserMktConditionResponseType result7 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", null, "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "scenecode", "ISD_C_APP", 1, ["labelId"], 1))

        queryUserMktConditionResponseType result8 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", null, "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "scenecode", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result9 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", null, "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "scenecode", "ISD_C_APP", null, ["labelId"], null))

        then:
        result.getResultCode() == -1
        result2.getResultCode() == -1

        result3.getResultCode() == -1
        result4.getResultCode() == 200
        result5.getResultCode() == -1
        result6.getResultCode() == 200
    }


    @Unroll
    def "query User Mkt Condition where request=#request then expect: #expectedResult"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode", sourceFrom: "ISD_C_APP", abResult: "23", display: "34434")])

        expect:
        userLableCodeBusiness.queryUserMktCondition(request).getLabelResult() == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                                                              || expectedResult
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "sceneCode", "ISD_C_APP", 0, ["labelId"], 0) || true
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "sceneCode", "ISD_C_APP", 0, ["labelId"], 1) || true
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "sceneCode", "ISD_C_APP", 0, ["labelId"], 2) || true
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", null, "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), null, "sceneCode", "ISD_C_APP", 0, ["labelId"], 2)   || true

    }
    /***
     *  APP banner   B+b
     */
    @Unroll
    def "test query User Mkt Condition B+b"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("2323233")
        serviceQConfig.getValueByKeyFromQconfig("shareABTestNumber") >> "shareABTestNumber";
        serviceQConfig.getValueByKeyFromQconfig("wechatBTestNumber") >> "wechatBTestNumber"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.wechat") >> "ISD_C_APP"

        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.app") >> "ISD_C_WX"
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("343434")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB(Mockito.anyString(), Mockito.anyString()))
                .thenReturn("B")

        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result1 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_APP", 0, ["labelId"], 0))

        then:
        result.getLabelResult() == true
        result2.getLabelResult() == true
        result1.getLabelResult() == true


    }


    /***
     *  APP banner   ACD+B
     */
    @Unroll
    def "test query User Mkt Condition ACD+b"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("2323233")
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("343434")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("shareABTestNumber", "cid"))
                .thenReturn("A")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("wechatBTestNumber", "cid"))
                .thenReturn("B")
        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result1 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "OSD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner2", "ISD_C_APP", 0, ["labelId"], 0))

        then:
        result.getLabelResult() == true
        result2.getLabelResult() == true
        result1.getLabelResult() == true


    }
    /***
     *  APP banner   B+ACD
     */
    @Unroll
    def "test query User Mkt Condition B+ACD"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("2323233")
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("343434")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("shareABTestNumber", "cid"))
                .thenReturn("B")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("wechatBTestNumber", "cid"))
                .thenReturn("A")
        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result1 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "OSD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner2", "ISD_C_APP", 0, ["labelId"], 0))

        then:
        result.getLabelResult() == true
        result2.getLabelResult() == true
        result1.getLabelResult() == true
    }

    /***
     *  APP banner   "ACD+ACD
     */


    @Unroll
    def "test query User Mkt Condition APP banner  ACD+ACD"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("2323233")
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("343434")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("shareABTestNumber", "cid"))
                .thenReturn("A")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("wechatBTestNumber", "cid"))
                .thenReturn("A")
        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result1 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "OSD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner2", "ISD_C_APP", 0, ["labelId"], 0))

        then:
        result.getLabelResult() == true
        result2.getLabelResult() == true
        result1.getLabelResult() == true
    }
    /***
     *  APP banner   default
     */


    /***
     *  mini banner   all
     */
    def "test query User Mkt Condition APP banner   default"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("2323233")
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("343434")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("shareABTestNumber", "cid"))
                .thenReturn("A")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("wechatBTestNumber", "cid"))
                .thenReturn("A")
        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_WX", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result1 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner", "ISD_C_WX", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "middleBanner2", "ISD_C_WX", 0, ["labelId"], 0))

        then:
        result.getLabelResult() == true
        result2.getLabelResult() == true
        result1.getLabelResult() == true
    }
    /***
     *  mini banner   default
     */


    /***
     *  APP jump   B+
     */
    def "test query User Mkt Condition mini banner    APP jump B"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("2323233")
        serviceQConfig.getValueByKeyFromQconfig("shareABTestNumber") >> "shareABTestNumber";
        serviceQConfig.getValueByKeyFromQconfig("wechatBTestNumber") >> "wechatBTestNumber"
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("343434")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("shareABTestNumber", "cid"))
                .thenReturn("A")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("wechatBTestNumber", "cid"))
                .thenReturn("A")
        serviceQConfig.getUserMktConditions2() >> list
        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "jumpBanner", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result1 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "jumpBanner", "ISD_C_APP", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "jumpBanner", "ISD_C_APP", 0, ["labelId"], 0))

        then:
        result.getLabelResult() == true
        result2.getLabelResult() == true
        result1.getLabelResult() == true
    }
    /***
     *  mini jump   all
     */
    def "test query User Mkt Condition mini banner   mini jump   allall"() {
        given:
        (userLabelAiProxy.queryUserLabelCode(_ as String, _ as List)) >> ([new LabelInfo("labelId", "true")])
        (serviceQConfig.getUserMktConditions2()) >> ([new UserMktConditionDTO(sceneCode: "sceneCode2", sourceFrom: "ISD_C_APP", display: "121212")])
        baseUtilsMockedStatic.when(() -> BaseUtils.getUidByCommon(Mockito.any(MobileRequestHead)))
                .thenReturn("2323233")
        serviceQConfig.findAPPSourceFrom(_ as String) >> true;
        serviceQConfig.findWeChatSourceFrom(_ as String) >> true;
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner") >> "middleBanner"
        serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner") >> "jumpBanner"
        baseUtilsMockedStatic.when(() -> BaseUtils.getQunarUidByCommon(Mockito.any(RestRequestHeader)))
                .thenReturn("343434")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("shareABTestNumber", "cid"))
                .thenReturn("A")
        abTestUtilsMockedStatic.when(() -> ABTestUtils.getAB("wechatBTestNumber", "cid"))
                .thenReturn("A")
        when:
        queryUserMktConditionResponseType result = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "jumpBanner", "ISD_C_WX", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result1 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "jumpBanner", "ISD_C_WX", 0, ["labelId"], 0))
        queryUserMktConditionResponseType result2 = userLableCodeBusiness.queryUserMktCondition(new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "jumpBanner", "ISD_C_WX", 0, ["labelId"], 0))

        then:
        result.getLabelResult() == true
        result2.getLabelResult() == true
        result1.getLabelResult() == true
    }
}

