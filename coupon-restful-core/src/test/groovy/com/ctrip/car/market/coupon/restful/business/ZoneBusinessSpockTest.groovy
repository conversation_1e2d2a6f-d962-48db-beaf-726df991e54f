package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.QueryZonesRequestType
import com.ctrip.car.market.coupon.restful.contract.QueryZonesResponseType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy
import spock.lang.Specification

class ZoneBusinessSpockTest extends Specification {

    def carCrossRecommendedServiceProxy = Mock(CarCrossRecommendedServiceProxy)
    def zoneBusiness = new ZoneBusiness(carCrossRecommendedServiceProxy: carCrossRecommendedServiceProxy)

    def setup() {
    }

    def "queryZones"() {
        given:
        carCrossRecommendedServiceProxy.queryZones(_ as QueryZonesRequestType) >> new QueryZonesResponseType()

        when:
        QueryZonesResponseType responseType = zoneBusiness.queryZones(new QueryZonesRequestType(baseRequest: new BaseRequest()));
        QueryZonesResponseType responseType2 = zoneBusiness.queryZones(new QueryZonesRequestType());
        then:
        responseType != null;
        responseType2 != null;
    }



}
