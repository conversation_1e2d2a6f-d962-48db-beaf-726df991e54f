package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.JumpImageInfo
import com.ctrip.car.market.coupon.restful.contract.RestRequestHeader
import com.ctrip.car.market.coupon.restful.contract.UserLabelInfo
import com.ctrip.car.market.coupon.restful.contract.queryUserLabelCodeRequestType
import com.ctrip.car.market.coupon.restful.contract.queryUserLabelCodeResponseType
import com.ctrip.car.market.coupon.restful.contract.queryUserMktConditionRequestType
import com.ctrip.car.market.coupon.restful.contract.queryUserMktConditionResponseType
import com.ctrip.car.market.coupon.restful.dto.UserMktConditionDTO
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai.UserLabelAiProxy
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig
import com.ctriposs.baiji.rpc.common.types.ResponseStatusType
import com.ctriposs.baiji.rpc.mobile.common.types.ExtensionFieldType
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import soa.ctrip.com.tour.ai.user.label.LabelInfo
import spock.lang.*
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import static org.mockito.Mockito.*

class UserLableCodeBusinessTest2 extends Specification {
    @Mock
    UserLabelAiProxy userLabelAiProxy
    @Mock
    CouponServiceQConfig serviceQConfig

    @InjectMocks
    UserLableCodeBusiness userLableCodeBusiness

    def setup() {
        MockitoAnnotations.openMocks(this)
    }

    @Unroll
    def "query User Label Code where request=#request then expect: #expectedResult"() {
        given:
        when(userLabelAiProxy.queryUserLabelCode(anyString(), any(List<String>.class))).thenReturn([new LabelInfo("labelId", "labelValue")])

        expect:
        userLableCodeBusiness.queryUserLabelCode(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                          || expectedResult
        new queryUserLabelCodeRequestType(new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), ["labelCodes"], 0) || new queryUserLabelCodeResponseType(new ResponseStatusType(), [new UserLabelInfo("labelCode", "labelValue")], 0, "resultMessage", Boolean.TRUE)
    }

    @Unroll
    def "query User Mkt Condition where request=#request then expect: #expectedResult"() {
        given:
        when(userLabelAiProxy.queryUserLabelCode(anyString(), any(List<String>.class))).thenReturn([new LabelInfo("labelId", "labelValue")])
        when(serviceQConfig.getValueByKeyFromQconfig(anyString())).thenReturn("getValueByKeyFromQconfigResponse")
        when(serviceQConfig.getUserMktConditions()).thenReturn([new UserMktConditionDTO()])
        when(serviceQConfig.findAPPSourceFrom(anyString())).thenReturn(true)
        when(serviceQConfig.findWeChatSourceFrom(anyString())).thenReturn(true)

        expect:
        userLableCodeBusiness.queryUserMktCondition(request) == expectedResult

        where:
        request                                                                                                                                                                                                                                                                                                                                                                                                                                                                  || expectedResult
        new queryUserMktConditionRequestType(new RestRequestHeader("lang", "host", "language", "locale", "cury", 0, 0, 0, 0, "sf", "cid", "uid", "ip", "ticket", "os", "mode", 0 as BigDecimal, "token", "globalTraceId", "rmstoken", "osver", "sign", "scookie"), new MobileRequestHead("syscode", "lang", "auth", "cid", "ctok", "cver", "sid", [new ExtensionFieldType("name", "value")], "pauth", "sauth", "appid"), "cid", "scenecode", "sourcefrom", 0, ["labelCodes"], 0) || new queryUserMktConditionResponseType(new ResponseStatusType(), 0, "resultMessage", [new JumpImageInfo("imageUrl", "jumpUrl", 1l, 0)], "abResult", Boolean.TRUE, [new UserLabelInfo("labelCode", "labelValue")], "abTestNumber")
    }
}

//Generated with love by TestMe :) Please raise issues & feature requests at: https://weirddev.com/forum#!/testme