package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.client.contract.MktMainSupportInfo
import com.ctrip.car.market.client.contract.QueryMktSupportDetailsResponseType
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsRequestType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.mkt.MktSupportProxy
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import mockit.Mocked
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification

class MktSupportDetailsBusinessTest extends Specification {

    def mktSupportProxy = Mock(MktSupportProxy)
    def business = new MktSupportDetailsBusiness(mktSupportProxy: mktSupportProxy)

    MockedStatic<com.ctrip.car.market.coupon.restful.utils.BaseUtils> mockedStatic;
    def setup() {
        mockedStatic = Mockito.mockStatic(com.ctrip.car.market.coupon.restful.utils.BaseUtils.class)
        mockedStatic.when {
            com.ctrip.car.market.coupon.restful.utils.BaseUtils.getUidByCommon(Mockito.any())
        }.thenReturn("1111")
    }
    def cleanup() {
        mockedStatic.close()
    }

    def "test getMktSupportDetails with valid request"() {
        given: "一个有效的请求"
        def request = new QueryMktSupportDetailsRequestType(
                baseRequest: new BaseRequest(uid: "123", sourceFrom: "test"),
                projectId: 1,
                taskId: 2
        )

        and: "Mock MktSupportProxy 的返回值"
        def clientResponse = new QueryMktSupportDetailsResponseType(
                bottomButtonText: "Test Button",
                taskStatus: 1,
                cashbackStatus: 2,
                mktMainSupportInfos: [
                        new MktMainSupportInfo(
                                assistTime: "2023-01-01",
                                cashbackStatus: 2,
                                avatarPictureURL: "http://example.com/avatar.jpg",
                                beInvitedUid: "456",
                                currentProcess: 1,
                                cashbackMoney: 100.0,
                                cashbackRatio: 0.5,
                                nickname: "Test User",
                                eventTarget: 50,
                                expectedCashbackTime: "2023-02-01",
                                userId: "789"
                        )
                ]
        )
        mktSupportProxy.getMktSupportDetails(_ as com.ctrip.car.market.client.contract.QueryMktSupportDetailsRequestType) >> clientResponse

        when: "调用 getMktSupportDetails 方法"
        def response = business.getMktSupportDetails(request)

        then: "验证返回结果"
        response instanceof com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType
        response.bottomButtonText == "Test Button"
        response.mktMainSupportInfos.size() == 1
        response.mktMainSupportInfos[0].nickname == "Test User"
        response.mktMainSupportInfos[0].cashbackMoney == 100.0
    }

    def "test getMktSupportDetails with invalid request"() {
        given: "一个无效的请求"
        def request = new QueryMktSupportDetailsRequestType(
                baseRequest: null,
                projectId: null,
                taskId: null
        )

        when: "调用 getMktSupportDetails 方法"
        def response = business.getMktSupportDetails(request)

        then: "验证返回结果"
        response instanceof com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType
        response.baseResponse.getCode() == ResponseUtil.fail("BaseRequest cannot be null").getCode()
    }
    def "test valid with null request"() {
        given: "一个空的请求对象"
        def request = new QueryMktSupportDetailsRequestType(taskId:  1L)
        def response = new com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType()

        when: "调用 valid 方法"
        def result = (boolean)ReflectionTestUtils.invokeMethod(business, "valid", request, response)

        then: "验证返回结果"
        !result
        response.baseResponse.getCode() == ResponseUtil.fail("Request cannot be null").getCode()
    }

    def "test valid with null baseRequest"() {
        given: "一个 baseRequest 为 null 的请求对象"
        def request = new QueryMktSupportDetailsRequestType(baseRequest: null)
        def response = new com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType()

        when: "调用 valid 方法"
        def result = (boolean)ReflectionTestUtils.invokeMethod(business, "valid", request, response)

        then: "验证返回结果"
        !result
        response.baseResponse.getCode() == ResponseUtil.fail("BaseRequest cannot be null").getCode()
    }

    def "test valid with null uid"() {
        given: "一个 uid 为 null 的请求对象"
        def request = new QueryMktSupportDetailsRequestType(
                baseRequest: new BaseRequest(uid: null)
        )
        def response = new com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType()

        when: "调用 valid 方法"
        def result = (boolean)ReflectionTestUtils.invokeMethod(business, "valid", request, response)

        then: "验证返回结果"
        !result
        response.baseResponse.getCode() == ResponseUtil.fail("Uid cannot be null").getCode()
    }

    def "test valid with null taskId and projectId"() {
        given: "一个 taskId 和 projectId 为 null 的请求对象"
        def request = new QueryMktSupportDetailsRequestType(
                baseRequest: new BaseRequest(uid: "123"),
                taskId: null,
                projectId: null
        )
        def response = new com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType()

        when: "调用 valid 方法"
        def result = (boolean)ReflectionTestUtils.invokeMethod(business, "valid", request, response)

        then: "验证返回结果"
        !result
        response.baseResponse.getCode() == ResponseUtil.fail("TaskId and ProjectId cannot be null").getCode()
    }
    def "test valid with null taskId and projectId2"() {
        given: "一个 taskId 和 projectId 为 null 的请求对象"
        def request = new QueryMktSupportDetailsRequestType(
                baseRequest: new BaseRequest(uid: "123"),
                projectId: null
        )
        def response = new com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType()

        when: "调用 valid 方法"
        def result = (boolean)ReflectionTestUtils.invokeMethod(business, "valid", request, response)

        then: "验证返回结果"
        !result
        response.baseResponse.getCode() == ResponseUtil.fail("TaskId and ProjectId cannot be null").getCode()
    }

    def "test convertRequest"() {
        given:
        def request = new QueryMktSupportDetailsRequestType(
                baseRequest: new BaseRequest(uid: "123"),
                projectId: 1L,
                taskId: 1L
        )

        when: "调用 convertRequest 方法"
        def result = (com.ctrip.car.market.client.contract.QueryMktSupportDetailsRequestType)ReflectionTestUtils.invokeMethod(business, "convertRequest", request, "1111")

        then: "验证返回结果"
        result.getUid() == "1111"
    }

    def "test convertSupport"() {
        given:
        def request = new com.ctrip.car.market.client.contract.MktMainSupportInfo(userId: "11")

        when: "调用 convertSupport 方法"
        def result = (com.ctrip.car.market.coupon.restful.contract.MktMainSupportInfo)ReflectionTestUtils.invokeMethod(business, "convertSupport", request)

        then: "验证返回结果"
        result.getUserId() == "11"
    }
}
