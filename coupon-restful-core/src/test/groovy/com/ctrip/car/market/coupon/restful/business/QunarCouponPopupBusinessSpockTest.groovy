//package com.ctrip.car.market.coupon.restful.business
//
//import com.ctrip.car.market.coupon.restful.contract.piao.QueryQunarCouponPopupRequestType
//import com.ctrip.car.market.coupon.restful.dto.QunarUserInfo
//import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.QunarCouponProxy
//import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.QunarUidServiceProxy
//import com.ctrip.car.market.coupon.restful.qconfig.PiaoQunarCouponConfig
//import com.ctrip.car.market.coupon.restful.utils.AbUtil
//import com.ctrip.di.data.abtest.client.ABTestClient
//import org.mockito.MockedStatic
//import org.mockito.Mockito
//import spock.lang.Specification
//
//
//class QunarCouponPopupBusinessSpockTest extends Specification {
//
//    def qunarCouponProxy = Mock(QunarCouponProxy)
//
//    def qunarUidServiceProxy = Mock(QunarUidServiceProxy)
//
//    def piaoQunarCouponConfig = Mock(PiaoQunarCouponConfig)
//
//    def service = new QunarCouponPopupBusiness(
//            qunarCouponProxy: qunarCouponProxy,
//            qunarUidServiceProxy: qunarUidServiceProxy,
//            piaoQunarCouponConfig: piaoQunarCouponConfig
//    )
//
//    MockedStatic<AbUtil> abUtilMockedStatic
//    MockedStatic<ABTestClient> abTestClientMockedStatic;
//
//    def setup() {
//        abTestClientMockedStatic = Mockito.mockStatic(ABTestClient.class)
//        abUtilMockedStatic = Mockito.mockStatic(AbUtil.class)
//        abUtilMockedStatic.when(() -> AbUtil.getAlternative(Mockito.anyString(), Mockito.anyString())).thenReturn("A")
//    }
//    def cleanup() {
//        abTestClientMockedStatic.close()
//        abUtilMockedStatic.close()
//    }
//
//
//    def test_queryPopup() {
//        given:
//        qunarUidServiceProxy.queryQunarUid(_ as String) >> userInfo
//        qunarCouponProxy.isNewCustomer(_ as QunarUserInfo) >> isNew
//        qunarCouponProxy.canSendCoupon(_ as QunarUserInfo) >> canSend
//        qunarCouponProxy.sendCoupon(_ as QunarUserInfo) >> send
//        piaoQunarCouponConfig.getNewMaterialUrl() >> "new"
//        piaoQunarCouponConfig.getOldMaterialUrl() >> "old"
//
//
//        expect:
//        service.queryPopup(request as QueryQunarCouponPopupRequestType).getImageUrl() == result
//
//        where:
//        request                                               | userInfo                                      | isNew | canSend | send  || result
//        new QueryQunarCouponPopupRequestType()                | null                                          | false | false   | false || null
//        new QueryQunarCouponPopupRequestType(scookie: "test") | null                                          | false | false   | false || null
//        new QueryQunarCouponPopupRequestType(scookie: "test") | new QunarUserInfo(uid: 123, userName: "test") | false | false   | false || "old"
//        new QueryQunarCouponPopupRequestType(scookie: "test") | new QunarUserInfo(uid: 123, userName: "test") | true  | false   | false || "old"
//        new QueryQunarCouponPopupRequestType(scookie: "test") | new QunarUserInfo(uid: 123, userName: "test") | true  | true    | false || "old"
//        new QueryQunarCouponPopupRequestType(scookie: "test") | new QunarUserInfo(uid: 123, userName: "test") | true  | true    | true  || "new"
//    }
//
//    def test_queryPopup2() {
//        given:
//        qunarUidServiceProxy.queryQunarUid(_ as String) >> userInfo
//        qunarCouponProxy.isNewCustomer(_ as QunarUserInfo) >> isNew
//        qunarCouponProxy.canSendCoupon(_ as QunarUserInfo) >> canSend
//        qunarCouponProxy.sendCoupon(_ as QunarUserInfo) >> send
//        piaoQunarCouponConfig.getNewMaterialUrl() >> "new"
//        piaoQunarCouponConfig.getOldMaterialUrl() >> "old"
//
//        AbUtil.getAlternative(_ as String, _ as String) >> ("A")
//
//        expect:
//        service.queryPopup(request as QueryQunarCouponPopupRequestType).getImageUrl() == result
//
//        where:
//        request                                               | userInfo                                      | isNew | canSend | send  || result
//        new QueryQunarCouponPopupRequestType()                | null                                          | false | false   | false || null
//        new QueryQunarCouponPopupRequestType(scookie: "test") | null                                          | false | false   | false || null
//        new QueryQunarCouponPopupRequestType(scookie: "test") | new QunarUserInfo(uid: 123, userName: "test") | false | false   | false || "old"
//        new QueryQunarCouponPopupRequestType(scookie: "test") | new QunarUserInfo(uid: 123, userName: "test") | true  | true    | true  || null
//    }
//}
