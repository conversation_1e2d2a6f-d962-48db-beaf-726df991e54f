package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoRecommendCityRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.RecommendCityInfo
import com.ctrip.car.market.coupon.restful.enums.SeoShark
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.google.common.collect.Maps
import org.apache.commons.collections4.CollectionUtils
import org.assertj.core.util.Sets
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoRecommendCityBusinessSpockTest extends Specification {

    def service = Mock(SeoService)

    def tripConfig = Mock(TripConfig)

    def testInstance = new SeoRecommendCityBusiness(
            service: service,
            tripConfig: tripConfig
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def "test convert"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("convert", List.class, List.class, String.class)
        testMethod.setAccessible(true)

        Map<Long, City> cityMap = Maps.newHashMap();
        cityMap.put(1L, new City().builder().id(1L).build())

        Map<Long, String> imageMap = Maps.newHashMap();
        imageMap.put(1L, "test")

        given:
        service.getCityOrderNum(_ as List<SeoHotDestinatioinfoDO>) >> Maps.newHashMap()
        service.getCity(_ as List<Long>, _ as String) >> cityMap
        service.getCountryName(_ as Integer, _ as String) >> "test"
        tripConfig.getMaxRecommendCityCount() >> 10

        expect:
        CollectionUtils.isNotEmpty(((List<RecommendCityInfo>) testMethod.invoke(testInstance, cityList as List<SeoHotCityinfoDO>, poiList as List<SeoHotDestinatioinfoDO>, "en-US"))) == result

        where:
        cityList                                                       | poiList                        || result
        [new SeoHotCityinfoDO(cityId: 1, countryId: 1, image: "test")] | [new SeoHotDestinatioinfoDO()] || true
        [new SeoHotCityinfoDO(cityId: 2, countryId: 1)]                | [new SeoHotDestinatioinfoDO()] || false
    }

    def "test queryCity"() {
        Set<Integer> citySet = Sets.newHashSet()
        citySet.add(1)

        Map<Long, City> cityMap = Maps.newHashMap();
        cityMap.put(1L, new City().builder().id(1L).build())

        Map<Long, String> imageMap = Maps.newHashMap();
        imageMap.put(1L, "test")

        given:
        service.queryHotDestination(_ as Integer, null, null, null) >> destinatioinfoList
        service.queryHotCity(_ as Integer) >> cityList
        service.getCountryName(_ as Integer, _ as String) >> "test"
        tripConfig.getTwCityList() >> citySet
        service.getCityOrderNum(_ as List<SeoHotDestinatioinfoDO>) >> Maps.newHashMap()
        service.getCity(_ as List<Long>, _ as String) >> cityMap
        tripConfig.getMaxRecommendCityCount() >> 10

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryCity(request as QuerySeoRecommendCityRequestType).getCityList()) == result

        where:
        request                                                                                            | destinatioinfoList                                      | cityList                                                         || result
        new QuerySeoRecommendCityRequestType()                                                             | []                                                      | []                                                               || false
        new QuerySeoRecommendCityRequestType(baseRequest: new BaseRequest(locale: "zh-HK"))                | []                                                      | []                                                               || false
        new QuerySeoRecommendCityRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), countryId: 1L) | []                                                      | []                                                               || false
        new QuerySeoRecommendCityRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), countryId: 1L) | [new SeoHotDestinatioinfoDO(countryId: 1L, cityId: 1L)] | []                                                               || false
        new QuerySeoRecommendCityRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), countryId: 1L) | [new SeoHotDestinatioinfoDO(countryId: 1L, cityId: 1L)] | [new SeoHotCityinfoDO(countryId: 1L, cityId: 1L, image: "test")] || true
        new QuerySeoRecommendCityRequestType(baseRequest: new BaseRequest(locale: "zh-TW"), countryId: 1L) | [new SeoHotDestinatioinfoDO(countryId: 1L, cityId: 1L)] | [new SeoHotCityinfoDO(countryId: 1L, cityId: 1L)]                || false
    }
}
