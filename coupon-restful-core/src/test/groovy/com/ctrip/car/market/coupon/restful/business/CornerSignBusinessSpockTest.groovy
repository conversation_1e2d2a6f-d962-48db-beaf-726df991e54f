package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.QueryHomeIndexConfigRequestType
import com.ctrip.car.market.coupon.restful.contract.QueryHomeIndexConfigResponseType
import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig
import spock.lang.Specification

class CornerSignBusinessSpockTest extends Specification {

    def couponServiceQConfig = Mock(CouponServiceQConfig)

    def cornerSignBusiness = new CornerSignBusiness(couponServiceQConfig: couponServiceQConfig)

    def "test queryHomeIndexConfig with union 0"() {
        given: "一个请求对象，union 为 0"
        def request = new QueryHomeIndexConfigRequestType(union: 0)

        and: "Mock CouponServiceQConfig 的返回值"
        couponServiceQConfig.getCtripHomeConfig() >> "Ctrip Home Config"

        when: "调用 queryHomeIndexConfig 方法"
        def response = cornerSignBusiness.queryHomeIndexConfig(request)

        then: "验证返回结果"
        response instanceof QueryHomeIndexConfigResponseType
        response.resultCode == ResultOpenStatus.SUCCESS.getCode()
        response.resultMessage == ResultOpenStatus.SUCCESS.getMessage()
        response.indexConfig == "Ctrip Home Config"
    }

    def "test queryHomeIndexConfig with union 1"() {
        given: "一个请求对象，union 为 1"
        def request = new QueryHomeIndexConfigRequestType(union: 1)

        and: "Mock CouponServiceQConfig 的返回值"
        couponServiceQConfig.getQunarHomeConfig() >> "Qunar Home Config"

        when: "调用 queryHomeIndexConfig 方法"
        def response = cornerSignBusiness.queryHomeIndexConfig(request)

        then: "验证返回结果"
        response instanceof QueryHomeIndexConfigResponseType
        response.resultCode == ResultOpenStatus.SUCCESS.getCode()
        response.resultMessage == ResultOpenStatus.SUCCESS.getMessage()
        response.indexConfig == "Qunar Home Config"
    }
}