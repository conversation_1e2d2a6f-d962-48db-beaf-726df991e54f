package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.LocationInfo
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoHotLocationRequestType
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.dcs.geo.domain.value.City
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.google.common.collect.Maps
import org.apache.commons.collections.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoHotLocationBusinessSpockTest extends Specification {

    def service = Mock(SeoService)

    def tripConfig = Mock(TripConfig)

    def testInstance = new SeoHotLocationBusiness(
            service: service,
            tripConfig: tripConfig
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def "test checkRequest"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QuerySeoHotLocationRequestType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as QuerySeoHotLocationRequestType)) == result

        where:
        request                                                                       || result
        new QuerySeoHotLocationRequestType()                                          || false
        new QuerySeoHotLocationRequestType(baseRequest: new BaseRequest())            || false
        new QuerySeoHotLocationRequestType(baseRequest: new BaseRequest(), cityId: 1) || true
    }

    def "test convert"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("convert", List.class, List.class, Integer.class, String.class, Integer.class)
        testMethod.setAccessible(true)

        given:
        service.getCountryName(_ as Integer, _ as String) >> "test"
        Map<Long, City> cityMap = Maps.newHashMap()
        cityMap.put(1L, new City())
        service.getCity(_ as List<Long>, _ as String) >> cityMap
        service.getSiteUrl(_ as String, _ as String) >> "test"
        service.getCity(_ as Integer) >> cityInfo
        tripConfig.getTwCityList() >> twList
        service.getProvinceName(_ as Integer, _ as String) >> "test"
        service.getCityOrderNum(_ as List<SeoHotDestinatioinfoDO>) >> Maps.newHashMap();

        expect:
        ((List<LocationInfo>) testMethod.invoke(testInstance, cityList as List<SeoHotCityinfoDO>, destanationList as List<SeoHotDestinatioinfoDO>, 1, locale as String, 53)).isEmpty() == result

        where:
        twList | cityInfo                  | locale  | cityList                                       | destanationList                                        || result
        [1, 2] | null                      | "en-US" | [new SeoHotCityinfoDO(cityId: 1, url: "test")] | [new SeoHotDestinatioinfoDO(cityId: 1, orderNum: 111)] || false
        [1, 2] | new City(provinceId: 53L) | "zh-TW" | [new SeoHotCityinfoDO(cityId: 1, url: "test")] | [new SeoHotDestinatioinfoDO(cityId: 1, orderNum: 111)] || false
        [2]    | new City(provinceId: 58L) | "zh-TW" | [new SeoHotCityinfoDO(cityId: 1, url: "test")] | [new SeoHotDestinatioinfoDO(cityId: 1, orderNum: 111)] || false
    }

    def "test queryHotLocation"() {
        given:
        service.getCountryName(_ as Integer, _ as String) >> "test"
        Map<Long, City> cityMap = Maps.newHashMap()
        cityMap.put(1L, new City())
        service.getCity(_ as List<Long>, _ as String) >> cityMap
        service.getSiteUrl(_ as String, _ as String) >> "test"
        service.queryHotDestination(_ as Integer, _ as Integer, _ as Integer, _ as String) >> destanationList
        service.queryHotCity(_ as Integer) >> [new SeoHotCityinfoDO(cityId: 1, url: "test")]
        service.getCity(_ as Integer) >> new City(provinceId: 53L)
        tripConfig.getTwCityList() >> [1, 2]
        service.getProvinceName(_ as Integer, _ as String) >> "test"

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryHotLocation(request as QuerySeoHotLocationRequestType).getLocationList()) == result

        where:
        request                                                                                                                             | destanationList                                                      || result
        new QuerySeoHotLocationRequestType()                                                                                                | []                                                                   || false
        new QuerySeoHotLocationRequestType(baseRequest: new BaseRequest(locale: "en-US"), cityId: 1)                                        | []                                                                   || false
        new QuerySeoHotLocationRequestType(baseRequest: new BaseRequest(locale: "en-US"), cityId: 0, countryId: 1, poiType: 1, poiCode: "") | [new SeoHotDestinatioinfoDO(cityId: 1, orderNum: 111, countryId: 1)] || true

    }
}
