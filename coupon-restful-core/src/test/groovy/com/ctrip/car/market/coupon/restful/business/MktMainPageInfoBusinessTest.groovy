package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.client.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.ActivityStatusInfoDTO
import com.ctrip.car.market.coupon.restful.contract.AnimationInfo
import com.ctrip.car.market.coupon.restful.contract.CancelBindOrderInfo
import com.ctrip.car.market.coupon.restful.contract.LotteryProgressInfo
import com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoRequestType
import com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoResponseType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.SDMarketingServiceProxy
import com.ctrip.car.market.coupon.restful.utils.BaseUtils
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.mockito.MockedStatic
import org.mockito.Mockito
import org.springframework.test.util.ReflectionTestUtils
import retrofit2.http.HEAD
import spock.lang.Specification

class MktMainPageInfoBusinessTest extends Specification{

    def sdMarketingServiceProxy = Mock(SDMarketingServiceProxy)
    def business = new MktMainPageInfoBusiness(sdMarketingServiceProxy: sdMarketingServiceProxy)

    MockedStatic<BaseUtils> mockedStatic

    def setup() {
        mockedStatic = Mockito.mockStatic(BaseUtils.class)
        mockedStatic.when {
            BaseUtils.getUidByCommon(Mockito.any())
        }.thenReturn("1111")
    }

    def cleanup() {
        mockedStatic.close()
    }

    def "test queryMktMainPageInfo with valid request"() {
        given: "一个有效的请求"
        def request = new QueryMktMainPageInfoRequestType(
                baseRequest: new com.ctrip.car.market.coupon.restful.contract.BaseRequest(
                        requestId: "req123",
                        channelId: 1,
                        sourceFrom: "sourceTest"
                ),
                head: new MobileRequestHead(cid: "cid123"),
                projectId: 1L,
                taskId: 2L
        )

        and: "Mock SDMarketingServiceProxy 的返回值"
        def clientResponse = new com.ctrip.car.market.client.contract.QueryMktMainPageInfoResponseType(
                orderId: 123,
                orderStatus: 1,
                taskReceiveTime: null,
                cashbackIsAmount: true,
                cashbackAmoutLabel: "100元",
                cashbackLabel: "返现",
                taskStatusToast: "任务完成",
                invitationProgress: "50%"
        )
        sdMarketingServiceProxy.queryMktMainPageInfo(_ as com.ctrip.car.market.client.contract.QueryMktMainPageInfoRequestType) >> clientResponse

        when: "调用 queryMktMainPageInfo 方法"
        def response = business.queryMktMainPageInfo(request)

        then: "验证返回结果"
        response instanceof QueryMktMainPageInfoResponseType
        response.baseResponse.getCode()  == ResponseUtil.success().getCode()
        response.orderId == 123
    }

    def "test queryMktMainPageInfo with invalid request"() {
        given: "一个无效的请求"
        def request = new QueryMktMainPageInfoRequestType(
                baseRequest: null,
                head: null,
                projectId: null,
                taskId: null
        )

        when: "调用 queryMktMainPageInfo 方法"
        def response = business.queryMktMainPageInfo(request)

        then: "验证返回结果"
        response instanceof QueryMktMainPageInfoResponseType
        response.baseResponse.getCode() == ResponseUtil.fail("parameter invalid").getCode()
    }

    def "test convertSupport"() {
        given:
        def request = new com.ctrip.car.market.client.contract.QueryMktMainPageInfoResponseType(activityStatusInfo:
        new com.ctrip.car.market.client.contract.ActivityStatusInfoDTO(cancelBindOrderInfo: new com.ctrip.car.market.client.contract.CancelBindOrderInfo()),
        lotteryProgressInfo: new com.ctrip.car.market.client.contract.LotteryProgressInfo(), animationInfo:  new com.ctrip.car.market.client.contract.AnimationInfo())
        def responseType = new  com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoResponseType()

        when: "调用 convertSupport 方法"
        def result = (com.ctrip.car.market.client.contract.BaseRequest) ReflectionTestUtils.invokeMethod(business, "resultConvert", request, responseType)

        then: "验证返回结果"
        result == null
    }

}
