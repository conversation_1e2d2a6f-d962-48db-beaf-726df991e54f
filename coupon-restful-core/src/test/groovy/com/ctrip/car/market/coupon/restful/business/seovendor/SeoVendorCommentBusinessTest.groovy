package com.ctrip.car.market.coupon.restful.business.seovendor

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListRequestType
import com.ctrip.car.market.coupon.restful.dto.VendorLogoItem
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.CommonQueryCommentSummaryResponseType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CommentAggregationInfoType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CommentDetailInfoType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.CtripUserInfoType
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.TagAggregationInfoType
import org.apache.commons.collections.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoVendorCommentBusinessTest extends Specification {

    def ucpServiceProxy = Mock(UcpServiceProxy)

    def seoService = Mock(SeoService)

    def seoVendorCache = Mock(SeoVendorCache)

    def tripConfig = Mock(TripConfig)

    def testInstance = new SeoVendorCommentBusiness(
            ucpServiceProxy: ucpServiceProxy,
            seoService: seoService,
            seoVendorCache: seoVendorCache,
            tripConfig: tripConfig
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def test_vendorPage() {
        given:
        seoVendorCache.queryVendor(_ as String) >> new SeoHotVendorDO(vendorId: "SD0001")
        seoService.queryVendorTop3City(_ as String) >> [new SeoHotVendorInformationDO(cityId: 1), new SeoHotVendorInformationDO(cityId: 2), new SeoHotVendorInformationDO(cityId: 3)]
        ucpServiceProxy.queryVendorCityComment(_ as String, _ as String, _ as Integer) >> new CommonQueryCommentSummaryResponseType(comments: [new CommentDetailInfoType(userInfo: new CtripUserInfoType(), commentTime: System.currentTimeMillis(), extInfoMap: new HashMap<String, String>())
                                                                                                                                               , new CommentDetailInfoType(userInfo: new CtripUserInfoType(), commentTime: System.currentTimeMillis(), extInfoMap: new HashMap<String, String>())
                                                                                                                                               , new CommentDetailInfoType(userInfo: new CtripUserInfoType(), commentTime: System.currentTimeMillis())])
        seoVendorCache.queryVendorCommentScore(_ as String) >> vendorCommentScoreDO
        tripConfig.getVendorLogoList() >> [new VendorLogoItem(vendorCode: "SD0001", logo: "test")]

        expect:
        CollectionUtils.isNotEmpty(testInstance.vendorPage(request as QuerySeoCommentListRequestType, new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test")).getCommentList()) == result
        testInstance.queryComment(new QuerySeoCommentListRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001"))

        where:
        request                                                                                                 | vendorCommentScoreDO   || result
        new QuerySeoCommentListRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001") | null                    | true
        new QuerySeoCommentListRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001") | getVendorCommentScore() | true
    }

    def getVendorCommentScore() {
        return new SeoVendorCommentScoreDO(totalCount: 1, socre: BigDecimal.TWO, subItemScore: "[{\"subItemConfigId\":83,\"scoreAvg\":4.530},{\"subItemConfigId\":84,\"scoreAvg\":4.607},{\"subItemConfigId\":85,\"scoreAvg\":4.838},{\"subItemConfigId\":86,\"scoreAvg\":4.778}]")
    }

    def test_cityPage() {
        seoVendorCache.queryVendor(_ as String) >> new SeoHotVendorDO(vendorId: "SD0001")
        ucpServiceProxy.queryVendorCityComment(_ as String, _ as String, _ as Integer) >> getVendorComment()
        tripConfig.getVendorLogoList() >> [new VendorLogoItem(vendorCode: "SD0001", logo: "test")]

        expect:
        CollectionUtils.isNotEmpty(testInstance.cityPage(request as QuerySeoCommentListRequestType, new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test")).getCommentList()) == result

        where:
        request                                                                                                            || result
        new QuerySeoCommentListRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), vendorCode: "SD0001", cityId: 1) || true

    }

    def getVendorComment() {
        return new CommonQueryCommentSummaryResponseType(commentAggregation:
                new CommentAggregationInfoType(totalCount: 1, scoreAvg: BigDecimal.TWO,
                        subItemTags: [new TagAggregationInfoType(tagName: "83", scoreAvg: BigDecimal.TWO)
                                      , new TagAggregationInfoType(tagName: "84", scoreAvg: BigDecimal.TWO),
                                      new TagAggregationInfoType(tagName: "85", scoreAvg: BigDecimal.TWO),
                                      new TagAggregationInfoType(tagName: "86", scoreAvg: BigDecimal.TWO)]), comments: [new CommentDetailInfoType(userInfo: new CtripUserInfoType(), commentTime: System.currentTimeMillis())])
    }
}
