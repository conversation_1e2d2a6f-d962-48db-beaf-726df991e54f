package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.GetTripHomepageDevInfoRequestType
import com.ctrip.car.market.coupon.restful.contract.seo.PlatformAdvantage
import com.ctrip.car.market.coupon.restful.dto.CustomerConfigDTO
import com.ctrip.car.market.coupon.restful.dto.SeoHotVendorDTO
import com.ctrip.car.market.coupon.restful.dto.VendorLogoItem
import com.ctrip.car.market.coupon.restful.pojo.PlatformAdvantageConfig
import com.ctrip.car.market.coupon.restful.pojo.TripHomeDevInfoQO
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.coupon.restful.qconfig.TripHomeDevInfoConfig
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoHomepageDevInfoBusinessSpockTest extends Specification {

    def configs = Mock(TripHomeDevInfoConfig)

    def tripConfig = Mock(TripConfig)

    def testInstance = new SeoHomepageDevInfoBusiness(configs: configs, tripConfig: tripConfig)

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def "tes buildResponse"() {
        given:
        configs.getTripHomeDevInfo() >> devInfo
        tripConfig.getSeoHotVendor() >> new SeoHotVendorDTO(defaultConfig: "SD0001", customerConfig: [new CustomerConfigDTO(type: 1, id: 1, vendorList: "SD0001")])
        tripConfig.getVendorLogoList() >> [new VendorLogoItem(vendorCode: "SD0001", logo: "test")]

        expect:
        testInstance.buildResponse(request as GetTripHomepageDevInfoRequestType).getHotVendors() != null == result

        where:
        request                                                                                                       | devInfo                                                                                                                                                                                  || result
        new GetTripHomepageDevInfoRequestType(baseRequest: new BaseRequest(locale: "zh-HK"))                          | null                                                                                                                                                                                     || true
        new GetTripHomepageDevInfoRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), countryId: 1, cityId: 1) | new TripHomeDevInfoQO(platformAdvantageConfigs: [new PlatformAdvantageConfig(locale: "zh-HK", platformAdvantages: [new PlatformAdvantage(sortNum: 1, content: "test", title: "test")])]) || true
    }
}
