package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoVehicleListRequestType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO
import com.ctrip.car.osd.shopping.api.entity.*
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.apache.commons.collections.CollectionUtils
import org.mockito.MockedStatic
import qunar.metrics.Metrics
import spock.lang.Specification
import static org.mockito.Mockito.*

class SeoVehicleBusinessSpockTest extends Specification {

    def service = Mock(SeoService)

    def osdShoppingProxy = Mock(OsdShoppingProxy)

    def tripConfig = Mock(TripConfig)

    def testInstance = new SeoVehicleBusiness(
            service: service,
            osdShoppingProxy: osdShoppingProxy,
            tripConfig: tripConfig
    )

    MockedStatic<Metrics> metricsMockedStatic

    def setup() {
        metricsMockedStatic = mockStatic(Metrics.class)
    }

    def cleanup() {
        metricsMockedStatic.close()
    }

    def "test checkRequest"() {
        def testMethod = testInstance.getClass().getDeclaredMethod("checkRequest", QuerySeoVehicleListRequestType.class)
        testMethod.setAccessible(true)

        expect:
        ((boolean) testMethod.invoke(testInstance, request as QuerySeoVehicleListRequestType)) == result

        where:
        request                                                                       || result
        new QuerySeoVehicleListRequestType()                                          || false
        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest())            || false
        new QuerySeoVehicleListRequestType(baseRequest: new BaseRequest(), cityId: 1) || true
    }

    def "test queryVehicleList"() {
        given:
        tripConfig.getPcUrl() >> "test"
        tripConfig.getMaxVehicleCount() >> 6
        service.queryHotDestinationFirst(_ as Integer, _ as Integer, _ as Integer, _ as String) >> hotDestinationInfo
        osdShoppingProxy.queryRecomdProducts(_ as QueryRecomdProductsRequestType, _ as Boolean) >> vehicleResponse
        service.currencyString(_ as BigDecimal, _ as LanguageLocaleEnum, _ as CurrencyEnum) >> "test"

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryVehicleList(request as QuerySeoVehicleListRequestType).getVehicleList()) == result

        where:
        request                                                                                         | hotDestinationInfo                                                               | vehicleResponse                                              || result
        new QuerySeoVehicleListRequestType()                                                            | null                                                                             | null                                                         || false
        new QuerySeoVehicleListRequestType(baseRequest:
                new BaseRequest(locale: "en-US"), countryId: 1, cityId: 1, poiType: 1, poiCode: "test") | null                                                                             | null                                                         || false

        new QuerySeoVehicleListRequestType(baseRequest:
                new BaseRequest(locale: "en-US"), head: new MobileRequestHead(),
                countryId: 1, cityId: 1, poiType: 1, poiCode: "test")                                   | new SeoHotDestinatioinfoDO(countryId: 1, cityId: 1, poiType: 1, poiCode: "test") | null                                                         || false

        new QuerySeoVehicleListRequestType(baseRequest:
                new BaseRequest(locale: "en-US"), head: new MobileRequestHead(),
                countryId: 1, cityId: 1, poiType: 1, poiCode: "test")                                   | new SeoHotDestinatioinfoDO(countryId: 1, cityId: 1, poiType: 1, poiCode: "test") | null                                                         || false

        new QuerySeoVehicleListRequestType(baseRequest:
                new BaseRequest(locale: "en-US"), head: new MobileRequestHead(),
                countryId: 1, cityId: 1, poiType: 1, poiCode: "test")                                   | new SeoHotDestinatioinfoDO(countryId: 1, cityId: 1, poiType: 1, poiCode: "test") | new QueryRecomdProductsResponseType(
                recomdProductResList: [new RecomdProductRes(products: [new RecomdProduct(vehicle: new RecomdVehicleDTO(vehicleKey: "test"), labels: [new OsdLabelInfo(code: "test", name: "test", extDesc: "test")],
                        price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.ONE, currentOriginalDailyPrice: BigDecimal.TEN, suffix: "test", prefix: "test"))], returnLocation: new LocationRequestInfo(), pickupLocation: new LocationRequestInfo())]
        )                                                                                                                                                                                                                                                 || true
    }
}
