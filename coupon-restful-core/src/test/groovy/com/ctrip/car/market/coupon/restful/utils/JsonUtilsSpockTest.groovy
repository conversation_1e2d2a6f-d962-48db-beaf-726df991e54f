package com.ctrip.car.market.coupon.restful.utils

import org.apache.commons.lang3.StringUtils;
import spock.lang.Specification;

class JsonUtilsSpockTest extends Specification {

    static private class JsonTest {
        int code
        String msg
    }

    def "test_toJson"() {
        setup: ""

        when: ""
        def result = JsonUtils.toJson(new JsonTest(code: 1, msg: ""))

        then: ""
        StringUtils.isNotBlank(result)
    }

    def "test_toObject"() {
        setup: ""

        when: ""
        def result = JsonUtils.toObject("{}", JsonTest.class)

        then: ""
        Objects.nonNull(result)
    }

    def "test_convertObject2Json"() {
        setup: ""

        when: ""
        def result = JsonUtils.convertObject2Json(new JsonTest(code: 1, msg: ""))

        then: ""
        Objects.nonNull(result)
    }

    def "test_convertJson2Object"() {
        setup: ""

        when: ""
        def result = JsonUtils.convertJson2Object("{}", JsonTest.class)

        then: ""
        Objects.nonNull(result)
    }

}
