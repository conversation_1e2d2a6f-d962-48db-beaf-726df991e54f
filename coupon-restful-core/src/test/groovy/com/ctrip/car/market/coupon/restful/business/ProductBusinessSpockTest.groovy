package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.QueryTripSeoProductsRequestType
import com.ctrip.car.market.coupon.restful.contract.QueryTripSeoProductsResponseType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.MockitoAnnotations
import spock.lang.Specification

import static org.mockito.Matchers.any
import static org.mockito.Mockito.when

class ProductBusinessSpockTest extends Specification {

    def carCrossRecommendedServiceProxy = Mock(CarCrossRecommendedServiceProxy)
    def productBusiness = new ProductBusiness(carCrossRecommendedServiceProxy: carCrossRecommendedServiceProxy)

    def setup() {
    }

    def "queryTripSeoProducts"() {
        given:
        carCrossRecommendedServiceProxy.queryTripSeoProducts(_ as QueryTripSeoProductsRequestType) >> new QueryTripSeoProductsResponseType();

        when:
        QueryTripSeoProductsResponseType responseType = productBusiness.queryTripSeoProducts(new QueryTripSeoProductsRequestType(cityId: 1, baseRequest: new BaseRequest(sourceCountryId: 1)));
        productBusiness.queryTripSeoProducts(new QueryTripSeoProductsRequestType(baseRequest: new BaseRequest(sourceCountryId: 1)));
        productBusiness.queryTripSeoProducts(new QueryTripSeoProductsRequestType(cityId: 1));
        productBusiness.queryTripSeoProducts(new QueryTripSeoProductsRequestType(cityId: 1, baseRequest: new BaseRequest()));

        then:
        responseType != null;
    }



}
