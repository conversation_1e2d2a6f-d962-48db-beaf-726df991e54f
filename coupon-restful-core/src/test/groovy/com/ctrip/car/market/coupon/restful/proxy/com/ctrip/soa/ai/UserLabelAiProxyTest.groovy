package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai

import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctrip.tour.ai.userlabelservice.TourAiUserLabelServiceClient
import org.mockito.MockedStatic
import org.mockito.Mockito
import soa.ctrip.com.tour.ai.user.label.BatchUserLabelInfoRequestType
import soa.ctrip.com.tour.ai.user.label.BatchUserLabelInfoResponseType
import soa.ctrip.com.tour.ai.user.label.LabelInfo
import spock.lang.*

class UserLabelAiProxyTest extends Specification {
/*
    def tourAiUserLabelServiceClient = Mock(TourAiUserLabelServiceClient)

    def userLabelAiProxy = new UserLabelAiProxy()

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    @Unroll
    def "judge Is New Guest where uid=#uid and labelIds=#labelIds then expect: #expectedResult"() {

        given:
        tourAiUserLabelServiceClient.getBatchUserLabelInfos(_ as BatchUserLabelInfoRequestType) >> new BatchUserLabelInfoResponseType(labelInfos: [new LabelInfo(labelValue: "true")])

        expect:
        userLabelAiProxy.judgeIsNewGuest(uid, labelIds) == expectedResult

        where:
        uid   | labelIds     || expectedResult
        "uid" | ["labelIds"] || true
    }

    @Unroll
    def "query User Label Code where uid=#uid and labelIds=#labelIds then expect: #expectedResult"() {
        given:
        tourAiUserLabelServiceClient.getBatchUserLabelInfos(_ as BatchUserLabelInfoRequestType) >> new BatchUserLabelInfoResponseType(labelInfos: [new LabelInfo(labelValue: "true")])
        expect:
        userLabelAiProxy.queryUserLabelCode(uid, labelIds) == expectedResult

        where:
        uid   | labelIds     || expectedResult
        "uid" | ["labelIds"] || [new LabelInfo(labelValue: "true")]
    }*/
}

