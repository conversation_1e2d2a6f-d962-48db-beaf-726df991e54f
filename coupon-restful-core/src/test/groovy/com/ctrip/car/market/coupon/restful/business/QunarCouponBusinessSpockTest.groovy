package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.client.contract.CouponDataDto
import com.ctrip.car.market.client.contract.CouponTabDto
import com.ctrip.car.market.client.contract.QueryQunarCouponListRequestType
import com.ctrip.car.market.client.contract.QueryQunarCouponListResponseType
import com.ctrip.car.market.client.contract.QunarCouponItem
import com.ctrip.car.market.client.contract.QunarCouponItemValue
import com.ctrip.car.market.coupon.restful.contract.QueryQunarCouponRequestType
import com.ctrip.car.market.coupon.restful.dto.QunarUserInfo
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.QunarUidServiceProxy
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.SDMarketingServiceProxy
import spock.lang.Specification

class QunarCouponBusinessSpockTest extends Specification {

    def sdMarketingServiceProxy = Mock(SDMarketingServiceProxy)

    def qunarUidServiceProxy = Mock(QunarUidServiceProxy)

    def service = new QunarCouponBusiness(sdMarketingServiceProxy: sdMarketingServiceProxy, qunarUidServiceProxy: qunarUidServiceProxy)

    def "test queryQunarCoupon"() {
        given:
        qunarUidServiceProxy.queryQunarUid(_ as String) >> userInfo
        sdMarketingServiceProxy.queryQunarCouponList(_ as QueryQunarCouponListRequestType) >> couponInfo

        expect:
        service.queryQunarCoupon(request as QueryQunarCouponRequestType).getData() != null == result

        where:
        request                                          | userInfo                    | couponInfo                                                                                                                                                         || result
        new QueryQunarCouponRequestType()                | null                        | null                                                                                                                                                               || false
        new QueryQunarCouponRequestType(scookie: "test") | new QunarUserInfo(uid: 123) | null                                                                                                                                                               || false
        new QueryQunarCouponRequestType(scookie: "test") | new QunarUserInfo(uid: 123) | new QueryQunarCouponListResponseType(data: new CouponDataDto(tab: new CouponTabDto(), couponList: [new QunarCouponItem(couponValue: new QunarCouponItemValue())])) || true
    }
}
