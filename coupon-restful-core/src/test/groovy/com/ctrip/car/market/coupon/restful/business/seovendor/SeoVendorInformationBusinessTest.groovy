package com.ctrip.car.market.coupon.restful.business.seovendor

import com.ctrip.car.market.coupon.restful.business.SeoService
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache
import com.ctrip.car.market.coupon.restful.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationRequestType
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes
import com.ctrip.car.osd.shopping.api.entity.RecomdVehicleDTO
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.apache.commons.collections.CollectionUtils
import org.mockito.MockedStatic
import org.mockito.Mockito
import spock.lang.Specification

class SeoVendorInformationBusinessTest extends Specification {

    def seoVendorCache = Mock(SeoVendorCache)

    def service = Mock(SeoService)

    def seoVendorVehicleBusiness = Mock(SeoVendorVehicleBusiness)

    def testInstance = new SeoVendorInformationBusiness(
            seoVendorCache: seoVendorCache,
            service: service,
            seoVendorVehicleBusiness: seoVendorVehicleBusiness
    )

    MockedStatic<Shark> sharkMockedStatic
    MockedStatic<SharkInitializer> sharkInitializerMockedStatic

    def setup() {
        sharkInitializerMockedStatic = Mockito.mockStatic(SharkInitializer.class)
        sharkMockedStatic = Mockito.mockStatic(Shark.class)
        sharkMockedStatic.when { Shark.getByLocale(_ as String, _ as String) }.thenReturn("test")
    }

    def cleanup() {
        sharkInitializerMockedStatic.close()
        sharkMockedStatic.close()
    }

    def test_queryInformation() {
        given:
        seoVendorCache.queryVendorCity(_ as String, _ as Integer) >> new SeoHotVendorCityDO(vendorId: "SD0001", cityId: 1)
        seoVendorCache.queryVendor(_ as String) >> new SeoHotVendorDO(vendorId: "SD0001", vendorName: "test")
        service.queryVendorCity(_ as String, _ as Integer) >> new SeoHotVendorInformationDO(vendorCode: "SD0001", cityId: 1, tenancy: 2, vehicleGroupId: 2, vehicleId: 1L)
        service.queryVendorHotCity(_ as String) >> new SeoHotVendorInformationDO(vendorCode: "SD0001", cityId: 1, tenancy: 2, vehicleGroupId: 2, vehicleId: 1L)
        service.getVehicleGroupName(_ as String, _ as String, _ as String) >> "test"
        service.queryVendorHotCityDefault(_ as String) >> new SeoHotVendorInformationDO(vendorCode: "SD0001", cityId: 1, tenancy: 2, vehicleGroupId: 2, vehicleId: 1L)
        service.queryVendorHotCityDefault(_ as String, _ as Integer) >> new SeoHotVendorInformationDO(vendorCode: "SD0001", cityId: 1, tenancy: 2, vehicleGroupId: 2, vehicleId: 1L)
        seoVendorVehicleBusiness.queryVehicle(_ as BaseRequest, _ as MobileRequestHead, _ as List<SeoHotVendorInformationDO>, true) >> getVehicleResponse()
        service.currencyString(_ as BigDecimal, _ as LanguageLocaleEnum, _ as CurrencyEnum) >> "test"

        expect:
        CollectionUtils.isNotEmpty(testInstance.queryInformation(request as QuerySeoInformationRequestType).getInformationList()) == result

        where:
        request                                                                                                                                           || result
        new QuerySeoInformationRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), head: new MobileRequestHead(), vendorCode: "SD0001")            || true
        new QuerySeoInformationRequestType(baseRequest: new BaseRequest(locale: "zh-HK"), head: new MobileRequestHead(), vendorCode: "SD0001", cityId: 1) || true
    }

    def getVehicleResponse() {
        return [new QueryRecomdProductsResponseType(recomdProductResList: [new RecomdProductRes(products: [new RecomdProduct(vehicle: new RecomdVehicleDTO(groupCode: "2"), price: new RecomdPriceDTO(currentDailyPrice: BigDecimal.ONE, currentOriginalDailyPrice: BigDecimal.TWO))])])]
    }
}
