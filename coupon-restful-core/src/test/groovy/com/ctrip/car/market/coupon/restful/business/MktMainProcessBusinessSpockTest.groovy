package com.ctrip.car.market.coupon.restful.business

import com.ctrip.car.market.client.contract.BaseRequest
import com.ctrip.car.market.coupon.restful.contract.MktMainProcessInfo
import com.ctrip.car.market.coupon.restful.contract.QueryMktMainProcessInfoRequestType
import com.ctrip.car.market.coupon.restful.contract.QueryMktMainProcessInfoResponseType
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.mkt.MktSupportProxy
import com.ctrip.car.market.coupon.restful.utils.BaseUtils
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil
import com.ctriposs.baiji.rpc.mobile.common.types.ExtensionFieldType
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead
import org.springframework.test.util.ReflectionTestUtils
import spock.lang.Specification

class MktMainProcessBusinessSpockTest extends Specification {

    def mktSupportProxy = Mock(MktSupportProxy)
    def business = new MktMainProcessBusiness()

    def setup() {
        business.mktSupportProxy = mktSupportProxy
    }

    def "test queryMktMainProcessInfo with valid request and successful response"() {
        given: "一个有效的请求"
        def head = createMobileRequestHead("12345", "test-cid")
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                scenarioCode: 0,
                cid: "backup-cid"
        )

        and: "Mock SOA服务返回成功响应"
        def soaResponse = createSuccessfulSoaResponse()
        mktSupportProxy.getMktMainProcessInfo(_ as com.ctrip.car.market.client.contract.QueryMktMainProcessInfoRequestType) >> soaResponse

        when: "调用 queryMktMainProcessInfo 方法"
        def response = business.queryMktMainProcessInfo(request)

        then: "验证返回结果"
        response != null
        response instanceof QueryMktMainProcessInfoResponseType
        response.baseResponse.code == "0"
        response.baseResponse.message == "success"
        response.mktMainProcessInfo != null
        response.mktMainProcessInfo.showPopup == true
        response.mktMainProcessInfo.url == "http://test.com"
        response.mktMainProcessInfo.imageUrl == "http://test.com/image.jpg"
    }

    def "test queryMktMainProcessInfo with no login user"() {
        given: "一个没有登录用户的请求"
        def head = createMobileRequestHead(null, "test-cid")
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                scenarioCode: 0
        )

        when: "调用 queryMktMainProcessInfo 方法"
        def response = business.queryMktMainProcessInfo(request)

        then: "验证返回失败结果"
        response != null
        response.baseResponse.code == "-1"
        response.baseResponse.message == "no login"
        response.mktMainProcessInfo.showPopup == false
    }

    def "test queryMktMainProcessInfo with null scenario code"() {
        given: "一个没有场景代码的请求"
        def head = createMobileRequestHead("12345", "test-cid")
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                scenarioCode: null
        )

        when: "调用 queryMktMainProcessInfo 方法"
        def response = business.queryMktMainProcessInfo(request)

        then: "验证返回失败结果"
        response != null
        response.baseResponse.code == "-1"
        response.baseResponse.message == "no scenario code"
        response.mktMainProcessInfo.showPopup == false
    }

    def "test queryMktMainProcessInfo with null soa response"() {
        given: "一个有效的请求"
        def head = createMobileRequestHead("12345", "test-cid")
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                scenarioCode: 0
        )

        and: "Mock SOA服务返回null"
        mktSupportProxy.getMktMainProcessInfo(_ as com.ctrip.car.market.client.contract.QueryMktMainProcessInfoRequestType) >> null

        when: "调用 queryMktMainProcessInfo 方法"
        def response = business.queryMktMainProcessInfo(request)

        then: "验证返回结果"
        response != null
        response.baseResponse.code == "0"
        response.baseResponse.message == "success"
        response.mktMainProcessInfo.showPopup == false
        response.mktMainProcessInfo.url == null
        response.mktMainProcessInfo.imageUrl == null
    }

    def "test reqConvert method"() {
        given: "一个请求对象和uid"
        def head = createMobileRequestHead("12345", "test-cid")
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                scenarioCode: 0,
                cid: "backup-cid"
        )
        def uid = "12345"

        when: "调用 reqConvert 方法"
        def soaRequest = ReflectionTestUtils.invokeMethod(business, "reqConvert", request, uid)

        then: "验证转换结果"
        soaRequest != null
        soaRequest instanceof com.ctrip.car.market.client.contract.QueryMktMainProcessInfoRequestType
        soaRequest.baseRequest != null
        soaRequest.baseRequest.uid == uid
        soaRequest.baseRequest.cid == "test-cid"
        soaRequest.scenarioCode == 0
    }

    def "test getCid method with head cid"() {
        given: "一个包含head cid的请求"
        def head = createMobileRequestHead("12345", "head-cid")
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                cid: "backup-cid"
        )

        when: "调用 getCid 方法"
        def cid = ReflectionTestUtils.invokeMethod(business, "getCid", request)

        then: "验证返回head中的cid"
        cid == "head-cid"
    }

    def "test getCid method with backup cid"() {
        given: "一个head为null的请求"
        def request = new QueryMktMainProcessInfoRequestType(
                head: null,
                cid: "backup-cid"
        )

        when: "调用 getCid 方法"
        def cid = ReflectionTestUtils.invokeMethod(business, "getCid", request)

        then: "验证返回备用cid"
        cid == "backup-cid"
    }

    def "test getCid method with empty head cid"() {
        given: "一个head cid为空的请求"
        def head = createMobileRequestHead("12345", "")
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                cid: "backup-cid"
        )

        when: "调用 getCid 方法"
        def cid = ReflectionTestUtils.invokeMethod(business, "getCid", request)

        then: "验证返回备用cid"
        cid == "backup-cid"
    }

    def "test resConvert method with valid soa response"() {
        given: "一个有效的SOA响应"
        def soaResponse = createSuccessfulSoaResponse()

        when: "调用 resConvert 方法"
        def result = ReflectionTestUtils.invokeMethod(business, "resConvert", soaResponse)

        then: "验证转换结果"
        result != null
        result instanceof MktMainProcessInfo
        result.showPopup == true
        result.url == "http://test.com"
        result.imageUrl == "http://test.com/image.jpg"
    }

    def "test resConvert method with null mktMainProcessInfo in soa response"() {
        given: "一个mktMainProcessInfo为null的SOA响应"
        def soaResponse = new com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType()
        soaResponse.mktMainProcessInfo = null

        when: "调用 resConvert 方法"
        def result = ReflectionTestUtils.invokeMethod(business, "resConvert", soaResponse)

        then: "验证转换结果"
        result != null
        result instanceof MktMainProcessInfo
        result.showPopup == false
        result.url == null
        result.imageUrl == null
    }

    def "test resConvert method with false showPopup"() {
        given: "一个showPopup为false的SOA响应"
        def soaResponse = new com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType()
        def mktInfo = new com.ctrip.car.market.client.contract.MktMainProcessInfo()
        mktInfo.showPopup = false
        mktInfo.url = "http://test.com"
        mktInfo.imageUrl = "http://test.com/image.jpg"
        soaResponse.mktMainProcessInfo = mktInfo

        when: "调用 resConvert 方法"
        def result = ReflectionTestUtils.invokeMethod(business, "resConvert", soaResponse)

        then: "验证转换结果"
        result != null
        result instanceof MktMainProcessInfo
        result.showPopup == false
        result.url == "http://test.com"
        result.imageUrl == "http://test.com/image.jpg"
    }

    def "test queryMktMainProcessInfo with null head"() {
        given: "一个head为null的请求"
        def request = new QueryMktMainProcessInfoRequestType(
                head: null,
                scenarioCode: 0
        )

        when: "调用 queryMktMainProcessInfo 方法"
        def response = business.queryMktMainProcessInfo(request)

        then: "验证返回失败结果"
        response != null
        response.baseResponse.code == "-1"
        response.baseResponse.message == "no login"
        response.mktMainProcessInfo.showPopup == false
    }

    def "test queryMktMainProcessInfo with empty extension list"() {
        given: "一个extension为空列表的请求"
        def head = new MobileRequestHead()
        head.cid = "test-cid"
        head.extension = []
        def request = new QueryMktMainProcessInfoRequestType(
                head: head,
                scenarioCode: 0
        )

        when: "调用 queryMktMainProcessInfo 方法"
        def response = business.queryMktMainProcessInfo(request)

        then: "验证返回失败结果"
        response != null
        response.baseResponse.code == "-1"
        response.baseResponse.message == "no login"
        response.mktMainProcessInfo.showPopup == false
    }

    def "test reqConvert with null head"() {
        given: "一个head为null的请求"
        def request = new QueryMktMainProcessInfoRequestType(
                head: null,
                scenarioCode: 0,
                cid: "backup-cid"
        )
        def uid = "12345"

        when: "调用 reqConvert 方法"
        def soaRequest = ReflectionTestUtils.invokeMethod(business, "reqConvert", request, uid)

        then: "验证转换结果"
        soaRequest != null
        soaRequest.baseRequest.uid == uid
        soaRequest.baseRequest.cid == "backup-cid"
        soaRequest.scenarioCode == 0
    }

    def "test getCid method with null head and null cid"() {
        given: "一个head和cid都为null的请求"
        def request = new QueryMktMainProcessInfoRequestType(
                head: null,
                cid: null
        )

        when: "调用 getCid 方法"
        def cid = ReflectionTestUtils.invokeMethod(business, "getCid", request)

        then: "验证返回null"
        cid == null
    }

    def "test resConvert method with null url and imageUrl"() {
        given: "一个url和imageUrl为null的SOA响应"
        def soaResponse = new com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType()
        def mktInfo = new com.ctrip.car.market.client.contract.MktMainProcessInfo()
        mktInfo.showPopup = true
        mktInfo.url = null
        mktInfo.imageUrl = null
        soaResponse.mktMainProcessInfo = mktInfo

        when: "调用 resConvert 方法"
        def result = ReflectionTestUtils.invokeMethod(business, "resConvert", soaResponse)

        then: "验证转换结果"
        result != null
        result instanceof MktMainProcessInfo
        result.showPopup == true
        result.url == null
        result.imageUrl == null
    }

    // 辅助方法
    private MobileRequestHead createMobileRequestHead(String uid, String cid) {
        def head = new MobileRequestHead()
        head.cid = cid

        if (uid != null) {
            def extension = new ExtensionFieldType()
            extension.name = "uid"
            extension.value = uid
            head.extension = [extension]
        } else {
            head.extension = []
        }

        return head
    }

    private com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType createSuccessfulSoaResponse() {
        def soaResponse = new com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType()
        def mktInfo = new com.ctrip.car.market.client.contract.MktMainProcessInfo()
        mktInfo.showPopup = true
        mktInfo.url = "http://test.com"
        mktInfo.imageUrl = "http://test.com/image.jpg"
        soaResponse.mktMainProcessInfo = mktInfo
        return soaResponse
    }
}
