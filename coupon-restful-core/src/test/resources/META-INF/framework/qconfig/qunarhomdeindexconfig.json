{"A": {"CHF": {"title": "国内接送机", "type": "CHF", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/carch/chf/index?s=car&biztype=32&pttype=17&stntype=1", "appUrl": "/rn_dcs_fusion/_crn_config?CRNModuleName=rn_dcs_fusion&CRNType=1&initialPage=index&s=car&stntype=1&channelid=7266"}, "OCH": {"title": "境外接送机", "subtitle": "打车", "type": "OCH", "text": "", "clr": "#FF7700", "lastHybirdVer": "7100", "iconUrl": "", "appUrl": "/caroch/index.html#/webapp/caroch/och/index?s=car&biztype=33&isHideNavBar=YES"}, "TRAIN": {"title": "接送火车", "type": "TRAIN", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/carch/chf/index?s=car&biztype=32&pttype=17&stntype=1", "appUrl": "/rn_dcs_fusion/_crn_config?CRNModuleName=rn_dcs_fusion&CRNType=1&initialPage=index&s=car&stntype=2&channelid=7267"}, "RTN": {"title": "打车", "type": "RTN", "text": "", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/car/flash/qc/car?s=car&useUIWebView=YES", "appUrl": "/rn_dcs_fusion/_crn_config?CRNModuleName=rn_dcs_fusion&CRNType=1&initialPage=index&s=car&stntype=3&pttype=28&channelid=90178"}, "DAY": {"title": "按天包车", "type": "DAY", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/zhuanche/day/index?s=car_back&channelid=90195&tabid=0"}, "ODAY": {"title": "定制包车", "type": "ODAY", "clr": "#FF7700", "lastHybirdVer": "7100", "iconUrl": "", "url": "/webapp/zhuanche/day/index?s=car_back&channelid=90194&tabid=1"}, "ISD": {"title": "国内自驾租车", "type": "ISD", "text": "", "clr": "#FF7700", "lastHybirdVer": "7100", "iconUrl": "", "appUrl": "/rn_car_isd/_crn_config?CRNModuleName=CtripApp&CRNType=1&s=car&from=car"}, "ZJY": {"title": "自驾游", "type": "ZJY", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/vacations/tour/list?searchtype=theme&tab=128&ctm_ref=vdc_ctm_4480&kwd=&extension={\"title\":\"越野自驾\",\"themeId\":\"10\"}"}, "OSD": {"title": "境外自驾租车", "type": "OSD", "text": "", "clr": "#FF7700", "lastHybirdVer": "7070", "iconUrl": "", "appUrl": "/rn_car_osd/_crn_config?CRNModuleName=CtripApp&CRNType=1&page=Osd&from=car"}, "RTNNOW": {"clr": "", "lastHybirdVer": "12", "text": "", "type": "RTNNOW"}}, "B": {"CHF": {"title": "国内接送机", "type": "CHF", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/carch/chf/index?s=car&biztype=32&pttype=17&stntype=1", "appUrl": "/rn_dcs_fusion/_crn_config?CRNModuleName=rn_dcs_fusion&CRNType=1&initialPage=index&s=car&stntype=1&channelid=7266"}, "OCH": {"title": "境外接送机", "subtitle": "打车", "type": "OCH", "text": "", "clr": "#FF7700", "lastHybirdVer": "7100", "iconUrl": "", "appUrl": "/caroch/index.html#/webapp/caroch/och/index?s=car&biztype=33&isHideNavBar=YES"}, "TRAIN": {"title": "接送火车", "type": "TRAIN", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/carch/chf/index?s=car&biztype=32&pttype=17&stntype=1", "appUrl": "/rn_dcs_fusion/_crn_config?CRNModuleName=rn_dcs_fusion&CRNType=1&initialPage=index&s=car&stntype=2&channelid=7267"}, "RTN": {"title": "打车", "type": "RTN", "text": "", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/car/flash/qc/car?s=car&useUIWebView=YES", "appUrl": "/rn_dcs_fusion/_crn_config?CRNModuleName=rn_dcs_fusion&CRNType=1&initialPage=index&s=car&stntype=3&pttype=28&channelid=90178"}, "DAY": {"title": "按天包车", "type": "DAY", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/zhuanche/day/index?s=car_back&channelid=90195&tabid=0"}, "ODAY": {"title": "定制包车", "type": "ODAY", "clr": "#FF7700", "lastHybirdVer": "7100", "iconUrl": "", "url": "/webapp/zhuanche/day/index?s=car_back&channelid=90194&tabid=1"}, "ISD": {"title": "国内自驾租车", "type": "ISD", "text": "", "clr": "#FF7700", "lastHybirdVer": "7100", "iconUrl": "", "appUrl": "/rn_car_isd/_crn_config?CRNModuleName=CtripApp&CRNType=1&s=car&from=car"}, "ZJY": {"title": "自驾游", "type": "ZJY", "clr": "#FF7700", "lastHybirdVer": "", "iconUrl": "", "url": "/webapp/vacations/tour/list?searchtype=theme&tab=128&ctm_ref=vdc_ctm_4480&kwd=&extension={\"title\":\"越野自驾\",\"themeId\":\"10\"}"}, "OSD": {"title": "境外自驾租车", "type": "OSD", "text": "", "clr": "#FF7700", "lastHybirdVer": "7070", "iconUrl": "", "appUrl": "/rn_car_osd/_crn_config?CRNModuleName=CtripApp&CRNType=1&page=Osd&from=car"}, "RTNNOW": {"clr": "", "lastHybirdVer": "12", "text": "", "type": "RTNNOW"}}}