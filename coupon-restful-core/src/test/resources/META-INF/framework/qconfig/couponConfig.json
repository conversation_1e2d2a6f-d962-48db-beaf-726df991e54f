[{"receiveCouponStatus": 1, "isShow": 1, "buttonContent": "领券并前往携程旅行App", "sendStatus": 1, "document": "本App将于12月31日停止服务，为了给您带来更好的体验，请前往[携程旅行App]使用租车服务", "documents": ["本App已停止服务，为了给您带来更好的体验, 请前往「携程旅行App」使用租车服务", "领券立减66元，仅携程旅行App新客可领取，领取后仅可至携程旅行App使用", "因您注册账户为携程平台账户，如需注销，可登录携程旅行APP进行账户注销"]}, {"receiveCouponStatus": 2, "isShow": 1, "buttonContent": "前往携程旅行App", "sendStatus": 0, "document": "本App将于12月31日停止服务，为了给您带来更好的体验，请前往[携程旅行App]使用租车服务", "documents": ["本App已停止服务，为了给您带来更好的体验, 请前往「携程旅行App」使用租车服务", "领券立减66元，仅携程旅行App新客可领取，领取后仅可至携程旅行App使用", "因您注册账户为携程平台账户，如需注销，可登录携程旅行APP进行账户注销。"]}, {"receiveCouponStatus": 3, "isShow": 1, "buttonContent": "前往携程旅行App", "sendStatus": 0, "document": "本App将于12月31日停止服务，为了给您带来更好的体验，请前往[携程旅行App]使用租车服务", "documents": ["本App已停止服务，为了给您带来更好的体验, 请前往「携程旅行App」使用租车服务", "领券立减66元，仅携程旅行App新客可领取，领取后仅可至携程旅行App使用", "因您注册账户为携程平台账户，如需注销，可登录携程旅行APP进行账户注销"]}, {"receiveCouponStatus": 4, "isShow": 1, "buttonContent": "登录后可领券", "sendStatus": 0, "document": "本App将于12月31日停止服务，为了给您带来更好的体验，请前往[携程旅行App]使用租车服务", "documents": ["本App已停止服务，为了给您带来更好的体验, 请前往「携程旅行App」使用租车服务", "领券立减66元，仅携程旅行App新客可领取，领取后仅可至携程旅行App使用", "因您注册账户为携程平台账户，如需注销，可登录携程旅行APP进行账户注销"]}, {"receiveCouponStatus": 0, "isShow": 1, "buttonContent": "前往携程旅行App", "sendStatus": 0, "document": "本App已停止服务，为了给您带来更好的体验, 请前往[携程旅行App]使用租车服务", "documents": ["本App已停止服务，为了给您带来更好的体验, 请前往「携程旅行App」使用租车服务", "因您注册账户为携程平台账户，如需注销，可登录携程旅行APP进行账户注销。"]}]