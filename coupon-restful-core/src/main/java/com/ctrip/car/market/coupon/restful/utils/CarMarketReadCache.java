package com.ctrip.car.market.coupon.restful.utils;

import credis.java.client.CacheProvider;
import credis.java.client.util.CacheFactory;


public class CarMarketReadCache {

    //注意car_market_cache在SGP集群无法写redis，只支持读
    private static final CacheProvider marketProvider = CacheFactory.getProvider("car_market_cache");

    public static String get(String key) {
        return marketProvider.get(key);
    }
}
