package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.car.market.coupon.restful.dto.CouponConfigDTO;
import com.ctrip.car.market.coupon.restful.dto.InFlowRuleDTO;
import com.ctrip.car.market.coupon.restful.dto.MemberPrivilegeConfigDTO;
import com.ctrip.car.market.coupon.restful.dto.UserMktConditionDTO;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * Created by h_zhanga on 2018/7/24.
 */

@Component
public class CouponServiceQConfig {




    @QConfig("conf.properties")
    private Map<String, String> confConfig;




    @QConfig("openFlag.properties")
    private Map<String, String> openFlagConfig;



    @QConfig("qunarhomdeindexconfig.json")
    private String qunarHomeConfig;

    @QConfig("ctriphomdeindexconfig.json")
    private String ctripHomeConfig;

    @QConfig("memberPrivilegeConfig.json")
    private List<MemberPrivilegeConfigDTO> memberPrivilegeConfig;

    @QConfig("couponConfig.json")
    private List<CouponConfigDTO> couponConfigs;

    @QConfig("inFlowRule.json")
    private List<InFlowRuleDTO> inFlowRules;

    @QConfig("userMktCondition.json")
    private List<UserMktConditionDTO> userMktConditions;
    @QConfig("userMktCondition2.json")
    private List<UserMktConditionDTO> userMktConditions2;

    public String getQunarHomeConfig() {
        return qunarHomeConfig;
    }

    public void setQunarHomeConfig(String qunarHomeConfig) {
        this.qunarHomeConfig = qunarHomeConfig;
    }

    public String getCtripHomeConfig() {
        return ctripHomeConfig;
    }

    public void setCtripHomeConfig(String ctripHomeConfig) {
        this.ctripHomeConfig = ctripHomeConfig;
    }



    public Map<String, String> getConfConfig() {
        return confConfig;
    }

    public void setConfConfig(Map<String, String> confConfig) {
        this.confConfig = confConfig;
    }

    public String getValueByKeyFromQconfig(String key) {
        if (MapUtils.isNotEmpty(confConfig) && confConfig.containsKey(key)) {
            return confConfig.get(key);
        }
        return "";
    }



    public Map<String, String> getOpenFlagConfig() {
        return openFlagConfig;
    }

    public void setOpenFlagConfig(Map<String, String> openFlagConfig) {
        this.openFlagConfig = openFlagConfig;
    }



    public boolean findSpecificChannelExist(Integer channelId) {

        String supportChannelId = getValueByKeyFromQconfig("coupon.restful.offer.order.support.channel.id");
        if (StringUtils.isBlank(supportChannelId)) {
            return false;
        }

        return Arrays.stream(supportChannelId.split(","))
                .filter(StringUtils::isNotBlank)
                .map(Integer::valueOf)
                .anyMatch(x -> x.equals(channelId));

    }

    public boolean checkCommonUid(String uid) {
        String commonsUids =  getValueByKeyFromQconfig("offer.order.support.commons.op.uid");
        if (StringUtils.isBlank(commonsUids)) {
            return false;
        }

        return Arrays.stream(commonsUids.split(","))
                .filter(StringUtils::isNotBlank)
                .anyMatch(x -> x.equals(uid));
    }

    public List<MemberPrivilegeConfigDTO> getMemberPrivilegeConfig() {
        return memberPrivilegeConfig;
    }

    public void setMemberPrivilegeConfig(List<MemberPrivilegeConfigDTO> memberPrivilegeConfig) {
        this.memberPrivilegeConfig = memberPrivilegeConfig;
    }

    public List<CouponConfigDTO> getCouponConfigs() {
        return couponConfigs;
    }

    public void setCouponConfigs(List<CouponConfigDTO> couponConfigs) {
        this.couponConfigs = couponConfigs;
    }

    public List<InFlowRuleDTO> getInFlowRules() {
        return inFlowRules;
    }

    public void setInFlowRules(List<InFlowRuleDTO> inFlowRules) {
        this.inFlowRules = inFlowRules;
    }

    public List<UserMktConditionDTO> getUserMktConditions() {
        return userMktConditions;
    }

    public void setUserMktConditions(List<UserMktConditionDTO> userMktConditions) {
        this.userMktConditions = userMktConditions;
    }

    public boolean findAPPSourceFrom(String  sourceFrom) {

        String supportChannelId = getValueByKeyFromQconfig("sourceFrom.app");
        if (StringUtils.isBlank(supportChannelId)) {
            return false;
        }

        return Arrays.stream(supportChannelId.split(","))
                .filter(StringUtils::isNotBlank)
                .anyMatch(x -> x.equalsIgnoreCase(sourceFrom));

    }


    public boolean findWeChatSourceFrom(String sourceFrom) {

        String supportChannelId = getValueByKeyFromQconfig("sourceFrom.wechat");
        if (StringUtils.isBlank(supportChannelId)) {
            return false;
        }

        return Arrays.stream(supportChannelId.split(","))
                .filter(StringUtils::isNotBlank)
                .anyMatch(x -> x.equalsIgnoreCase(sourceFrom));

    }

    public List<UserMktConditionDTO> getUserMktConditions2() {
        return userMktConditions2;
    }

    public void setUserMktConditions2(List<UserMktConditionDTO> userMktConditions2) {
        this.userMktConditions2 = userMktConditions2;
    }
}
