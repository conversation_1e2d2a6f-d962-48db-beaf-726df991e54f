package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend;

import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.contract.QueryTripSeoProductsResponseType;
import com.ctrip.car.market.coupon.restful.contract.QueryZonesResponseType;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = "spring")
 public interface CarCrossRecommendedConverter {
    CarCrossRecommendedConverter INSTANCE = Mappers.getMapper( CarCrossRecommendedConverter.class );

    com.ctrip.car.market.BaseRequest converterBaseRequest(BaseRequest baseRequest);

    QueryTripSeoProductsResponseType converterQueryTripSeoProductsResponseType(com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryTripSeoProductsResponseType responseType);

    QueryZonesResponseType converterQueryZonesResponseType(com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryZonesResponseType responseType);
 }