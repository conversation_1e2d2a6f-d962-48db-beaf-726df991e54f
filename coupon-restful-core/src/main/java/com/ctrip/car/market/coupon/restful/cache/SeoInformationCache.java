package com.ctrip.car.market.coupon.restful.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@CreateCacheBean
public class SeoInformationCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoInformationCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_INFORMATION_KEY)
    public Cache<String, List<SeoHotInformationDO>> informationCache;

    @IncUpdateConsumerByRedis
    @CreateCache(name = "SeoInformationCountryCache",
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = "seo_information_country_info")
    public Cache<Integer, List<SeoHotInformationDO>> informationCountryCache;

    public SeoHotInformationDO queryInformation(Integer poiType, String poiCode) {
        if (StringUtils.isEmpty(poiCode)) {
            return null;
        }
        List<SeoHotInformationDO> values = informationCache.get(poiType + "-" + poiCode.toLowerCase());
        return CollectionUtils.isNotEmpty(values) ? values.get(0) : null;
    }

    public List<SeoHotInformationDO> queryInformationByCountry(Integer countryId) {
        return informationCountryCache.get(countryId);
    }
}
