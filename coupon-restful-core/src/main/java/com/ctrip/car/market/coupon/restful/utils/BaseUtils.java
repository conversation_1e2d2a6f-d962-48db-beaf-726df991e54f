package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.car.market.coupon.restful.contract.RestRequestHeader;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.qunar.QunarUserProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.qunar.QunarUserScookieProxy;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctriposs.baiji.rpc.mobile.common.types.ExtensionFieldType;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import java.text.DecimalFormat;

/**
 * Created by zmqu on 2018/6/9.
 */
public class BaseUtils {


    public static String getUidByCommon(MobileRequestHead head) {

        String uid = "";

        if (head == null || head.getExtension() == null)
            return null;
        for (ExtensionFieldType extension : head.getExtension()) {
            if (extension.getName() != null && extension.getName().equals("uid") && extension.getValue() != null) {
                uid = extension.getValue();
                break;
            }
        }

        return uid;
    }

    public static String getQunarUidByCommon(RestRequestHeader head) {
        String uid = "";
        if (!StringUtils.isBlank(head.getScookie())) {
            uid = QunarUserScookieProxy.getUidByScookie(head.getScookie());
        }
        if (StringUtils.isBlank(uid)){
            uid = QunarUserProxy.getUid(head.getUid(), head.getCid(), head.getSign());
        }
        return uid;
    }

}
