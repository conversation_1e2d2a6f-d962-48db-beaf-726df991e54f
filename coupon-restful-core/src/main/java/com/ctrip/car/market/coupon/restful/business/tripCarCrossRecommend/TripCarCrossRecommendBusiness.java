package com.ctrip.car.market.coupon.restful.business.tripCarCrossRecommend;

import com.ctrip.car.market.coupon.restful.contract.TripCarCrossRecommendRequestType;
import com.ctrip.car.market.coupon.restful.contract.TripCarCrossRecommendResponseType;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;


/**
 * <AUTHOR>
 */
@Component
public class TripCarCrossRecommendBusiness {
    public TripCarCrossRecommendResponseType tripCarCrossRecommend(TripCarCrossRecommendRequestType request) {
        TripCarCrossRecommendResponseType responseType = new TripCarCrossRecommendResponseType();
        if (!this.verify(request, responseType)) {
            return responseType;
        }
        TripCarCrossRecommend tripCarCrossRecommendService = TripCarCrossRecommendFactory.routeStrategy(request.getCrossScene(), request.getProductType());
        if (tripCarCrossRecommendService == null) {
            responseType.setBaseResponse(ResponseUtil.fail("can not find handler instance, please check your params"));
            return responseType;
        }
        TripCarCrossRecommendResponseType recommend = tripCarCrossRecommendService.recommend(request);
        Metrics.build().withTag("productType", Optional.ofNullable(request.getProductType().value()).orElse("NULL"))
                .withTag("crossScene", Optional.ofNullable(request.getCrossScene().value()).orElse("NULL"))
                .withTag("result", CollectionUtils.isNotEmpty(recommend.getProductList()) ? "1" : "0")
                .recordOne("TripCarCrossRecommend");
        return recommend;
    }

    private boolean verify(TripCarCrossRecommendRequestType request, TripCarCrossRecommendResponseType responseType) {
        if (request.getCrossScene() == null || request.getProductType() == null) {
            responseType.setBaseResponse(ResponseUtil.fail("cross scene and productType can not be null"));
            return false;
        }
        return true;
    }
}
