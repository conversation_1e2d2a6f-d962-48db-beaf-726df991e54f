package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon;

import com.ctrip.car.market.client.contract.*;
import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.coupon.restful.contract.MemberPrivilegeDTO;
import com.ctrip.car.market.coupon.restful.contract.ReservePrivilegeDTO;
import com.ctrip.car.market.coupon.restful.dto.MemberPrivilegeConfigDTO;
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;

@Service
public class SDMarketingServiceProxy {

    public SDMarketingServiceClient sdMarketingServiceClient = SDMarketingServiceClient.getInstance();
    private static final ILog log = LogManager.getLogger("SDMarketingServiceProxy");



    public SkinInfo getSkins(String sourceFrom) {
        QuerySkinsRequestType request = new QuerySkinsRequestType();
        request.setSourceFrom(sourceFrom);
        try {
            QuerySkinsResponseType response = sdMarketingServiceClient.querySkins(request);
            if (response != null)
                return response.getSkinInfo();
        } catch (Exception e) {
            log.error("SDMarketingServiceProxy getSkins error ", e);
            return null;
        }
        return null;
    }

    public QueryQunarCouponListResponseType queryQunarCouponList(QueryQunarCouponListRequestType requestType) {
        try {
            return sdMarketingServiceClient.queryQunarCouponList(requestType);
        } catch (Exception e) {
            log.error("SDMarketingServiceProxy queryQunarCouponList error ", e);
            return null;
        }
    }

    public QueryMktMainPageInfoResponseType queryMktMainPageInfo(QueryMktMainPageInfoRequestType request) {
        try {
            return sdMarketingServiceClient.queryMktMainPageInfo(request);
        } catch (Exception e) {
            log.error("SDMarketingServiceProxy queryMktMainPageInfo error ", e);
            return null;
        }
    }
}
