package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp;

import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.UcpBizSystemServiceClient;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.*;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.common.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

@Component
public class UcpServiceProxy {

    private final ILog logger = LogManager.getLogger(UcpServiceProxy.class);

    private final UcpBizSystemServiceClient client = UcpBizSystemServiceClient.getInstance();

    @NCache(holdMinute = 60, reHoldMinute = 30)
    public ListCommentsResponseType queryComment(String locale, Integer countryId, Integer cityId) {
        ListCommentsRequestType requestType = new ListCommentsRequestType();
        requestType.setUcpBizs(Lists.newArrayList(UcpBizEnum.CAR_RENTAL));
        TagTermType tagTermType = new TagTermType();
        tagTermType.setFilterGroup(0);
        tagTermType.setTagCategory("score");
        tagTermType.setTagName("GOOD");
        requestType.setTagTerms(Lists.newArrayList(tagTermType));
        requestType.setSortType(3);
        requestType.setPaging(new PagingType());
        requestType.getPaging().setPageNo(1);
        requestType.getPaging().setPageSize(9);
        requestType.setScene(QuerySceneEnum.CAR_TRIP_SEO_QUERY);
        requestType.setNeedTranslate(true);
        requestType.setCommentAuditStatuses(Lists.newArrayList(AuditStatusEnum.APPROVED));
        if (Optional.ofNullable(countryId).orElse(0) > 0) {
            requestType.setCountryIds(Lists.newArrayList(countryId.longValue()));
        }
        if (Optional.ofNullable(cityId).orElse(0) > 0) {
            requestType.setCityIds(Lists.newArrayList(cityId.longValue()));
        }
        client.setRawLocale(locale);

        try {
            ListCommentsResponseType responseType = client.listComments(requestType);
            return responseType != null && CollectionUtils.isNotEmpty(responseType.getComments()) ? responseType : null;
        } catch (Exception e) {
            logger.warn("queryComment", e);
            return null;
        }
    }

    public CommonQueryCommentSummaryResponseType queryVendorCityComment(String locale, String vendorCode, Integer cityId) {
        CommonQueryCommentSummaryRequestType requestType = new CommonQueryCommentSummaryRequestType();
        requestType.setUcpBiz(UcpBizEnum.CAR_RENTAL);
        requestType.setScene(QuerySceneEnum.BRAND_CITY_QUERY);
        requestType.setResultSize(8);
        Map<String, String> extInfoMap = Maps.newHashMap();
        extInfoMap.put("brandCity", vendorCode.toUpperCase() + "-" + cityId);
        requestType.setExtRequest(extInfoMap);
        client.setRawLocale(locale);
        try {
            CommonQueryCommentSummaryResponseType responseType = client.commonQueryCommentSummary(requestType);
            return responseType;
        } catch (Exception e) {
            logger.warn("queryComment", e);
            return null;
        }
    }
}
