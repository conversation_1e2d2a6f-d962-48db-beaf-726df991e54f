package com.ctrip.car.market.coupon.restful.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.market.job.common.entity.seo.SeoVendorCommentScoreDO;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@CreateCacheBean
public class SeoVendorCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoVendorCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_VENDOR_KEY)
    public Cache<String, List<SeoHotVendorDO>> vendorCache;

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoVendorCityCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_VENDOR_CITY_KEY)
    public Cache<String, List<SeoHotVendorCityDO>> vendorCityCache;

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoVendorInformationCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_VENDOR_INFORMATION_KEY)
    public Cache<String, List<SeoHotVendorInformationDO>> vendorInformationCache;

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoVendorCommentScoreCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_VENDOR_COMMENT_SCORE_KEY)
    public Cache<String, List<SeoVendorCommentScoreDO>> vendorCommentScoreCache;


    public SeoHotVendorDO queryVendor(String vendorCode) {
        List<SeoHotVendorDO> list = vendorCache.get(vendorCode.toLowerCase());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public SeoHotVendorCityDO queryVendorCity(String vendorCode, Integer cityId) {
        List<SeoHotVendorCityDO> list = vendorCityCache.get(vendorCode.toLowerCase());
        return CollectionUtils.isNotEmpty(list) ? list.stream().filter(l -> Objects.equals(l.getCityId(), cityId)).findFirst().orElse(null) : null;
    }

    public List<SeoHotVendorCityDO> queryVendorCity(String vendorCode) {
        return vendorCityCache.get(vendorCode.toLowerCase());
    }

    public SeoVendorCommentScoreDO queryVendorCommentScore(String vendorCode) {
        List<SeoVendorCommentScoreDO> list = vendorCommentScoreCache.get(vendorCode.toLowerCase());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<SeoHotVendorInformationDO> queryVendorInformation(String vendorCode) {
        return vendorInformationCache.get(vendorCode.toLowerCase());
    }
}
