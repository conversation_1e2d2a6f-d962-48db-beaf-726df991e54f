package com.ctrip.car.market.coupon.restful.business.seovendor;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache;
import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.contract.seo.FaqInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqResponseType;
import com.ctrip.car.market.coupon.restful.dto.KeyValueDto;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct;
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes;
import com.ctrip.car.osd.shopping.api.entity.RecomdVehicleDTO;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SeoVendorFaqBusiness {

    @Resource
    private SeoService service;

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private SeoVendorVehicleBusiness vendorVehicleBusiness;

    @Resource
    private TripConfig tripConfig;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    public QuerySeoFaqResponseType queryFaq(QuerySeoFaqRequestType request) {
        SeoHotVendorDO vendorDO = seoVendorCache.queryVendor(request.getVendorCode());
        if (vendorDO == null) {
            return new QuerySeoFaqResponseType();
        }
        SeoHotVendorCityDO vendorCityDO = Optional.ofNullable(request.getCityId()).orElse(0) > 0 ? seoVendorCache.queryVendorCity(request.getVendorCode(), request.getCityId()) : null;
        if (Optional.ofNullable(request.getCityId()).orElse(0) > 0 && vendorCityDO == null) {
            return new QuerySeoFaqResponseType();
        }
        QuerySeoFaqResponseType response = new QuerySeoFaqResponseType();
        if (Optional.ofNullable(request.getCityId()).orElse(0) > 0) {
            response = cityPage(request, vendorDO, vendorCityDO);
        } else {
            response = vendorPage(request, vendorDO);
        }
        response.setCacheTime(osdShoppingProxy.getCacheTime());
        response.setVendorStoreNum(service.getVendorStoreCount(request.getVendorCode()));
        return response;
    }

    private QuerySeoFaqResponseType vendorPage(QuerySeoFaqRequestType request, SeoHotVendorDO vendorDO) {
        QuerySeoFaqResponseType response = new QuerySeoFaqResponseType();
        SeoHotVendorInformationDO cityInformation = service.queryVendorHotCity(request.getVendorCode());
        List<FaqInfo> result = Lists.newArrayList();
        String locale = request.getBaseRequest().getLocale();
        List<RecomdProductRes> productList = queryProduct(request.getBaseRequest(), request.getHead(), cityInformation);
        //最低价
        String lowPrice = getLowPrice(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode());
        response.setLowPrice(lowPrice);
        response.setLowPriceValue(getLowPriceValue(productList));
        //供应商名称
        String vendorName = vendorDO.getVendorName();
        //热门车型
        String hotVehicleName = cityInformation != null && Optional.ofNullable(cityInformation.getVehicleId()).orElse(0L) > 0 ? service.queryVehicleName(cityInformation.getVehicleId(), locale) : null;
        //热门城市
        String hotCityName = cityInformation != null && Optional.ofNullable(cityInformation.getCityId()).orElse(0) > 0 ? service.getCityName(cityInformation.getCityId(), locale) : null;
        //热门机场
        String hotAirportName = cityInformation != null && Objects.equals(cityInformation.getPoiType(), 1) && StringUtils.isNotEmpty(cityInformation.getPoiCode()) ? service.getAirportName(cityInformation.getPoiCode(), locale) : null;
        //经济型车型
        String smallVehicle = getLowPriceVehicle(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("small"));
        //SUV
        String suvVehicle = getLowPriceVehicle(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("suv"));
        //豪华型
        String premiumVehicle = getLowPriceVehicle(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("premium"));

        if (StringUtils.isNotEmpty(lowPrice)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("1", locale), vendorName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("1", locale), vendorName, lowPrice)));
        }
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("2", locale), vendorName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("2", locale), vendorName)));
        //车型组
        if (StringUtils.isNotEmpty(smallVehicle) || StringUtils.isNotEmpty(suvVehicle) || StringUtils.isNotEmpty(premiumVehicle)) {
            String q = LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("3", locale), vendorName);
            String a = vehicleGroupFaq(smallVehicle, suvVehicle, premiumVehicle, locale, vendorName, null);
            result.add(buildFaq(q, a));
        }
        if (StringUtils.isNotEmpty(hotVehicleName)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("4", locale), vendorName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("4", locale), vendorName, hotVehicleName)));
        }
        if (StringUtils.isNotEmpty(hotCityName) && StringUtils.isNotEmpty(hotVehicleName)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("5", locale), vendorName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("5", locale), vendorName, hotCityName, hotAirportName)));
        }
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("6", locale), vendorName), SeoShark.VendorFaqA.getVendorFaq("6", locale)));
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("7", locale), vendorName), SeoShark.VendorFaqA.getVendorFaq("7", locale)));
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("8", locale), vendorName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("8", locale), vendorName)));
        if (StringUtils.isNotEmpty(lowPrice)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("9", locale), vendorName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("9", locale), vendorName, lowPrice)));
        }
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorFaqQ.getVendorFaq("10", locale), vendorName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("10", locale), vendorName)));
        response.setFaqList(result);
        response.setCarCard(osdShoppingProxy.getCarCard(request.getBaseRequest(), cityInformation == null ? "" : cityInformation.getPoiCode(), request.getVendorCode()));
        return response;
    }

    private QuerySeoFaqResponseType cityPage(QuerySeoFaqRequestType request, SeoHotVendorDO vendorDO, SeoHotVendorCityDO vendorCityDO) {
        QuerySeoFaqResponseType response = new QuerySeoFaqResponseType();
        SeoHotVendorInformationDO cityInformation = service.queryVendorCity(request.getVendorCode(), request.getCityId());

        List<FaqInfo> result = Lists.newArrayList();
        String locale = request.getBaseRequest().getLocale();
        String vendorName = vendorDO.getVendorName();
        String cityName = service.getCityName(vendorCityDO.getCityId(), locale);
        String hotPoiName = cityInformation == null ? null : service.queryPoiName(cityInformation.getPoiType(), cityInformation.getPoiCode(), locale);
        Integer storeCount = cityInformation == null ? null : cityInformation.getStoreNum();
        List<RecomdProductRes> productList = queryProduct(request.getBaseRequest(), request.getHead(), cityInformation);
        //最低价
        String lowPrice = getLowPrice(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode());
        response.setLowPrice(lowPrice);
        response.setLowPriceValue(getLowPriceValue(productList));
        //经济型车型
        String smallVehicle = getLowPriceVehicle(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("small"));
        //SUV
        String suvVehicle = getLowPriceVehicle(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("suv"));
        //豪华型
        String premiumVehicle = getLowPriceVehicle(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("premium"));
        //车型组
        if (StringUtils.isNotEmpty(smallVehicle) || StringUtils.isNotEmpty(suvVehicle) || StringUtils.isNotEmpty(premiumVehicle)) {
            String q = LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("1", locale), vendorName, cityName);
            String a = vehicleGroupFaq(smallVehicle, suvVehicle, premiumVehicle, locale, vendorName, cityName);
            result.add(buildFaq(q, a));
        }
        //热门车型
        String hotVehicleName = cityInformation != null && Optional.ofNullable(cityInformation.getVehicleId()).orElse(0L) > 0 ? service.queryVehicleName(cityInformation.getVehicleId(), locale) : null;
        if (StringUtils.isNotEmpty(hotVehicleName)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("2", locale), vendorName, cityName), LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqA.getVendorFaq("2", locale), vendorName, hotVehicleName)));
        }
        if (StringUtils.isNotEmpty(hotPoiName)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("3", locale), vendorName, cityName), LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqA.getVendorFaq("3", locale), vendorName, cityName, hotPoiName)));
        }
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("4", locale), vendorName, cityName), LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqA.getVendorFaq("4", locale), vendorName)));
        if (Optional.ofNullable(storeCount).orElse(0) > 0 && StringUtils.isNotEmpty(hotPoiName)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("5", locale), vendorName, cityName), LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqA.getVendorFaq("5", locale), vendorName, cityName, storeCount, hotPoiName)));
        }
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("6", locale), vendorName, cityName), SeoShark.VendorCityFaqA.getVendorFaq("6", locale)));
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("7", locale), vendorName, cityName), SeoShark.VendorFaqA.getVendorFaq("7", locale)));
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("8", locale), vendorName, cityName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("8", locale), vendorName)));
        if (StringUtils.isNotEmpty(lowPrice)) {
            result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("9", locale), vendorName, cityName), LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("9", locale), vendorName, lowPrice)));
        }
        result.add(buildFaq(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqQ.getVendorFaq("10", locale), vendorName, cityName), SeoShark.VendorCityFaqA.getVendorFaq("10", locale)));
        response.setFaqList(result);
        response.setCarCard(osdShoppingProxy.getCarCard(request.getBaseRequest(), cityInformation == null ? "" : cityInformation.getPoiCode(), request.getVendorCode()));
        return response;
    }

    private String vehicleGroupFaq(String small, String suv, String premium, String locale, String vendorName, String cityName) {
        StringBuilder a = new StringBuilder();
        if (StringUtils.isNotEmpty(cityName)) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("3.1", locale), vendorName));
        } else {
            a.append(LanguageUtils.sharkValFormat(SeoShark.VendorCityFaqA.getVendorFaq("1", locale), vendorName));
        }
        if (StringUtils.isNotEmpty(small)) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("3.2", locale), small));
        }
        if (StringUtils.isNotEmpty(suv)) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("3.3", locale), suv));
        }
        if (StringUtils.isNotEmpty(premium)) {
            a.append(LanguageUtils.sharkValFormat(SeoShark.VendorFaqA.getVendorFaq("3.4", locale), premium));
        }
        return a.toString();
    }

    private FaqInfo buildFaq(String q, String a) {
        if (StringUtils.isEmpty(q) || StringUtils.isEmpty(a)) {
            return null;
        }
        FaqInfo result = new FaqInfo();
        result.setQuestion(q);
        result.setAnswer(a);
        return result;
    }

    private List<RecomdProductRes> queryProduct(BaseRequest baseRequest, MobileRequestHead head, SeoHotVendorInformationDO seoHotVendorInformationDO) {
        List<QueryRecomdProductsResponseType> response = vendorVehicleBusiness.queryVehicle(baseRequest, head, Lists.newArrayList(seoHotVendorInformationDO), true);
        if (CollectionUtils.isEmpty(response) || CollectionUtils.isEmpty(response.get(0).getRecomdProductResList())) {
            return Lists.newArrayList();
        }
        return response.get(0).getRecomdProductResList();
    }

    private String getLowPrice(List<RecomdProductRes> productList, String locale, String currencyCode) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        BigDecimal price = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return service.currencyString(price, LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(locale), CurrencyEnum.getCurrrencyEnum(currencyCode));
    }

    private BigDecimal getLowPriceValue(List<RecomdProductRes> productList) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        return productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
    }

    private String getLowPriceVehicle(List<RecomdProductRes> productList, String locale, String currencyCode, Set<String> vehicleGroupSet) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        if (CollectionUtils.isEmpty(vehicleGroupSet)) {
            return null;
        }
        RecomdProduct product = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null
                && l.getVehicle() != null && StringUtils.isNotEmpty(l.getVehicle().getGroupCode()) && vehicleGroupSet.contains(l.getVehicle().getGroupCode())).sorted(Comparator.comparing(l -> l.getPrice().getCurrentDailyPrice())).findFirst().orElse(null);
        if (product == null || product.getVehicle() == null) {
            return null;
        }
        return product.getVehicle().getVehicleName();
    }

    private Set<String> getVehicleGroupSet(String vehicleGroup) {
        return tripConfig.getVehicleGroupMappingList().stream().filter(l -> StringUtils.equalsIgnoreCase(vehicleGroup, l.getValue())).map(KeyValueDto::getKey).collect(Collectors.toSet());
    }
}
