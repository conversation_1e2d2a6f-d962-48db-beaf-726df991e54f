package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.vendor;

import com.ctrip.car.commodity.vendor.query.service.method.QueryVendorListToCacheRequestType;
import com.ctrip.car.commodity.vendor.query.service.method.QueryVendorListToCacheResponseType;
import com.ctrip.car.commodity.vendor.query.service.soa.CarCommodityVendorQueryApiClient;
import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import com.ctrip.car.top.BaseRequest;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

@Component
public class VendorProxy {

    private final ILog logger = LogManager.getLogger(VendorProxy.class);

    private final CarCommodityVendorQueryApiClient apiClient = CarCommodityVendorQueryApiClient.getInstance();

    public QueryVendorListToCacheResponseType queryVendor(String vendorCode) {
        try {
            QueryVendorListToCacheRequestType requestType = new QueryVendorListToCacheRequestType();
            requestType.setBaseRequest(new BaseRequest());
            requestType.getBaseRequest().setVendorIdList(Lists.newArrayList(Long.valueOf(vendorCode)));
            logger.info("queryVendor_req", JsonUtils.convertObject2Json(requestType));
            QueryVendorListToCacheResponseType responseType = apiClient.queryVendorListToCache(requestType);
            logger.info("queryVendor_res", JsonUtils.convertObject2Json(responseType));
            return responseType;
        } catch (Exception e) {
            logger.warn("queryVendor", e);
            return null;
        }
    }
}
