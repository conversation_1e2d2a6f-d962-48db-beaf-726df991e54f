package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.Optional;

@Service
public class CornerSignBusiness {

    @Autowired
    private CouponServiceQConfig couponServiceQConfig;



    public QueryHomeIndexConfigResponseType queryHomeIndexConfig(QueryHomeIndexConfigRequestType request) {
        QueryHomeIndexConfigResponseType responseType = new QueryHomeIndexConfigResponseType();
        responseType.setResultCode(ResultOpenStatus.SUCCESS.getCode());
        responseType.setResultMessage(ResultOpenStatus.SUCCESS.getMessage());
        if (Optional.ofNullable(request.getUnion()).orElse(0).equals(0)) {
            responseType.setIndexConfig(couponServiceQConfig.getCtripHomeConfig());
        } else {
            responseType.setIndexConfig(couponServiceQConfig.getQunarHomeConfig());
        }
       return responseType;
    }


}
