package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.seo.CommentInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoCommentListResponseType;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ucp.UcpServiceProxy;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.tour.tripservice.ucp.biz.systemservice.contract.dto.ListCommentsResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SeoCommentBusiness {

    @Resource
    private UcpServiceProxy ucpServiceProxy;

    @Resource
    private SeoService seoService;

    public QuerySeoCommentListResponseType queryComment(QuerySeoCommentListRequestType request) {
        QuerySeoCommentListResponseType response = new QuerySeoCommentListResponseType();
        if (request.getBaseRequest() == null) {
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        Integer countryId = request.getCountryId();
        Integer cityId = request.getCityId();
        if (request.getPoiType() != null && StringUtils.isNotEmpty(request.getPoiCode())) {
            SeoHotDestinatioinfoDO destinationInfo = seoService.queryHotDestinationFirst(null, null, request.getPoiType(), request.getPoiCode());
            if (destinationInfo != null) {
                cityId = destinationInfo.getCityId();
            }
        }
        if (Optional.ofNullable(countryId).orElse(0) <= 0 && Optional.ofNullable(cityId).orElse(0) <= 0) {
            response.setBaseResponse(ResponseUtil.fail("country or city id error"));
            return response;
        }
        String locale = StringUtils.isNotEmpty(request.getBaseRequest().getLocale()) ? request.getBaseRequest().getLocale() : "en-US";
        ListCommentsResponseType commentsResponse = ucpServiceProxy.queryComment(locale, countryId, cityId);
        response.setTotalCount(0);
        if (commentsResponse != null) {
            response.setCommentList(commentsResponse.getComments().stream().map(l -> {
                CommentInfo info = new CommentInfo();
                info.setAvatarUrl(l.getUserInfo().getAvatarUrl());
                info.setUserDisplayName(l.getUserInfo().getDisplayName());
                info.setContent(l.getContent());
                info.setTranslatedContent(l.getTranslatedContent());
                info.setSocre(l.getScore());
                info.setCommentTime(seoService.commentDateFormat(l.getCommentTime(), locale));
                info.setVehicleName(getVehicleName(l.getExtInfoMap()));
                return info;
            }).collect(Collectors.toList()));
            response.setTotalCount(commentsResponse.getTotalCount());
        }
        response.setTitle(getTitle(countryId, cityId, request.getBaseRequest().getLocale()));
        response.setBaseResponse(ResponseUtil.success());
        Metrics.build().withTag("country", Optional.ofNullable(countryId).orElse(0).toString())
                .withTag("city", Optional.ofNullable(cityId).orElse(0).toString())
                .withTag("result", CollectionUtils.isNotEmpty(response.getCommentList()) ? "1" : "0")
                .recordOne(MetricsEnum.SEO_COMMENT.getTitle());
        return response;
    }

    private String getVehicleName(Map<String, String> extInfoMap) {
        return extInfoMap != null ? extInfoMap.get("vehicleName") : null;
    }

    private String getTitle(Integer countryId, Integer cityId, String locale) {
        //城市
        if (Optional.ofNullable(cityId).orElse(0) > 0) {
            String cityName = seoService.getCityName(cityId, locale);
            return LanguageUtils.sharkValFormat(SeoShark.RentalCommentTitle.getValue(locale), cityName);
        }
        //国家
        String countryName = seoService.getCountryName(countryId, locale);
        return LanguageUtils.sharkValFormat(SeoShark.RentalCommentTitle.getValue(locale), countryName);
    }
}
