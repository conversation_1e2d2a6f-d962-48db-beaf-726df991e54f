package com.ctrip.car.market.coupon.restful.enums;

import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

public enum TripCrossRecommendShark {

    TRAIN_RENTAL_CAR_CARD_TITLE("trip.train.rental.card.title", ""),
    TRAIN_RENTAL_CAR_COMPONENT_TITLE("trip.train.rental.component.title", ""),
    TRAIN_RENTAL_CAR_TOP_SALE("trip.train.rental.top.sale.recommendation", ""),
    TRAIN_RENTAL_CAR_USER_EXCLUSIVE("trip.train.rental.user.exclusive.recommendation", ""),
    TRAIN_RENTAL_CAR_FAMILY("trip.train.rental.family.recommendation", "");

    @Getter
    private final String sharkKey;
    private final String defaultValue;

    TripCrossRecommendShark(String sharkKey, String defaultValue) {
        this.sharkKey = sharkKey;
        this.defaultValue = defaultValue;
    }

    public String getValue(String locale) {
        if (StringUtils.isEmpty(locale)) {
            locale = "en-US";
        }
        String value = Shark.getByLocale(sharkKey, locale);
        if (StringUtils.isEmpty(value)) {
            return defaultValue;
        }
        return value;
    }

}
