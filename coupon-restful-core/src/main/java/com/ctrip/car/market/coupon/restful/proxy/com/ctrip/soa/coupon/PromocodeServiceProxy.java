package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon;

import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.coupon.restful.contract.PromotionStrategyItemDTO;
import com.ctrip.car.market.coupon.restful.dto.UrlInfoDTO;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.soa.platform.account.promocodeservice.data.v1.PromotionCouponCode;
import com.ctrip.soa.platform.account.promocodeservice.data.v1.PromotionStrategyItem;
import com.ctrip.soa.platform.account.promocodeservice.message.v1.*;
import com.ctrip.soa.platform.account.promocodeservice.v1.PromocodeServiceClient;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.swing.text.html.Option;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class PromocodeServiceProxy {
    private static final ILog log = LogManager.getLogger("PromocodeServiceProxy");
    private static final PromocodeServiceClient client = PromocodeServiceClient.getInstance();



    @NCache(holdMinute = 20)
    public UrlInfoDTO getPromotionStrategy(int PromotionId) {
        UrlInfoDTO urlInfoDTO = null;
        GetPromotionStrategyRequestType request = new GetPromotionStrategyRequestType();
        request.setProductLineID(18);
        request.setPromotionID(PromotionId);
        request.setIsNeedProductlineUrl(true);
        try {
            GetPromotionStrategyResponseType responseType = client.getPromotionStrategy(request);
            if (responseType != null && responseType.getPromotionStrategy() != null) {
                if (  !StringUtils.isBlank(responseType.getPromotionStrategy().getH5URL()) ||
                        !StringUtils.isBlank(responseType.getPromotionStrategy().getAppURL())
                        ) {
                    urlInfoDTO = new UrlInfoDTO();
                    urlInfoDTO.setAlipayID(responseType.getPromotionStrategy().getAlipayID());
                    urlInfoDTO.setAlipayUrl(responseType.getPromotionStrategy().getAlipayUrl());
                    urlInfoDTO.setBaiduID(responseType.getPromotionStrategy().getBaiduID());
                    urlInfoDTO.setBaiduUrl(responseType.getPromotionStrategy().getBaiduUrl());
                    urlInfoDTO.setAppURL( responseType.getPromotionStrategy().getAppURL());
                    urlInfoDTO.setH5URL(responseType.getPromotionStrategy().getH5URL());
                    urlInfoDTO.setPcURL(responseType.getPromotionStrategy().getPcURL());
                    urlInfoDTO.setWeChatAppletID(responseType.getPromotionStrategy().getWeChatAppletID());
                    urlInfoDTO.setWeChatAppletUrl(responseType.getPromotionStrategy().getWeChatAppletUrl());
                } else {
                    return null;
                }
            }
        } catch (Exception ex) {
            log.error("getPromotionStrategy", ex);
        }
        return urlInfoDTO;
    }

}
