package com.ctrip.car.market.coupon.restful.business.tripCarCrossRecommend;

import com.ctrip.car.market.coupon.restful.contract.ServiceType;
import com.ctrip.car.market.coupon.restful.contract.TripRecommendSceneEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
public @interface TripCrossRecommendAnnotation {

    // 交叉推荐bu
    TripRecommendSceneEnum targetDepartment();

    // 两车业务线
    ServiceType serviceType();
}
