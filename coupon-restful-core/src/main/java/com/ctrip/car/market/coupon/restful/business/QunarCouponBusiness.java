package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.client.contract.BaseRequest;
import com.ctrip.car.market.client.contract.QueryQunarCouponListRequestType;
import com.ctrip.car.market.client.contract.QueryQunarCouponListResponseType;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.dto.QunarUserInfo;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.QunarUidServiceProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.SDMarketingServiceProxy;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.stream.Collectors;

@Component
public class QunarCouponBusiness {

    @Resource
    private SDMarketingServiceProxy sdMarketingServiceProxy;

    @Resource
    private QunarUidServiceProxy qunarUidServiceProxy;

    public QueryQunarCouponResponseType queryQunarCoupon(QueryQunarCouponRequestType request) {
        QueryQunarCouponResponseType response = new QueryQunarCouponResponseType();
        QunarUserInfo userInfo = qunarUidServiceProxy.queryQunarUid(request.getScookie());
        if (userInfo == null || userInfo.getUid() == null) {
            response.setStatus(3);
            response.setMessage("USER_NOT_FOUND");
            return response;
        }
        QueryQunarCouponListRequestType soaReq = requestConvert(userInfo.getUid().toString(), request);
        QueryQunarCouponListResponseType soaRes = sdMarketingServiceProxy.queryQunarCouponList(soaReq);
        if (soaRes != null) {
            response.setStatus(soaRes.getStatus());
            response.setMessage(soaRes.getMessage());
            response.setData(couponConvert(soaRes.getData()));
            return response;
        }
        response.setStatus(-1);
        response.setMessage("SERVER_ERROR");
        return response;
    }

    private QueryQunarCouponListRequestType requestConvert(String uid, QueryQunarCouponRequestType request) {
        QueryQunarCouponListRequestType soaReq = new QueryQunarCouponListRequestType();
        soaReq.setBiz(request.getBiz());
        soaReq.setPlatform(request.getPlatform());
        soaReq.setBd_source(request.getBd_source());
        soaReq.setBaseRequest(new BaseRequest());
        soaReq.getBaseRequest().setUid(uid);
        return soaReq;
    }

    private CouponDataDto couponConvert(com.ctrip.car.market.client.contract.CouponDataDto couponDataDto) {
        CouponDataDto obj = new CouponDataDto();
        obj.setBiz(couponDataDto.getBiz());
        obj.setTab(new CouponTabDto());
        obj.getTab().setCount(couponDataDto.getTab().getCount());
        obj.getTab().setTitle(couponDataDto.getTab().getTitle());
        obj.setCouponList(couponDataDto.getCouponList().stream().map(li->{
            QunarCouponItem item = new QunarCouponItem();
            item.setBiz(li.getBiz());
            item.setCouponId(li.getCouponId());
            item.setCouponName(li.getCouponName());
            item.setCouponType(li.getCouponType());
            item.setCouponValue(new QunarCouponItemValue());
            item.getCouponValue().setLimit(li.getCouponValue().getLimit());
            item.getCouponValue().setNumber(li.getCouponValue().getNumber());
            item.getCouponValue().setType(li.getCouponValue().getType());
            item.setBdColor(li.getBdColor());
            item.setButtonColor(li.getButtonColor());
            item.setDesc(li.getDesc());
            item.setShortDesc(li.getShortDesc());
            item.setEndDate(li.getEndDate());
            item.setStartDate(li.getStartDate());
            item.setIcon(li.getIcon());
            item.setIsApplet(li.getIsApplet());
            item.setRealCouponDiscount(li.getRealCouponDiscount());
            item.setStatus(li.getStatus());
            item.setUseUrl(li.getUseUrl());
            item.setRealCouponValue(li.getRealCouponValue());
            return item;
        }).collect(Collectors.toList()));
        return obj;
    }
}
