package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.qunar;

import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import com.ctrip.car.market.coupon.restful.utils.QConfigUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.framework.foundation.Env;
import com.ctrip.framework.foundation.Foundation;
import lombok.Data;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import java.sql.Timestamp;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class QunarUserScookieProxy {

    private static final MediaType JSON_MEDIA = MediaType.parse("application/json");
    private static final ILog LOGGER = LogManager.getLogger(QunarUserScookieProxy.class);
    private static final OkHttpClient HTTP_CLIENT;

    static {
        HTTP_CLIENT = new OkHttpClient.Builder()
                .readTimeout(2, TimeUnit.SECONDS)
                .connectTimeout(1, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true).build();
    }

    public static String getUidByScookie(String scookie) {
        if (StringUtils.isBlank(scookie)) return null;
        Map<String, String> logTags = new HashMap<>();
        logTags.put("SCOOKIE", scookie);
        String url = QConfigUtil.getByFileAndKey("qunarUrl.properties", "ScookieGetQunarUserInfo");
        if (StringUtils.isBlank(url)) return null;
        try {
            QunarUserInfoRequest qunarUserInfoRequest = new QunarUserInfoRequest();
            qunarUserInfoRequest.setScookie(scookie);
            String requestPayload = JsonUtils.convertObject2Json(qunarUserInfoRequest);
            String responsePayload = post(url, requestPayload);
            LOGGER.info("getUidByScookie", "request => " + requestPayload + "\n" + "response => " + responsePayload, logTags);
            if (StringUtils.isNotBlank(responsePayload)) {
                QunarUserInfoResponse qunarUserInfoResponse = JsonUtils.convertJson2Object(responsePayload, QunarUserInfoResponse.class);
                if (Objects.nonNull(qunarUserInfoResponse.getSimpleUserInfo())) {
                    return qunarUserInfoResponse.getSimpleUserInfo().getUid().toString();
                }
            }
        } catch (Exception ex) {
            LOGGER.error("getUidByScookie", ex, logTags);
        }
        return null;
    }

    private static String post(String requestUrl, String requestPayLoad) {
        try {
            RequestBody requestBody = RequestBody.create(JSON_MEDIA, requestPayLoad);
            Request httpRequest = new Request.Builder()
                    .url(requestUrl)
                    .post(requestBody)
                    .build();
            Response response = HTTP_CLIENT.newCall(httpRequest).execute();
            if (Objects.nonNull(response.body())) {
                return response.body().string();
            }
        } catch (Exception e) {
            LOGGER.info("QunarUserScookieProxy.post", e);
        }
        return "";
    }

    @Data
    static class QunarUserInfoRequest {
        private String scookie;
    }

    @Data
    static class BaseResponse {
        private Integer code;
        private Boolean isSuccess;
        private String message;
        private String requestId;
    }

    @Data
    static class SimpleUserInfo {
        private String cookies;
        private Boolean expired;
        private Boolean guest;
        private Timestamp logintime;
        private Boolean logout;
        private String mobile;
        private String msg;
        private Integer status;
        private Integer type;
        private Long uid;
        private String userName;
    }

    @Data
    static class QunarUserInfoResponse {
        private BaseResponse baseResponse;
        private String jsonUser;
        private SimpleUserInfo simpleUserInfo;
    }

}
