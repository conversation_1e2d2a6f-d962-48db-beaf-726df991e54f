package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.seoplatform;

import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.CarMarketReadCache;
import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.ibu.seo.platform.admin.contract.GetSingleSeoInfRequestType;
import com.ctrip.ibu.seo.platform.admin.contract.GetSingleSeoInfResponseType;
import com.ctrip.ibu.seo.platform.admin.contract.InfType;
import com.ctrip.ibu.seo.platform.admin.contract.SeoPlatformManagerClient;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Component
public class SeoPlatformProxy {

    private final ILog logger = LogManager.getLogger(SeoPlatformProxy.class);

    private final SeoPlatformManagerClient client = SeoPlatformManagerClient.getInstance();

    @Resource
    private TripConfig tripConfig;

    @NCache(holdMinute = 60, reHoldMinute = 30)
    public String getSeoName(Integer type, String locale, String code) {
        try {
            GetSingleSeoInfRequestType requestType = new GetSingleSeoInfRequestType();
            switch (type) {
                case 1:
                    requestType.setType(InfType.COUNTRY_ID);
                    break;
                case 2:
                    requestType.setType(InfType.PROVINCE_ID);
                    break;
                case 3:
                    requestType.setType(InfType.BASE_CITY_ID);
                    break;
                case 4:
                    requestType.setType(InfType.AIRPORT_CODE);
                    break;
            }
            requestType.setLocale(locale);
            requestType.setInfCode(code);
            GetSingleSeoInfResponseType responseType = client.getSeoInf(requestType);
            Map<String, String> tag = Maps.newHashMap();
            tag.put("locale", locale);
            tag.put("type", String.valueOf(type));
            tag.put("code", code);
            logger.info("getSeoInf", JsonUtils.convertObject2Json(responseType), tag);
            String name = null;
            if (responseType != null && responseType.getInfo() != null) {
                name = responseType.getInfo().getOriginName();
                if (StringUtils.isNotEmpty(responseType.getInfo().getSeoName())) {
                    name = responseType.getInfo().getSeoName();
                }
            }
            return name;
        } catch (Exception e) {
            logger.warn("querySeoName", e);
            return null;
        }
    }

    public String querySeoName(int type, String locale, String code) {
        if (StringUtils.isEmpty(locale) || StringUtils.isEmpty(code)) {
            return null;
        }
        String cacheValue = getCache(type, locale, code);
        if (StringUtils.isNotEmpty(cacheValue)) {
            return cacheValue;
        }
        return getSeoName(type, locale, code);
    }


    private String buildKey(int type, String locale, String code) {
        return String.format("car.market.seo.platform.data.cache.%s.%s.%s", type, locale, code).toLowerCase();
    }

    private String getCache(int type, String locale, String code) {
        try {
            String key = buildKey(type, locale, code);
            return CarMarketReadCache.get(key);
        } catch (Exception e) {
            logger.warn("getCache", e);
            return null;
        }
    }

}
