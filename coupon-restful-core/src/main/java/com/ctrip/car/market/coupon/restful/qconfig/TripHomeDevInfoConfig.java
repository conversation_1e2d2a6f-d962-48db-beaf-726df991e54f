package com.ctrip.car.market.coupon.restful.qconfig;

import com.ctrip.car.market.coupon.restful.pojo.TripHomeDevInfoQO;
import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Arrays;

@Component
public class TripHomeDevInfoConfig {

    private TripHomeDevInfoQO tripHomeDevInfoQO = null;

    @QConfig("TripHomepage.json")
    public void tripHomeDevInfoChanged(String config) throws Exception {
        this.tripHomeDevInfoQO = JsonUtils.convertJson2Object(config, TripHomeDevInfoQO.class);
    }

    public Boolean tripHomeDevInfoEnable(String sourceFrom) {
        if (this.tripHomeDevInfoQO == null) {
            return false;
        }
        String sources = this.tripHomeDevInfoQO.getSources();
        if (StringUtils.isBlank(sources) || CollectionUtils.isEmpty(Arrays.asList(sources.split(",")))) {
            return false;
        }
        return Arrays.asList(sources.split(",")).contains(sourceFrom);
    }

    public TripHomeDevInfoQO getTripHomeDevInfo() {
        return tripHomeDevInfoQO;
    }
}
