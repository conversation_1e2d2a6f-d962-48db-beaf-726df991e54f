package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.infosec.kms.KmsUtilCacheable;
import com.ctrip.infosec.kms.pojo.KmsKey;
import com.ctrip.infosec.kms.pojo.KmsResponse;

import java.util.HashMap;
import java.util.Objects;

public class KMSUtil {

    private static final ILog logger = LogManager.getLogger(KMSUtil.class);

    public static String getByKeyV2(String key) {
        HashMap<String, String> logTagMap = new HashMap<>();
        logTagMap.put("key", key);
        String kmsToken = QConfigUtil.getByFileAndKey("groupIDAESV2.properties", key);
        logTagMap.put("kmsToken", kmsToken);
        try {
            KmsResponse<KmsKey> kmsKeyResponse = KmsUtilCacheable.getKey(kmsToken);
            if (Objects.nonNull(kmsKeyResponse) && kmsKeyResponse.getCode() == 0) {
                KmsKey kmsKey = kmsKeyResponse.getResult();
                if (Objects.nonNull(kmsKey)) {
                    return kmsKey.getKeyValue();
                }
            } else {
                logger.warn("getByKey: message", kmsKeyResponse.getMessage() + "", logTagMap);
            }
            logger.warn("getByKey: key", key, logTagMap);
            return "";
        } catch (Exception e) {
            logger.error("getByKey", e, logTagMap);
        }
        return "";
    }
}
