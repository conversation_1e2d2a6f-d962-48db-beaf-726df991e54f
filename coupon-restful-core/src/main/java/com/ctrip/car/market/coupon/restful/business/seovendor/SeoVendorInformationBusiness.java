package com.ctrip.car.market.coupon.restful.business.seovendor;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache;
import com.ctrip.car.market.coupon.restful.contract.seo.InformationInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoInformationResponseType;
import com.ctrip.car.market.coupon.restful.enums.InformationEnum;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.job.common.entity.seo.*;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class SeoVendorInformationBusiness {

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private SeoService service;

    @Resource
    private SeoVendorVehicleBusiness seoVendorVehicleBusiness;

    public QuerySeoInformationResponseType queryInformation(QuerySeoInformationRequestType request) {
        QuerySeoInformationResponseType response = new QuerySeoInformationResponseType();
        SeoHotVendorCityDO vendorCityDO = Optional.ofNullable(request.getCityId()).orElse(0) > 0 ? seoVendorCache.queryVendorCity(request.getVendorCode(), request.getCityId()) : null;
        SeoHotVendorDO vendorDO = seoVendorCache.queryVendor(request.getVendorCode());
        if (vendorDO == null) {
            return response;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) > 0 && vendorCityDO == null) {
            return response;
        }
        SeoHotVendorInformationDO vendorInformationDO = Optional.ofNullable(request.getCityId()).orElse(0) > 0
                ? service.queryVendorCity(request.getVendorCode(), request.getCityId())
                : service.queryVendorHotCity(request.getVendorCode());

        if (vendorInformationDO == null) {
            return response;
        }
        response.setInformationList(getInformation(vendorInformationDO, vendorDO, request));
        return response;
    }

    private List<InformationInfo> getInformation(SeoHotVendorInformationDO vendorInformationDO, SeoHotVendorDO vendorDO, QuerySeoInformationRequestType request) {
        List<InformationInfo> result = Lists.newArrayList();
        String locale = request.getBaseRequest().getLocale();
        //热门供应商
        if (StringUtils.isNotEmpty(vendorDO.getVendorName())) {
            result.add(getInformation(InformationEnum.Vendor.getType(), vendorDO.getVendorName(), locale));
        }
        //热门车型组
        if (Optional.ofNullable(vendorInformationDO.getVehicleGroupId()).orElse(0) > 1) {
            String vehicleGroupName = service.getVehicleGroupName(vendorInformationDO.getVehicleGroupId().toString(), "", locale);
            result.add(getInformation(InformationEnum.VehicleGroup.getType(), vehicleGroupName, locale));
            SeoHotVendorInformationDO vendorPoi = Optional.ofNullable(request.getCityId()).orElse(0) > 0
                    ? service.queryVendorHotCityDefault(request.getVendorCode(), request.getCityId())
                    : service.queryVendorHotCityDefault(request.getVendorCode());
            //每日均价
            if (Optional.ofNullable(vendorInformationDO.getVehicleGroupId()).orElse(0) > 0) {
                BigDecimal price = getVehicleGroupPrice(vendorPoi, request, vendorInformationDO.getVehicleGroupId().toString());
                if (price != null) {
                    String priceStr = service.currencyString(price, LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(locale), CurrencyEnum.getCurrrencyEnum(request.getBaseRequest().getCurrencyCode()));
                    if (StringUtils.isNotEmpty(priceStr)) {
                        result.add(getInformation(InformationEnum.VehiclePrice.getType(), priceStr, locale));
                    }
                }
            }
        }
        //常见租期
        if (Optional.ofNullable(vendorInformationDO.getTenancy()).orElse(0) > 0) {
            if (vendorInformationDO.getTenancy() > 1) {
                result.add(getInformation(InformationEnum.Tenancy.getType(), LanguageUtils.sharkValFormat(SeoShark.HotRentalTermPluralValue.getValue(locale), vendorInformationDO.getTenancy()), locale));
            } else {
                result.add(getInformation(InformationEnum.Tenancy.getType(), LanguageUtils.sharkValFormat(SeoShark.HotRentalTermValue.getValue(locale), vendorInformationDO.getTenancy()), locale));
            }
        }

        return result;
    }

    private BigDecimal getVehicleGroupPrice(SeoHotVendorInformationDO vendorPoi, QuerySeoInformationRequestType request, String vehicleGroupCode) {
        List<QueryRecomdProductsResponseType> vehicleResponseList = seoVendorVehicleBusiness.queryVehicle(request.getBaseRequest(), request.getHead(), Lists.newArrayList(vendorPoi), true);
        QueryRecomdProductsResponseType vehicleResponse = CollectionUtils.isNotEmpty(vehicleResponseList) ? vehicleResponseList.get(0) : null;
        if (vehicleResponse != null && CollectionUtils.isNotEmpty(vehicleResponse.getRecomdProductResList()) && CollectionUtils.isNotEmpty(vehicleResponse.getRecomdProductResList().get(0).getProducts())) {
            List<RecomdProduct> vehicleList = vehicleResponse.getRecomdProductResList().get(0).getProducts().stream().filter(l -> l.getVehicle() != null && StringUtils.equalsIgnoreCase(l.getVehicle().getGroupCode(), vehicleGroupCode)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(vehicleList)) {
                return vehicleList.stream().map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
            }
        }
        return null;
    }

    private InformationInfo getInformation(int type, String name, String locale) {
        InformationInfo informationInfo = new InformationInfo();
        informationInfo.setType(type);
        informationInfo.setName(name);
        switch (type) {
            case 1:
                informationInfo.setTitle(SeoShark.HotCompany.getValue(locale));
                break;
            case 2:
                informationInfo.setTitle(SeoShark.HotVehicleGroup.getValue(locale));
                break;
            case 3:
                informationInfo.setTitle(SeoShark.HotDailyPrice.getValue(locale));
                break;
            case 4:
                informationInfo.setTitle(SeoShark.HotRentalTerm.getValue(locale));
                break;
        }
        return informationInfo;
    }
}
