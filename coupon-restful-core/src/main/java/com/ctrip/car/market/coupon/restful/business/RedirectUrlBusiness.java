package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.dto.UrlInfoDTO;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.PromocodeServiceProxy;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.mergecouponservice.interfaces.common.RequestHeader;
import com.ctrip.car.market.mergecouponservice.interfaces.service.CarMergeCouponServiceClient;
import com.ctrip.car.market.mergecouponservice.interfaces.service.message.GetRedirectUrlByPromotionIDRequestType;
import com.ctrip.car.market.mergecouponservice.interfaces.service.message.GetRedirectUrlByPromotionIDResponseType;
import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by h_zhanga on 2018/7/19.
 */
@Service
public class RedirectUrlBusiness {
    @Autowired
    public PromocodeServiceProxy promocodeServiceProxy;
    private final ILog logger = LogManager.getLogger(this.getClass());
    public final CarMergeCouponServiceClient carMergeCouponServiceClient
            = CarMergeCouponServiceClient.getInstance();


    @NCache(holdMinute = 30)
    public GetJumpUrlByPromotionIdResponseType getJumpUrlByPromotionId(GetJumpUrlByPromotionIdRequestType request) {
        GetRedirectUrlByPromotionIDRequestType requestType = new GetRedirectUrlByPromotionIDRequestType();
        GetJumpUrlByPromotionIdResponseType result = new GetJumpUrlByPromotionIdResponseType();
        if (request.getPid() != null && request.getPid() > 0) {
            requestType.setPromotionID(request.getPid().intValue());
            UrlInfoDTO urlInfoDTO = promocodeServiceProxy.getPromotionStrategy(request.getPid().intValue());
            if (urlInfoDTO != null) {
                result.setAPPUrl(StringUtils.trimToEmpty(urlInfoDTO.getAppURL()));
                result.setH5Url(StringUtils.trimToEmpty(urlInfoDTO.getH5URL()));
                result.setOnlineUrl(StringUtils.trimToEmpty(urlInfoDTO.getPcURL()));
                result.setWechatUrl(StringUtils.trimToEmpty(urlInfoDTO.getWeChatAppletUrl()));
                result.setWechatId(StringUtils.trimToEmpty(urlInfoDTO.getWeChatAppletID()));
                result.setRnUrl(StringUtils.trimToEmpty(urlInfoDTO.getAppURL()));
                result.setBaiDuUrl(StringUtils.trimToEmpty(urlInfoDTO.getBaiduUrl()));
                result.setBaiDuID(urlInfoDTO.getBaiduID());
                result.setAliPayUrl(StringUtils.trimToEmpty(urlInfoDTO.getAlipayUrl()));
                result.setAliPayID(urlInfoDTO.getAlipayID());
                return result;
            }
        }
        requestType.setEncryptedGroupId(request.getGid());
        requestType.setUnionType(request.getUnionType());
        RequestHeader requestHeader = new RequestHeader();
        requestHeader.setUid(BaseUtils.getUidByCommon(request.getHead()));
        requestType.setRequestHeader(requestHeader);
        try {
            GetRedirectUrlByPromotionIDResponseType responseType = carMergeCouponServiceClient.getRedirectUrlByPromotionID(requestType);
            if (responseType != null) {
                result.setAPPUrl(StringUtils.trimToEmpty(responseType.getAppUrl()));
                result.setH5Url(StringUtils.trimToEmpty(responseType.getH5Url()));
                result.setOnlineUrl(StringUtils.trimToEmpty(responseType.getOnlineUrl()));
                result.setUrlDec(StringUtils.trimToEmpty(responseType.getUrlDec()));
                result.setWechatUrl(StringUtils.trimToEmpty(responseType.getWeChatUrl()));
                result.setWechatId(responseType.getWeChatId());
                if (!StringUtils.isBlank(responseType.getRnUrl())) {
                    result.setRnUrl(StringUtils.trimToEmpty(responseType.getRnUrl()));
                } else {
                    result.setRnUrl(StringUtils.trimToEmpty(responseType.getAppUrl()));
                }
                result.setBaiDuUrl(StringUtils.trimToEmpty(responseType.getBaiDuUrl()));
                result.setBaiDuID(responseType.getBaiDuID());
                result.setAliPayUrl(StringUtils.trimToEmpty(responseType.getAliPayUrl()));
                result.setAliPayID(responseType.getAliPayID());
            } else {
                logger.warn("getJumpUrlByPromotionId", JsonUtil.toString(request));
            }
        } catch (Exception ex) {
            logger.error("getJumpUrlByPromotionId", ex);
        }
        return result;
    }


}
