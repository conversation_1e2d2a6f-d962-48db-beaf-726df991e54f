package com.ctrip.car.market.coupon.restful.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.CpnLabelDO;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@CreateCacheBean
public class LabelCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.LabelCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.LABEL_HASH_KEY)
    public Cache<Long, CpnLabelDO> cache;

    public CpnLabelDO getLabel(Long code) {
        return this.cache.get(code);
    }
}