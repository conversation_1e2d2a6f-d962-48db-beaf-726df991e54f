package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.arch.coreinfo.utils.StringUtil;

import java.text.MessageFormat;
import java.util.regex.Pattern;

public class LanguageUtils {

    /**
     * String.format() 匹配规则
     */
    private static final String FORMAT_SPECIFIER = "%(\\d+\\$)?([-#+ 0,(\\<]*)?(\\d+)?(\\.\\d+)?([tT])?([a-zA-Z%])";

    private static final Pattern FORMAT_PATTERN = Pattern.compile(FORMAT_SPECIFIER);

    /**
     * 左括号转义
     * 1、前面不是单引号，表示字符串本身没有转义，如：'{ 表示字符串已经做个转义，无需再转义
     * 2、后面不是单个数字+右括号，排除正常的占位符，如：{0} 左括号不用转义
     */
    private static final String BRACE_LEFT_REGEX = "(?<!')\\{(?!\\d+\\})";
    /**
     * 右括号转义
     * 1、前面不是左括号+单个数字，排除正常的占位符，如：{0} 右括号不用转义
     */
    private static final String BRACE_RIGHT_REGEX = "(?<!\\{\\d{1,1000})\\}";
    /**
     * 单引号匹配规则
     * 1、前后都不是单引号，表示字符串本身没有转义，如：'' 表示字符串已经做个转义，无需再转义
     * 2、后面不是左括号，便是字符串本身没有对左括号转义，如: '{ 表示字符串已经做个转义，无需再转义
     */
    private static final String SINGLEQUOTES_REGEX = "(?<!')'(?!'|\\{)";
    /**
     * 格式化不成功的条件正则
     */
    private static final Pattern FORMAT_UNEXPECTED_PATTERN = Pattern.compile("\\{\\d+\\}");



    /**
     * 对shark配置文案进行格式化
     * 包含%s则使用String.format，否则以{0}{1}进行格式化
     *
     * @param pattern   格式化
     * @param arguments 参数
     * @return 格式化后文案
     */
    public static String sharkValFormat(String pattern, Object... arguments) {
        if (StringUtil.isBlank(pattern)) {
            return "";
        }
        // 匹配 String.format 并且不包含{n}占位符
        if (FORMAT_PATTERN.matcher(pattern).find() && !FORMAT_UNEXPECTED_PATTERN.matcher(pattern).find()) {
            return String.format(pattern, arguments);
        }
        Object[] args = new Object[arguments.length];
        for (int i = 0; i < arguments.length; i++) {
            Object obj = arguments[i];
            if (obj == null) {
                args[i] = "null";
            } else if (obj instanceof Number) {
                args[i] = obj + "";
            } else if (obj instanceof String) {
                args[i] = obj;
            } else {
                args[i] = obj.toString();
            }
        }

        // MessageFormat.format无法识别的特殊字符转义
        String escapePattern = pattern.replaceAll(SINGLEQUOTES_REGEX, "''")
                .replaceAll(BRACE_LEFT_REGEX, "'{")
                .replaceAll(BRACE_RIGHT_REGEX, "'}");

        String formatStr = "";
        try {
            formatStr = MessageFormat.format(escapePattern, args);
        } catch (Exception ex) {

        }

        return formatStr;
    }
}
