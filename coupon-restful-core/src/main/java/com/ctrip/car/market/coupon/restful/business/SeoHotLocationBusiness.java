package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.seo.LocationInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoHotLocationRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoHotLocationResponseType;
import com.ctrip.car.market.coupon.restful.contract.seo.UrlInfo;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.dcs.geo.domain.value.City;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SeoHotLocationBusiness {

    @Resource
    private SeoService service;

    @Resource
    private TripConfig tripConfig;

    public QuerySeoHotLocationResponseType queryHotLocation(QuerySeoHotLocationRequestType request) {
        QuerySeoHotLocationResponseType response = new QuerySeoHotLocationResponseType();
        if (!checkRequest(request)) {
            Metrics.build().withTag("errorType", "paraError").withTag("result", "0").recordOne(MetricsEnum.SEO_LOCATION.getTitle());
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        List<SeoHotDestinatioinfoDO> destinatioinfoList = service.queryHotDestination(request.getCountryId(), request.getCityId(), request.getPoiType(), request.getPoiCode());
        if (CollectionUtils.isEmpty(destinatioinfoList)) {
            Metrics.build().withTag("errorType", "destinationError").withTag("result", "0").recordOne(MetricsEnum.SEO_LOCATION.getTitle());
            response.setBaseResponse(ResponseUtil.fail("destination error"));
            return response;
        }
        //国家id
        Integer countryId = destinatioinfoList.get(0).getCountryId();
        List<SeoHotDestinatioinfoDO> list = Lists.newArrayList();
        //机场、城市
        if ((Optional.ofNullable(request.getPoiType()).orElse(0) > 0 && StringUtils.isNotEmpty(request.getPoiCode()))
                || Optional.ofNullable(request.getCityId()).orElse(0) > 0) {
            list = service.queryHotDestination(countryId, null, null, null);
        } else {
            list = destinatioinfoList;
        }
        List<SeoHotCityinfoDO> cityList = service.queryHotCity(countryId);
        response.setLocationList(convert(cityList, list, countryId, request.getBaseRequest().getLocale(), request.getCityId()));
        Metrics.build().withTag("country", Optional.ofNullable(destinatioinfoList.get(0).getCountryId()).orElse(0).toString())
                .withTag("city", Optional.ofNullable(destinatioinfoList.get(0).getCityId()).orElse(0).toString())
                .withTag("result", CollectionUtils.isNotEmpty(response.getLocationList()) ? "1" : "0")
                .recordOne(MetricsEnum.SEO_LOCATION.getTitle());
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private List<LocationInfo> convert(List<SeoHotCityinfoDO> cityList, List<SeoHotDestinatioinfoDO> destanationList, Integer countryId, String locale, Integer cityId) {
        List<LocationInfo> result = Lists.newArrayList();
        String countryName = service.getCountryName(countryId, locale);
        City cityInfo = Optional.ofNullable(cityId).orElse(0) > 0 ? service.getCity(cityId) : null;
        //台湾站特殊处理
        if ("zh-tw".equalsIgnoreCase(locale) && Objects.equals(countryId, 1)) {
            //台湾的城市页面
            if (cityInfo != null && Objects.equals(cityInfo.getProvinceId(), 53L)) {
                countryName = service.getProvinceName(53, locale);
                cityList = cityList.stream().filter(l -> tripConfig.getTwCityList().contains(l.getCityId())).collect(Collectors.toList());
            } else {
                //排除台湾的城市
                cityList = cityList.stream().filter(l -> !tripConfig.getTwCityList().contains(l.getCityId())).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isNotEmpty(cityList)) {
            //城市单量
            Map<Integer, Long> cityOrderMap = service.getCityOrderNum(destanationList);
            List<SeoHotCityinfoDO> citys = cityList.stream().sorted(Comparator.comparing(l -> cityOrderMap.getOrDefault(l.getCityId(), 999L))).limit(30).collect(Collectors.toList());
            Map<Long, City> cityMap = service.getCity(citys.stream().map(l -> l.getCityId().longValue()).distinct().collect(Collectors.toList()), locale);
            LocationInfo city = new LocationInfo();
            city.setTitle(LanguageUtils.sharkValFormat(SeoShark.City.getValue(locale), countryName));
            city.setType(1);
            city.setUrlList(citys.stream().map(l -> {
                if (!cityMap.containsKey(l.getCityId().longValue()) || StringUtils.isEmpty(l.getUrl())) {
                    return null;
                }
                UrlInfo urlInfo = new UrlInfo();
                urlInfo.setUrl(service.getSiteUrl(l.getUrl(), locale));
                String name = LanguageUtils.sharkValFormat(SeoShark.CarRental.getValue(locale), cityMap.get(l.getCityId().longValue()).getTranslationName());
                urlInfo.setName(name);
                return urlInfo;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
            result.add(city);
        }
        return result;
    }

    private boolean checkRequest(QuerySeoHotLocationRequestType request) {
        if (request.getBaseRequest() == null) {
            return false;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) <= 0
                && Optional.ofNullable(request.getCountryId()).orElse(0) <= 0
                && StringUtils.isEmpty(request.getPoiCode())) {
            return false;
        }
        if (StringUtils.isEmpty(request.getBaseRequest().getRequestId())) {
            request.getBaseRequest().setRequestId(UUID.randomUUID().toString());
        }
        return true;
    }
}
