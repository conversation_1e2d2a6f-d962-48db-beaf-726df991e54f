package com.ctrip.car.market.coupon.restful.dto;

/**
 * Created by h_zhanga on 2018/6/13.
 */
public class CommonUserInfoDTO {
    public String AvatarUrl;
    public String BindMobilePhone;
    public String CNName;
    public String ContactEmail;
    public int ContactFaxPrefix;
    public String ContactTel;
    public String Gender;
    public String IsMailbinding;
    public String IsMobilebinding;
    public String MobilePhone;
    public String NickName;
    public String UID;

    public CommonUserInfoDTO() {
    }

    public String getAvatarUrl() {
        return AvatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        AvatarUrl = avatarUrl;
    }

    public String getBindMobilePhone() {
        return BindMobilePhone;
    }

    public void setBindMobilePhone(String bindMobilePhone) {
        BindMobilePhone = bindMobilePhone;
    }

    public String getCNName() {
        return CNName;
    }

    public void setCNName(String CNName) {
        this.CNName = CNName;
    }

    public String getContactEmail() {
        return ContactEmail;
    }

    public void setContactEmail(String contactEmail) {
        ContactEmail = contactEmail;
    }

    public int getContactFaxPrefix() {
        return ContactFaxPrefix;
    }

    public void setContactFaxPrefix(int contactFaxPrefix) {
        ContactFaxPrefix = contactFaxPrefix;
    }

    public String getContactTel() {
        return ContactTel;
    }

    public void setContactTel(String contactTel) {
        ContactTel = contactTel;
    }

    public String getGender() {
        return Gender;
    }

    public void setGender(String gender) {
        Gender = gender;
    }

    public String getIsMailbinding() {
        return IsMailbinding;
    }

    public void setIsMailbinding(String isMailbinding) {
        IsMailbinding = isMailbinding;
    }

    public String getIsMobilebinding() {
        return IsMobilebinding;
    }

    public void setIsMobilebinding(String isMobilebinding) {
        IsMobilebinding = isMobilebinding;
    }

    public String getMobilePhone() {
        return MobilePhone;
    }

    public void setMobilePhone(String mobilePhone) {
        MobilePhone = mobilePhone;
    }

    public String getNickName() {
        return NickName;
    }

    public void setNickName(String nickName) {
        NickName = nickName;
    }

    public String getUID() {
        return UID;
    }

    public void setUID(String UID) {
        this.UID = UID;
    }
}
