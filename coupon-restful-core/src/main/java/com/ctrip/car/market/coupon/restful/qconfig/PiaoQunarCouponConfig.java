package com.ctrip.car.market.coupon.restful.qconfig;

import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

@Component
public class PiaoQunarCouponConfig {

    @QConfig("piaoQunarCouponConfig.properties")
    public void onChange(Map<String,String> map) {
        this.qunarUrl = map.getOrDefault("qunarUrl","https://piao.qunar.com/ticket/api/common/router.json");
        this.t = map.get("t");
        this.k = map.get("k");
        this.httpTimeOut = Long.valueOf(map.getOrDefault("httpTimeOut","10000"));
        this.newMaterialUrl = map.get("newMaterialUrl");
        this.oldMaterialUrl = map.get("oldMaterialUrl");
    }

    private String qunarUrl;

    private String t;

    private String k;

    private Long httpTimeOut;

    private String newMaterialUrl;

    private String oldMaterialUrl;

    public String getQunarUrl() {
        return qunarUrl;
    }

    public String getT() {
        return t;
    }

    public String getK() {
        return k;
    }

    public String getNewMaterialUrl() {
        return newMaterialUrl;
    }

    public String getOldMaterialUrl() {
        return oldMaterialUrl;
    }

    public Long getHttpTimeOut() {
        return httpTimeOut;
    }
}
