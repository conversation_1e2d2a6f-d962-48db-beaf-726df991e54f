package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;

import java.lang.reflect.Field;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 返回状态统一处理工具类
 */
public class ResponseCodeUtils {
    private final static ILog log= LogManager.getLogger(ResponseCodeUtils.class);
    private final static Map<Class,CodeMessageFieldHolder> _messageField=new ConcurrentHashMap<>();
    private static class CodeMessageFieldHolder{
        Field messageField;
        Field resultCodeField;

        public CodeMessageFieldHolder(Field messageField, Field resultCodeField) {
            this.messageField = messageField;
            this.resultCodeField = resultCodeField;
        }
    }



    public static <T> T res(int resultCode, String resultMessage, Class<T> t){
        try {
            T resInstance=t.newInstance();
            CodeMessageFieldHolder holder=_messageField.get(t);
            if(holder==null){
                synchronized (t){
                    if((holder=_messageField.get(t))==null){
                        Field messageField=t.getDeclaredField("resultMessage");
                        messageField.setAccessible(true);
                        Field resultCodeField=t.getDeclaredField("resultCode");
                        resultCodeField.setAccessible(true);
                        _messageField.put(t,holder=new CodeMessageFieldHolder(messageField,resultCodeField));
                    }
                }
            }
            holder.messageField.set(resInstance,resultMessage);
            holder.resultCodeField.set(resInstance,resultCode);
            return resInstance;
        } catch (NoSuchFieldException e) {
            log.error(e);
        } catch (IllegalAccessException e) {
            log.error(e);
        } catch (InstantiationException e) {
            log.error(e);
        }
        return null;
    }


    public static <T> T res(ResultOpenStatus resultOpenStatus, Class<T> t){
        try {
            T resInstance=t.newInstance();
            CodeMessageFieldHolder holder=_messageField.get(t);
            if(holder==null){
                synchronized (t){
                    if((holder=_messageField.get(t))==null){
                        Field messageField=t.getDeclaredField("resultMessage");
                        if(messageField!=null){
                            messageField.setAccessible(true);
                        }
                        Field resultCodeField=t.getDeclaredField("resultCode");
                        if(resultCodeField!=null){
                            resultCodeField.setAccessible(true);
                        }
                        _messageField.put(t,holder=new CodeMessageFieldHolder(messageField,resultCodeField));
                    }
                }
            }
            holder.messageField.set(resInstance,resultOpenStatus.getMessage());
            holder.resultCodeField.set(resInstance,resultOpenStatus.getCode());
            return resInstance;
        } catch (NoSuchFieldException e) {
            log.error(e);
        } catch (IllegalAccessException e) {
            log.error(e);
        } catch (InstantiationException e) {
            log.error(e);
        }
        return null;
    }

}
