package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping;

import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.osd.translate.dto.*;
import com.ctrip.car.osd.translate.methodtype.CarTranslateServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Component
public class TmsProxy {

    private final static String defaultLocale = "en-US";

    private final ILog logger = LogManager.getLogger(TmsProxy.class);

    private final CarTranslateServiceClient translateServiceClient = CarTranslateServiceClient.getInstance();

    @Resource
    private TripConfig tripConfig;

    @NCache(holdMinute = 60, reHoldMinute = 30)
    public String getTranslateValue(String key, String locale) {
        if (StringUtils.isEmpty(locale)) {
            locale = defaultLocale;
        }
        try {
            List<TranslateRequestInfo> translateList = Lists.newArrayList();
            TranslateRequestInfo requestInfo = new TranslateRequestInfo();
            requestInfo.setTargetLocale(locale);
            requestInfo.setStandardKey(key);
            translateList.add(requestInfo);

            TranslateRequestInfo defaultRequestInfo = new TranslateRequestInfo();
            defaultRequestInfo.setTargetLocale(defaultLocale);
            defaultRequestInfo.setStandardKey(key);
            translateList.add(defaultRequestInfo);

            TranslateRequestType request = new TranslateRequestType();
            request.setBuCode(tripConfig.getTmsBuCode());
            request.setTranslateType(TranslateType.tms);
            request.setParams(translateList);
            TranslateResponseType response = translateServiceClient.translate(request);
            List<TranslateResponseInfo> result = Objects.nonNull(response) && CollectionUtils.isNotEmpty(response.getResponseInfo())
                    ? response.getResponseInfo() : Lists.newArrayList();
            if (CollectionUtils.isEmpty(result)) {
                logger.warn("getTranslateValue", key + "_" + locale);
                return null;
            }
            TranslateResponseInfo translateInfo = get(result, locale);
            if (translateInfo == null) {
                translateInfo = get(result, defaultLocale);
            }
            return Objects.nonNull(translateInfo) && CollectionUtils.isNotEmpty(translateInfo.getResults()) ? translateInfo.getResults().get(0).getResult() : null;
        } catch (Exception e) {
            logger.warn("getTranslateValue", e);
            return null;
        }
    }

    private TranslateResponseInfo get(List<TranslateResponseInfo> list, String locale) {
        return list.stream().filter(l -> CollectionUtils.isNotEmpty(l.getResults())
                && l.getResults().stream().anyMatch(li -> StringUtils.equalsIgnoreCase(li.getTargetLocale(), locale))).findFirst().orElse(null);
    }
}
