package com.ctrip.car.market.coupon.restful.enums;

import com.ctrip.car.market.coupon.restful.qconfig.QConfigHelper;

public enum  ResultOpenStatus {
    SUCCESS(200, QConfigHelper.getChineseTips("SUCCESS")),
    NO_LOGIN(-2, QConfigHelper.getChineseTips("NO_LOGIN")),
    ERROR_PARAMS(-1, QConfigHelper.getChineseTips("ERROR_PARAMS")),
    SYSTEM_ECEPTION(500, QConfigHelper.getChineseTips("SYSTEM_ECEPTION")),
    TEL_NOINFO(-3, QConfigHelper.getChineseTips("TEL_NOINFO")),
    NO_ORDER(1002, QConfigHelper.getChineseTips("NO_ORDER")),
    ERROR_AMOUNT(1005, QConfigHelper.getChineseTips("ERROR_AMOUNT")),
    ERROR_PRODUCT(1006, QConfigHelper.getChineseTips("ERROR_PRODUCT")),
    NO_DATA(1008, QConfigHelper.getChineseTips("NO_DATA")),
    OPERATOR_TO_FAST(1012, QConfigHelper.getChineseTips("OPERATOR_TO_FAST")),
    ORDER_CREATEfrequently (1030, QConfigHelper.getChineseTips("ORDER_CREATEfrequently")),
    OAUTH_CHECK_FAIL(1031, QConfigHelper.getChineseTips("OAUTH_CHECK_FAIL"));
    private final int code;
    private final String message;

    ResultOpenStatus(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}

