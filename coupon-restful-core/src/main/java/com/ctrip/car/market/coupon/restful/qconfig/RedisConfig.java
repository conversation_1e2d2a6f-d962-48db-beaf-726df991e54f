package com.ctrip.car.market.coupon.restful.qconfig;

import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QMapConfig;

import java.util.Map;

@Component
public class RedisConfig {

    @QMapConfig("redisConfig.properties")
    public void onChange(Map<String, String> map) {
        clusterName = map.get("clusterName");
        cacheName = map.get("cacheName");
    }

    private static String clusterName;

    private static String cacheName;

    public static String getClusterName() {
        return clusterName;
    }

    public static String getCacheName() {
        return cacheName;
    }

}
