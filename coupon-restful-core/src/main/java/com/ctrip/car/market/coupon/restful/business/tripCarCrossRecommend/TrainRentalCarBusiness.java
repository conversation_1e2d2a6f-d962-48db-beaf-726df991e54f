package com.ctrip.car.market.coupon.restful.business.tripCarCrossRecommend;

import com.ctrip.car.market.common.utils.ABTestUtils;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.dto.TrainPointOfInterest;
import com.ctrip.car.market.coupon.restful.dto.TripUrlParams;
import com.ctrip.car.market.coupon.restful.enums.*;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai.UserLabelAiProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.BasicDataProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripRecommendConfig;
import com.ctrip.car.market.coupon.restful.utils.DateUtil;
import com.ctrip.car.market.coupon.restful.utils.NewVehicleURLUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.osd.basicdataservice.dto.PoiInfo;
import com.ctrip.car.osd.shopping.api.entity.*;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@TripCrossRecommendAnnotation(
        targetDepartment = TripRecommendSceneEnum.TRAIN,
        serviceType = ServiceType.CAR_RENTAL
)
@Component
public class TrainRentalCarBusiness implements TripCarCrossRecommend {

    private static final ILog logger = LogManager.getLogger(TrainRentalCarBusiness.class);

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    @Resource
    private UserLabelAiProxy userLabelAiProxy;

    @Resource
    private BasicDataProxy basicDataProxy;

    @Resource
    private TripRecommendConfig tripRecommendConfig;

    @Override
    public TripCarCrossRecommendResponseType recommend(TripCarCrossRecommendRequestType requestType) {
        TripCarCrossRecommendResponseType response = new TripCarCrossRecommendResponseType();
        response.setBaseResponse(ResponseUtil.success());
        if (!validRequest(requestType, response)) {
            return response;
        }
        // 无组件
        if (tripRecommendConfig.getBlankList().contains(this.getAbTestResult(requestType, response))) {
            return response;
        }
        List<com.ctrip.car.osd.basicdataservice.dto.PoiInfo> poiInfoList = this.getPoiInfo(requestType);
        if (CollectionUtils.isEmpty(poiInfoList)) {
            return response;
        }
        try {
            QueryRecomdProductsRequestType productsRequestType = buildQueryRecomdProductsRequestType(requestType);
            QueryRecomdProductsResponseType responseType = osdShoppingProxy.queryRecommendProductsTripTrain(productsRequestType);
            buildResponse(requestType, responseType, response, poiInfoList.get(0), productsRequestType.getQueryParams().get(0));
        } catch (Exception e) {
            logger.error("TrainRentalCarBusiness.recommend", e);
            response.setBaseResponse(ResponseUtil.fail(e.getMessage()));
        }
        return response;
    }

    // AB实验分流
    private String getAbTestResult(TripCarCrossRecommendRequestType requestType, TripCarCrossRecommendResponseType response) {
        String experimentCode = tripRecommendConfig.getAbTestExpCode(requestType.getBaseRequest().getSourceFrom());
        String ab = ABTestUtils.getAB(experimentCode, requestType.getBaseRequest().getCid());
        response.setAbExpCode(experimentCode);
        response.setAbVersion(ab);
        return ab;
    }

    /**
     * 获取火车站poi信息
     */
    private List<com.ctrip.car.osd.basicdataservice.dto.PoiInfo> getPoiInfo(TripCarCrossRecommendRequestType requestType) {
        Integer trainType = requestType.getTrainInfoList().get(0).getTrainType();
        String trainCode;
        if (Objects.equals(trainType, TrainTicketType.RoundTrip.getCode())) {
            trainCode = requestType.getTrainInfoList().stream().filter(x -> Objects.equals(x.getRoundTripType(), RoundTripTypeEnum.GO_TRIP.getCode())).findFirst().orElse(new TrainInfo()).getArrivalStation();
        } else {
            trainCode = requestType.getTrainInfoList().get(0).getArrivalStation();
        }
        return basicDataProxy.getTrainPoi(trainCode, requestType.getBaseRequest().getLocale()).getPoiInfos();
    }

    private boolean validRequest(TripCarCrossRecommendRequestType requestType, TripCarCrossRecommendResponseType response) {
        if (requestType.getBaseRequest() == null) {
            response.setBaseResponse(ResponseUtil.fail("baseRequest is null"));
            return false;
        }
        if (CollectionUtils.isEmpty(requestType.getTrainInfoList())) {
            response.setBaseResponse(ResponseUtil.fail("trainInfoList is null"));
            return false;
        }
        if (StringUtils.isEmpty(requestType.getBaseRequest().getRequestId())) {
            requestType.getBaseRequest().setRequestId(UUID.randomUUID().toString());
        }
        return true;
    }

    private QueryRecomdProductsRequestType buildQueryRecomdProductsRequestType(TripCarCrossRecommendRequestType requestType) {
        QueryRecomdProductsRequestType queryRecomdProductsRequestType = new QueryRecomdProductsRequestType();
        queryRecomdProductsRequestType.setBaseRequest(getBaseRequest(requestType));
        queryRecomdProductsRequestType.setSence(RecommendProductSenceEnum.Train.getSence());

        List<TrainInfo> trainInfoList = requestType.getTrainInfoList();
        TrainInfo firstTrainInfo = trainInfoList.get(0);
        Integer type = firstTrainInfo.getTrainType();

        LocationRequestInfo pickupLocation = new LocationRequestInfo();
        pickupLocation.setLocationType(LocationTypeEnum.TRAIN_STATION.getCode());
        LocationRequestInfo returnLocation = new LocationRequestInfo();
        returnLocation.setLocationType(LocationTypeEnum.TRAIN_STATION.getCode());

        // 往返票
        if (Objects.equals(type, TrainTicketType.RoundTrip.getCode())) {
            Map<Boolean, List<TrainInfo>> partitioned = trainInfoList.stream().collect(Collectors.partitioningBy(x -> Objects.equals(x.getRoundTripType(), RoundTripTypeEnum.GO_TRIP.getCode())));
            TrainInfo goInfo = partitioned.get(true).stream().findFirst().orElse(new TrainInfo());
            TrainInfo returnInfo = partitioned.get(false).stream().findFirst().orElse(new TrainInfo());
            pickupLocation.setDate(this.fixPickUpTime(goInfo.getArrivalTime(), goInfo.getTrainType()));
            returnLocation.setDate(this.fixReturnTime(returnInfo.getDepartureTime(), returnInfo.getTrainType()));
            pickupLocation.setCityId(goInfo.getArrivalCityId());
            pickupLocation.setLocationCode(goInfo.getArrivalStation());
            returnLocation.setCityId(goInfo.getArrivalCityId());
            returnLocation.setLocationCode(goInfo.getArrivalStation());
        } else {
            if (Objects.equals(type, TrainTicketType.SeasonOrFlexi.getCode())) {
                pickupLocation.setDate(this.fixPickUpTime(firstTrainInfo.getSeasonStartTime(), firstTrainInfo.getTrainType()));
            } else {
                pickupLocation.setDate(this.fixPickUpTime(firstTrainInfo.getArrivalTime(), firstTrainInfo.getTrainType()));
            }
            returnLocation.setDate(this.fixReturnTime(pickupLocation.getDate(), firstTrainInfo.getTrainType()));
            pickupLocation.setCityId(firstTrainInfo.getArrivalCityId());
            pickupLocation.setLocationCode(firstTrainInfo.getArrivalStation());
            returnLocation.setCityId(firstTrainInfo.getArrivalCityId());
            returnLocation.setLocationCode(firstTrainInfo.getArrivalStation());
        }
        QueryParamDTO queryParamDTO = new QueryParamDTO();
        queryParamDTO.setReturnLocation(returnLocation);
        queryParamDTO.setPickupLocation(pickupLocation);
        queryRecomdProductsRequestType.setQueryParams(Collections.singletonList(queryParamDTO));
        return queryRecomdProductsRequestType;
    }

    private BaseRequest getBaseRequest(TripCarCrossRecommendRequestType requestType) {
        BaseRequest baseRequest = new BaseRequest();
        baseRequest.setRequestId(requestType.getBaseRequest().getRequestId());
        baseRequest.setLocale(requestType.getBaseRequest().getLocale());
        baseRequest.setCurrencyCode(requestType.getBaseRequest().getCurrencyCode());
        baseRequest.setUid(requestType.getBaseRequest().getUid());
        baseRequest.setChannelId(requestType.getBaseRequest().getChannelId());
        baseRequest.setSourceFrom(requestType.getBaseRequest().getSourceFrom());
        return baseRequest;
    }

    private void buildResponse(TripCarCrossRecommendRequestType requestType, QueryRecomdProductsResponseType responseType, TripCarCrossRecommendResponseType tripCarCrossRecommendResponseType
            , PoiInfo poiInfo, QueryParamDTO queryParamDTO) {
        // 判断租车新老客
        boolean isNewGuest = userLabelAiProxy.judgeIsNewGuest(requestType.getBaseRequest().getUid(), Collections.singletonList(tripRecommendConfig.getQueryRentalCarNewGuestLabelId()));
        List<RecommendCarProduct> productList = new ArrayList<>();
        // 1、搜索框信息
        SearchBoxInfo searchBoxInfo = new SearchBoxInfo();
        searchBoxInfo.setPoiDetail(getPoiDetail(poiInfo));
        searchBoxInfo.setPickUpTime(DateUtil.toYYYY_MM_DD_HH_MM(queryParamDTO.getPickupLocation().getDate()));
        searchBoxInfo.setReturnTime(DateUtil.toYYYY_MM_DD_HH_MM(queryParamDTO.getReturnLocation().getDate()));
        searchBoxInfo.setRentalDay(DateUtil.getDifference(queryParamDTO.getPickupLocation().getDate(), queryParamDTO.getReturnLocation().getDate()));
        tripCarCrossRecommendResponseType.setSearchBoxInfo(searchBoxInfo);
        // 2、组件标题
        tripCarCrossRecommendResponseType.setComponentTitle(TripCrossRecommendShark.TRAIN_RENTAL_CAR_COMPONENT_TITLE.getValue(requestType.getBaseRequest().getLocale()));
        String locale = requestType.getBaseRequest().getLocale();
        // 3、商卡模块标题
        String title = TripCrossRecommendShark.TRAIN_RENTAL_CAR_CARD_TITLE.getValue(locale);
        tripCarCrossRecommendResponseType.setCardTitle(title.replace("${1}", poiInfo.getCityName()));
        AtomicBoolean hasPrivilege = new AtomicBoolean(false);
        if (CollectionUtils.isNotEmpty(responseType.getRecomdProductResList())) {
            RecomdProductRes recomdProductRes = responseType.getRecomdProductResList().get(0);
            if (CollectionUtils.isNotEmpty(recomdProductRes.getProducts())) {
                Map<String, RecomdProduct> maxSalesPerGroup = recomdProductRes.getProducts().stream().filter(x -> x.getVehicle().getGroupCode() != null)
                        .collect(Collectors.toMap(x -> x.getVehicle().getGroupCode(), Function.identity(), BinaryOperator.maxBy(Comparator.comparingInt(y -> y.getVehicle().getSnm()))));
                productList = recomdProductRes.getProducts().stream()
                        .map(x -> {
                            if (!x.getLabels().isEmpty() && x.getLabels().stream().anyMatch(y -> tripRecommendConfig.getTrainPriorityLabelId().equals(y.getCode()))) {
                                hasPrivilege.set(true);
                            }
                            return this.convert(x, responseType, requestType.getBaseRequest(), maxSalesPerGroup);
                        }).collect(Collectors.toList());
            }
        }
        // 4、利益点文案
        tripCarCrossRecommendResponseType.setInterestInfo(calInterestInfo(requestType.getTrainInfoList(), hasPrivilege, isNewGuest, locale));
        // 5、设置商卡列表
        tripCarCrossRecommendResponseType.setProductList(productList);
    }

    /*
        利益点信息
     */
    private InterestInfo calInterestInfo(List<TrainInfo> getTrainInfoList, AtomicBoolean hasPrivilege, boolean isNewGuest, String locale) {
        InterestInfo interestInfo = new InterestInfo();
        TrainPointOfInterest trainPointOfInterest = tripRecommendConfig.findTrainPointOfInterestByPriority(getInterestTextAndBackUrl(hasPrivilege.get(), isNewGuest, judgeIsEurope(getTrainInfoList)));
        interestInfo.setTextColor(trainPointOfInterest.getTextColor());
        interestInfo.setDiscountColor(trainPointOfInterest.getDiscountColor());
        interestInfo.setAppInterestBackUrl(trainPointOfInterest.getAppBackUrl());
        interestInfo.setPcInterestBackUrl(trainPointOfInterest.getPcBackUrl());
        interestInfo.setAppInterestDarkBackUrl(trainPointOfInterest.getAppBackDarkUrl());
        interestInfo.setPriority(trainPointOfInterest.getPriority());
        String text = Shark.getByLocale(trainPointOfInterest.getTextSharkKey(), locale);
        if (hasPrivilege.get() && isNewGuest) {
            interestInfo.setInterestText(text.replace("%1$s", tripRecommendConfig.getHasPriorityAndNew()));
        } else if (hasPrivilege.get()){
            interestInfo.setInterestText(text.replace("%1$s", tripRecommendConfig.getOnlyHasPriority()));
        } else {
            interestInfo.setInterestText(text);
        }
        return interestInfo;
    }

    private PoiDetail getPoiDetail(PoiInfo poiInfo) {
        PoiDetail poiDetail = new PoiDetail();
        poiDetail.setCityId(poiInfo.getCityId());
        poiDetail.setCityName(poiInfo.getCityName());
        poiDetail.setPoiType(poiInfo.getPoiType());
        poiDetail.setPoiCode(poiInfo.getCode());
        poiDetail.setPoiName(poiInfo.getName());
        poiDetail.setLongitude(poiInfo.getLongitude().doubleValue());
        poiDetail.setLatitude(poiInfo.getLatitude().doubleValue());
        return poiDetail;
    }

    private RecommendCarProduct convert(RecomdProduct recomdProduct, QueryRecomdProductsResponseType responseType, com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest, Map<String, RecomdProduct> maxSalesPerGroup) {
        RecommendCarProduct recommendCarProduct = new RecommendCarProduct();
        RecomdProductRes recomdProductRes = responseType.getRecomdProductResList().get(0);
        TripUrlParams tripUrlParams = this.buildParams(responseType.getSourceCountryId(), recomdProductRes, recomdProduct, baseRequest);

        recommendCarProduct.setAppUrl(this.buildTripUrl(true, recomdProductRes, tripUrlParams));
        recommendCarProduct.setH5Url(this.buildTripUrl(false, recomdProductRes, tripUrlParams));
        recommendCarProduct.setPcUrl(this.buildTripUrl(false, recomdProductRes, tripUrlParams));
        recommendCarProduct.setProductName(recomdProduct.getVehicle().getVehicleName());
        recommendCarProduct.setDailyCurrentPrice(recomdProduct.getPrice().getCurrentDailyPrice());
        recommendCarProduct.setDailyOriginPrice(recomdProduct.getPrice().getCurrentOriginalDailyPrice());
        recommendCarProduct.setProductImage(recomdProduct.getVehicle().getImageUrl());
        // 子车型组标签
        recommendCarProduct.setSubCarGroupLabel(recomdProduct.getVehicle().getGroupName());

        // 折扣标签
        Optional<OsdLabelInfo> discountLabel = recomdProduct.getLabels().stream().filter(x -> tripRecommendConfig.getDiscountLabels().contains(x.getCode())).findFirst();
        discountLabel.ifPresent(osdLabelInfo -> recommendCarProduct.setDiscountLabel(osdLabelInfo.getName()));

        // 免费取消标签
        Optional<OsdLabelInfo> freeCancelLabel = recomdProduct.getLabels().stream().filter(x -> x.getCode().equals(tripRecommendConfig.getFreeCancelLabelCode())).findFirst();
        freeCancelLabel.ifPresent(osdLabelInfo -> recommendCarProduct.setFreeCancelLabel(osdLabelInfo.getName()));

        // 车型推荐语
        this.getModelRecommendation(recomdProduct, baseRequest.getLocale(), maxSalesPerGroup, recommendCarProduct);

        // 币种
        recommendCarProduct.setCurrencyCode(baseRequest.getCurrencyCode());
        return recommendCarProduct;
    }

    /**
     * 构建url
     */
    private String buildTripUrl(boolean isApp, RecomdProductRes productRes, TripUrlParams params) {
        if (isApp) {
            return tripRecommendConfig.getTripAppListUrl() + NewVehicleURLUtils.getServerTripAppUrlList(params, productRes.getPickupLocation(), productRes.getReturnLocation());
        }
        return tripRecommendConfig.getTripPcListUrl() + NewVehicleURLUtils.getServerTripPcUrlListV2(params, productRes.getPickupLocation(), productRes.getReturnLocation());
    }

    private TripUrlParams buildParams(Integer sourceCountryId, RecomdProductRes productRes, RecomdProduct product, com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest) {
        TripUrlParams params = new TripUrlParams();
        params.setChannelId(baseRequest.getChannelId());
        params.setLocale(baseRequest.getLocale());
        params.setCurr(baseRequest.getCurrencyCode());
        if (product != null) {
            params.setPrice(product.getPrice().getCurrentDailyPrice());
            params.setVehicleKey(product.getVehicle().getVehicleKey());
            params.setPriceVersion(product.getPriceVersion());
        }
        params.setCityName(productRes.getPickupLocation().getCityName());
        params.setSourceCountryId(sourceCountryId);
        return params;
    }

    // 计算车型推荐语
    private void getModelRecommendation(RecomdProduct recomdProduct, String locale, Map<String, RecomdProduct> maxSalesPerGroup, RecommendCarProduct recommendCarProduct) {
        if (maxSalesPerGroup.containsKey(recomdProduct.getVehicle().getGroupCode()) && maxSalesPerGroup.get(recomdProduct.getVehicle().getGroupCode()).getVehicle().getVehicleKey().equals(recomdProduct.getVehicle().getVehicleKey())) {
            // 热销
            recommendCarProduct.setModelRecommendation(TripCrossRecommendShark.TRAIN_RENTAL_CAR_TOP_SALE.getValue(locale));
            recommendCarProduct.setRecommendPriority(0);
        } else if (recomdProduct.getLabels().stream().anyMatch(x -> tripRecommendConfig.getTrainPriorityLabelId().equals(x.getCode()))) {
            // 火车票用户专享
            recommendCarProduct.setModelRecommendation(TripCrossRecommendShark.TRAIN_RENTAL_CAR_USER_EXCLUSIVE.getValue(locale));
            recommendCarProduct.setRecommendPriority(1);
        } else if (recomdProduct.getVehicle().getPassengerNo() != null && recomdProduct.getVehicle().getPassengerNo() >= tripRecommendConfig.getTrainFamilyPeopleNum() && recomdProduct.getVehicle().getLuggageNo() != null && recomdProduct.getVehicle().getLuggageNo() >= tripRecommendConfig.getTrainFamilyLuggageNum()) {
            // 适合家庭出行
            recommendCarProduct.setModelRecommendation(TripCrossRecommendShark.TRAIN_RENTAL_CAR_FAMILY.getValue(locale));
            recommendCarProduct.setRecommendPriority(2);
        }
    }


    private Calendar fixPickUpTime(Calendar time, Integer trainType) {
        // 火车票到达时间+30分钟，非半点/整点则向上取整
        if (Objects.equals(trainType, TrainTicketType.OneWay.getCode()) || Objects.equals(trainType, TrainTicketType.RoundTrip.getCode())) {
           return DateUtil.roundToNextHalfHour(time, 30);
        }
        // 季票开始日期当天10:00
        return DateUtil.setTenHour(time);
    }

    private Calendar fixReturnTime(Calendar time, Integer trainType) {
        // 取整后的取车时间+7天
        if (Objects.equals(trainType, TrainTicketType.OneWay.getCode()) || Objects.equals(trainType, TrainTicketType.SeasonOrFlexi.getCode())) {
            return DateUtil.addSevenDay(time);
        }
        // 往返票还车时间-1h，非半点向下取整
        return DateUtil.roundToBeforeHalfHour(time, -1);
    }

    /**
     * 获得利益文案和背景url
     */
    private int getInterestTextAndBackUrl(boolean hasTrainPrivilege, boolean isRentalCarNewGuest, boolean arrivalCityIsEurope) {
        if (hasTrainPrivilege) {
            return tripRecommendConfig.getTrainPrivilege();
        } else if (isRentalCarNewGuest) {
            return tripRecommendConfig.getNewGuest();
        } else if (arrivalCityIsEurope) {
            return tripRecommendConfig.getEuropeCity();
        } else {
            return tripRecommendConfig.getDefaultCode();
        }
    }

    private boolean judgeIsEurope(List<TrainInfo> getTrainInfoList) {
        TrainInfo firstInfo = getTrainInfoList.get(0);
        boolean equals = firstInfo.getTrainType().equals(TrainTicketType.RoundTrip.getCode());
        if (equals) {
            TrainInfo trainInfo = getTrainInfoList.stream().filter(x -> Objects.equals(x.getRoundTripType(), RoundTripTypeEnum.GO_TRIP.getCode())).findFirst().orElse(new TrainInfo());
            return trainInfo.getArrivalCityIsEurope() != null && trainInfo.getArrivalCityIsEurope();
        } else {
            return firstInfo.getArrivalCityIsEurope() != null && firstInfo.getArrivalCityIsEurope();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        TripCarCrossRecommendFactory.registerStrategy(this);
    }
}
