package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.framework.triplog.client.tag.TagMarker;
import com.ctrip.framework.triplog.client.tag.TagMarkerBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class LogUtil {

    private static final Logger log = LoggerFactory.getLogger(LogUtil.class);

    public static void info(String title, String context, Map<String, String> tag) {
        TagMarker marker = TagMarkerBuilder.newBuilder().add("title", title).add(tag).build();
        log.info(marker, context);
    }

    public static void error(String title, String context, Throwable t) {
        TagMarker marker = TagMarkerBuilder.newBuilder().add("title", title).build();
        log.error(marker, context, t);
    }
}
