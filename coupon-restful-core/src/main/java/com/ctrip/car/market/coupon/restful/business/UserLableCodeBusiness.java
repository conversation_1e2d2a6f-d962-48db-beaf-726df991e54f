package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.common.utils.ABTestUtils;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.dto.MemberPrivilegeConfigDTO;
import com.ctrip.car.market.coupon.restful.dto.UserMktConditionDTO;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.ai.UserLabelAiProxy;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseCodeUtils;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import soa.ctrip.com.tour.ai.user.label.LabelInfo;

import java.util.*;
import java.util.stream.Collectors;


@Service
public class UserLableCodeBusiness {


    @Autowired
    UserLabelAiProxy userLabelAiProxy;

    @Autowired
    CouponServiceQConfig serviceQConfig;
    private final ILog logger = LogManager.getLogger(UserLableCodeBusiness.class);

    public queryUserLabelCodeResponseType queryUserLabelCode(queryUserLabelCodeRequestType request) {
        queryUserLabelCodeResponseType responseType = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, queryUserLabelCodeResponseType.class);
        String uid = BaseUtils.getUidByCommon(request.getHead());
        responseType.setLabelResult(false);
        if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
            uid = BaseUtils.getQunarUidByCommon(request.getRestRequestHeader());
        }
        if (StringUtils.isBlank(uid)) {
            return ResponseCodeUtils.res(ResultOpenStatus.NO_LOGIN, queryUserLabelCodeResponseType.class);
        }
        if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
            uid = "Q_" + uid;
        }
        if (CollectionUtils.isEmpty(request.getLabelCodes())) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserLabelCodeResponseType.class);

        }
        List<LabelInfo> result = userLabelAiProxy.queryUserLabelCode(uid, request.getLabelCodes());
        if (CollectionUtils.isNotEmpty(result)) {
            responseType.setUserLabelInfos(result.stream().map(x -> {
                        UserLabelInfo userLabelInfo = new UserLabelInfo();
                        userLabelInfo.setLabelCode(x.getLabelId());
                        userLabelInfo.setLabelValue(x.getLabelValue());
                        return userLabelInfo;
                    }).collect(Collectors.toList())
            );
            responseType.setLabelResult(result.stream().allMatch(x -> "true".equalsIgnoreCase(x.getLabelValue())));
        }
        return responseType;

    }

    public queryUserMktConditionResponseType queryUserMktCondition(queryUserMktConditionRequestType request) throws Exception {
        queryUserMktConditionResponseType responseType = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, queryUserMktConditionResponseType.class);
        if (CollectionUtils.isEmpty(request.getLabelCodes()) || StringUtils.isEmpty(request.getScenecode()) || StringUtils.isEmpty(request.getSourcefrom())) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserMktConditionResponseType.class);

        }
        if (isMiniAPP(request.getSourcefrom()) && request.getMiniType() == null) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserMktConditionResponseType.class);

        }
        if (StringUtils.isEmpty(request.getCid()) && StringUtils.isEmpty(request.getHead().getCid())) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, queryUserMktConditionResponseType.class);
        }

        boolean isNewUser = false;
        String uid = BaseUtils.getUidByCommon(request.getHead());
        if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
            uid = BaseUtils.getQunarUidByCommon(request.getRestRequestHeader());
        }
        if (!StringUtils.isBlank(uid)) {

            if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
                uid = "Q_" + uid;
            }
            List<LabelInfo> result = userLabelAiProxy.queryUserLabelCode(uid, request.getLabelCodes());
            if (CollectionUtils.isNotEmpty(result)) {
                responseType.setUserLabelInfos(result.stream().map(x -> {
                            UserLabelInfo userLabelInfo = new UserLabelInfo();
                            userLabelInfo.setLabelCode(x.getLabelId());
                            userLabelInfo.setLabelValue(x.getLabelValue());
                            return userLabelInfo;
                        }).collect(Collectors.toList())
                );
                isNewUser = result.stream().allMatch(x -> "true".equalsIgnoreCase(x.getLabelValue()));
            }

        }
        Map<String, String> tags = new HashMap<>();
        tags.put("uid", uid);
        tags.put("cid", request.getCid());
        tags.put("scenecode", request.getScenecode());
        tags.put("sourceFrom", request.getSourcefrom());
        tags.put("isNewUser", isNewUser + "");
        responseType.setLabelResult(isNewUser);

        // 裂变实验
        String shareABTestNumber = serviceQConfig.getValueByKeyFromQconfig("shareABTestNumber");
        boolean shareAbTestResult = "B".equalsIgnoreCase(ABTestUtils.getAB(shareABTestNumber, Optional.ofNullable(request.getHead().getCid()).orElse(request.getCid())));
        logger.warn("queryUserMktCondition", "shareAbTestResult:" + shareAbTestResult + ";shareABTestNumber:" + shareABTestNumber, tags);
        // B桶企微20%
        // 企微实验
        String wechatBTestNumber = serviceQConfig.getValueByKeyFromQconfig("wechatBTestNumber");
        boolean wechatBTestResult = "B".equalsIgnoreCase(ABTestUtils.getAB(wechatBTestNumber, Optional.ofNullable(request.getHead().getCid()).orElse(request.getCid())));
        logger.warn("queryUserMktCondition", "wechatBTestResult:" + wechatBTestResult + ";wechatBTestNumber:" + wechatBTestNumber, tags);

        //TODO  如何根据结果获取qconfig配置

        // banner
        if (isMidBanner(request.getScenecode())) {
            if (isCAPP(request.getSourcefrom())) {

                if (shareAbTestResult) {
                    if (wechatBTestResult) {
                        //  B+B  + (新客 或者是未登录)
// 企微+裂变
                        if (isNewUser || StringUtils.isEmpty(uid)) {
                            //新客/未登录，企微实验未命中b组  B+B  + (新客 或者是未登录) 企微+裂变
                            responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "B+B+N", 0));
                            Metrics.build().withTag("queryUserMktCondition", "middleBanner").withTag("isNewUser",isNewUser+"").withTag("shareAbTestResult",shareAbTestResult+"").withTag("wechatBTestResult",wechatBTestResult+"")  .withTag("result", "B+B+N").recordOne(MetricsEnum.MKT_USER.getTitle());
                            logger.warn("queryUserMktCondition", "B+B+N===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                        } else {
                            //企微
                            Metrics.build().withTag("queryUserMktCondition", "middleBanner").withTag("isNewUser",isNewUser+"").withTag("shareAbTestResult",shareAbTestResult+"").withTag("wechatBTestResult",wechatBTestResult+"")  .withTag("result", "B+B+O").recordOne(MetricsEnum.MKT_USER.getTitle());

                            responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "B+B+O", 0));
                            logger.warn("queryUserMktCondition", "B+B+O===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);
                        }

                    } else {

                        if (isNewUser || StringUtils.isEmpty(uid)) {
//裂变长条 新客/未登录，企微实验未命中b组
                            Metrics.build().withTag("queryUserMktCondition", "middleBanner").withTag("isNewUser",isNewUser+"").withTag("shareAbTestResult",shareAbTestResult+"").withTag("wechatBTestResult",wechatBTestResult+"")  .withTag("result", "B+ACD+N").recordOne(MetricsEnum.MKT_USER.getTitle());

                            responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "B+ACD+N", 0));
                            logger.warn("queryUserMktCondition", "B+AC+N ===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                        } else {
                            //无忧租 老客，企微实验未命中b组
                            Metrics.build().withTag("queryUserMktCondition", "middleBanner").withTag("isNewUser",isNewUser+"").withTag("shareAbTestResult",shareAbTestResult+"").withTag("wechatBTestResult",wechatBTestResult+"")  .withTag("result", "B+ACD+O").recordOne(MetricsEnum.MKT_USER.getTitle());

                            responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "B+ACD+O", 0));
                            logger.warn("queryUserMktCondition", "B+ACD+O ===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                        }
                    }
                } else {
//c+d组-老版本
                    if (wechatBTestResult) {
                        //20%*50% (ACD+)企微 企微实验命中b组
                        Metrics.build().withTag("queryUserMktCondition", "middleBanner").withTag("isNewUser",isNewUser+"").withTag("shareAbTestResult",shareAbTestResult+"").withTag("wechatBTestResult",wechatBTestResult+"")  .withTag("result", "ACD+B").recordOne(MetricsEnum.MKT_USER.getTitle());

                        responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "ACD+B", 0));
                        logger.warn("queryUserMktCondition", "ACD+B===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                    } else {
                        Metrics.build().withTag("queryUserMktCondition", "middleBanner").withTag("isNewUser",isNewUser+"").withTag("shareAbTestResult",shareAbTestResult+"").withTag("wechatBTestResult",wechatBTestResult+"")  .withTag("result", "ACD+ACD").recordOne(MetricsEnum.MKT_USER.getTitle());
                        //80%*50%(1ACD*2ACD) 无忧租 企微实验未命中b组
                        responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "ACD+ACD", 0));
                        logger.warn("queryUserMktCondition", "ACD+ACD===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                    }
                }
                if (CollectionUtils.isEmpty(responseType.getInfos()) || responseType.getInfos().stream().anyMatch(x -> StringUtils.isEmpty(x.getJumpUrl()) || StringUtils.isEmpty(x.getImageUrl()))) {
                    responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "default", 0));
                    logger.warn("queryUserMktCondition", "default===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);
                    Metrics.build().withTag("queryUserMktCondition", "middleBanner").withTag("isNewUser",isNewUser+"").withTag("shareAbTestResult",shareAbTestResult+"").withTag("wechatBTestResult",wechatBTestResult+"")  .withTag("result", "default").recordOne(MetricsEnum.MKT_USER.getTitle());

                }
            } else {
                logger.warn("queryUserMktCondition", "no app", tags);

            }
            if (isMiniAPP(request.getSourcefrom())) {
                /// 小程序banner 全部可见
                responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "ALL", request.getMiniType()));
                logger.warn("queryUserMktCondition", "ALL===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);
                if (CollectionUtils.isEmpty(responseType.getInfos()) || responseType.getInfos().stream().anyMatch(x -> org.apache.commons.lang3.StringUtils.isEmpty(x.getJumpUrl()) || StringUtils.isEmpty(x.getImageUrl()))) {
                    responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "default", request.getMiniType()));
                    logger.warn("queryUserMktCondition", "default===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);


                } else {
                    logger.warn("queryUserMktCondition", "default===>XXX" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                }
            } else {
                logger.warn("queryUserMktCondition", "no mini", tags);

            }

        }
        // 弹框

        if (isJumpBanner(request.getScenecode())) {

// 返回 APP 首页裂变弹窗 配置
            if (isCAPP(request.getSourcefrom())) {
                if (shareAbTestResult && isNewUser) {

                    // 返回 APP 首页裂变弹窗 配置
                    responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "B", 0));
                    logger.warn("queryUserMktCondition", "B===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                } else {
                    logger.warn("queryUserMktCondition", "B===>XXX" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                }
            } else {
                logger.warn("queryUserMktCondition", "no app", tags);

            }
            if (isMiniAPP(request.getSourcefrom())) {
                /// 小程序弹框 仅新客可见，未登录/老客用户不弹弹窗
                if (isNewUser) {

                    // 返回  小程序 首页裂变弹窗 配置
                    responseType.setInfos(buildResult(request.getSourcefrom(), request.getScenecode(), "ALL", request.getMiniType()));
                    logger.warn("queryUserMktCondition", "ALL===>" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                } else {
                    logger.warn("queryUserMktCondition", "ALL===>XXX" + "shareAbTestResult:" + shareAbTestResult + "; wechatBTestResult:" + wechatBTestResult + ";" + request.getSourcefrom() + ";" + request.getScenecode() + ";uid:" + uid, tags);

                }
            } else {
                logger.warn("queryUserMktCondition", "no mini", tags);

            }
        }

        return responseType;

    }

    private List<JumpImageInfo> buildResult(String sourceFrom, String sceneCode, String abResult, Integer miniType) {
        List<UserMktConditionDTO> mktConditionDTOS = getUserMktConditionDTO(sourceFrom, sceneCode, abResult, miniType);
        return (mktConditionDTOS.stream().map(x -> {
            JumpImageInfo jumpImageInfo = new JumpImageInfo();
            jumpImageInfo.setImageUrl(x.getImageUrl());
            jumpImageInfo.setJumpUrl(x.getJumpUrl());
            jumpImageInfo.setDisplayFrequency(Long.parseLong(x.getDisplay()));
            return jumpImageInfo;
        }).collect(Collectors.toList()));
    }

    private List<UserMktConditionDTO> getUserMktConditionDTO(String sourceFrom, String sceneCode, String abResult, Integer miniType) {
        return serviceQConfig.getUserMktConditions2().stream().filter(x -> x.getSceneCode().equalsIgnoreCase(sceneCode)).filter(x -> x.getSourceFrom().equals(sourceFrom)).filter(
                        x -> x.getAbResult().equalsIgnoreCase(abResult)
                ).filter(UserMktConditionDTO::getUseABTest)
                .filter(x -> x.getMiniType().equals(Optional.ofNullable(miniType).orElse(0)))
                .collect(Collectors.toList());
    }

    /**
     * APP判断
     *
     * @param sourceFrom
     * @return
     */
    private boolean isCAPP(String sourceFrom) {
        return serviceQConfig.findAPPSourceFrom(sourceFrom);

    }

    /***
     * 微信小程序判断
     * @param sourceFrom
     * @return
     */
    private boolean isMiniAPP(String sourceFrom) {
        return serviceQConfig.findWeChatSourceFrom(sourceFrom);

    }

    /***
     * 中部banner 判断
     * @param sceneCode
     * @return
     */
    private boolean isMidBanner(String sceneCode) {
        return (serviceQConfig.getValueByKeyFromQconfig("sourceFrom.middleBanner").equalsIgnoreCase(sceneCode));

    }

    /***
     * 弹框判断
     * @param sceneCode
     * @return
     */
    private boolean isJumpBanner(String sceneCode) {
        return (serviceQConfig.getValueByKeyFromQconfig("sourceFrom.jumpBanner").equalsIgnoreCase(sceneCode));

    }


}
