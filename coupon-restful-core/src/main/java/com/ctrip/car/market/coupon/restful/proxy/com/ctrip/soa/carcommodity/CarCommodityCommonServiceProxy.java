package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.carcommodity;

import com.ctrip.car.commodity.common.CarCommodityCommonApiClient;
import com.ctrip.car.commodity.common.service.types.CommodityCommonRequest;
import com.ctrip.car.commodity.common.service.types.MultiLanguageQueryCarStandardProductRequestType;
import com.ctrip.car.commodity.common.service.types.MultiLanguageQueryCarStandardProductResponseType;
import com.ctrip.car.commodity.common.service.types.SimpleStandardProductNameDTO;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class CarCommodityCommonServiceProxy {

    private final CarCommodityCommonApiClient client = CarCommodityCommonApiClient.getInstance();

    public SimpleStandardProductNameDTO multiLanguageQuery(Long id, String locale) {
        try {
            MultiLanguageQueryCarStandardProductRequestType requestType = new MultiLanguageQueryCarStandardProductRequestType();
            requestType.setCommonRequest(new CommodityCommonRequest());
            requestType.getCommonRequest().setLocale(locale);
            requestType.setIds(Lists.newArrayList(id));
            MultiLanguageQueryCarStandardProductResponseType responseType = client.multiLanguageQueryCarStandardProduct(requestType);
            return responseType != null && CollectionUtils.isNotEmpty(responseType.getData()) ? responseType.getData().get(0) : null;
        } catch (Exception e) {
            return null;
        }
    }
}
