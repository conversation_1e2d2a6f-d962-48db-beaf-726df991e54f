package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.BaseResponse;
import com.ctrip.car.market.coupon.restful.contract.QueryTripSeoProductsRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryTripSeoProductsResponseType;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class ProductBusiness {

    @Autowired
    private CarCrossRecommendedServiceProxy carCrossRecommendedServiceProxy;

    public QueryTripSeoProductsResponseType queryTripSeoProducts(QueryTripSeoProductsRequestType request) {
        QueryTripSeoProductsResponseType response = new QueryTripSeoProductsResponseType();
        if (Objects.isNull(request.getCityId())) {
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setCode("-1");
            baseResponse.setMessage("cityId is null");
            response.setBaseResponse(baseResponse);
            return response;
        }
        if (Objects.isNull(request.getBaseRequest())) {
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setCode("-1");
            baseResponse.setMessage("baseRequest is null");
            response.setBaseResponse(baseResponse);
            return response;
        }
        if (Objects.isNull(request.getBaseRequest().getSourceCountryId())) {
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setCode("-1");
            baseResponse.setMessage("SourceCountryId is null");
            response.setBaseResponse(baseResponse);
            return response;
        }
        return carCrossRecommendedServiceProxy.queryTripSeoProducts(request);
    }
}
