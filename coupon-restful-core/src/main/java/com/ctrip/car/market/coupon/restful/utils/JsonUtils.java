package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.car.market.common.util.JsonUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;

/**
 * Created by h_zhanga on 2019/2/14.
 */
public class JsonUtils {

    /**
     * JSON
     */
    private static final ObjectMapper objectMapper = new ObjectMapper();

    static {
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        //反序列化的时候允许多其他属性，不抛出异常
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    public static String toJson(Object o) throws JsonProcessingException {
        return JsonUtil.meetCtripOldProjectEnum.toString(o);
    }

    public static <T> T toObject(String json, Class<T> clazz) throws IOException {
        return JsonUtil.meetCtripOldProjectEnum.readValue(json, clazz);
    }

    // only used for PKActivity
    public static String convertObject2Json(Object obj) throws Exception {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    // only used for PKActivity
    public static <T> T convertJson2Object(String json, Class<T> clazz) throws Exception {
        try {
            checkNotNull(json, "json can NOT be null");
            T ret = objectMapper.readValue(json, clazz);
            return ret;
        } catch (Exception e) {
            throw new Exception(e);
        }
    }

    // only used for PKActivity
    public static <T> T checkNotNull(T reference, String errorMessage) throws Exception {
        if (reference == null)
            throw new Exception(errorMessage);
        return reference;
    }
}
