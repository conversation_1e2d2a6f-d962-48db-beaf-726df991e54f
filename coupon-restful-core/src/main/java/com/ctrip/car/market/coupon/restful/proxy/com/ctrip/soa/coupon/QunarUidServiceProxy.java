package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon;

import com.ctrip.car.market.coupon.restful.dto.GetQuanarUserInfoReq;
import com.ctrip.car.market.coupon.restful.dto.GetQuanarUserInfoRes;
import com.ctrip.car.market.coupon.restful.dto.QunarUserInfo;
import com.ctrip.car.market.coupon.restful.qconfig.QunarCouponConfig;
import com.ctrip.car.market.coupon.restful.redis.RedisProvider;
import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;

import javax.annotation.Resource;
import java.util.Objects;

@Component
public class QunarUidServiceProxy {

    private static final ILog log = LogManager.getLogger(QunarUidServiceProxy.class);

    private final QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    private final static String keyFormat = "car.market.coupon.qunar.uid.%s";

    @Resource
    private QunarCouponConfig qunarCouponConfig;

    public QunarUserInfo queryQunarUid(String sCookie) {
        try {
            if (StringUtils.isEmpty(sCookie)) {
                return null;
            }
            String key = String.format(keyFormat, sCookie);
            String cacheValue = RedisProvider.get(key);
            if (StringUtils.isNotEmpty(cacheValue)) {
                return JsonUtils.convertJson2Object(cacheValue, QunarUserInfo.class);
            }
            GetQuanarUserInfoReq req = new GetQuanarUserInfoReq();
            req.setScookie(sCookie);
            String res = doPostProxyWithProxy(qunarCouponConfig.getServiceUrl(), req);
            GetQuanarUserInfoRes response = JsonUtils.convertJson2Object(res, GetQuanarUserInfoRes.class);
            if (response == null || response.getSimpleUserInfo() == null || Objects.isNull(response.getSimpleUserInfo().getUid()) || response.getSimpleUserInfo().isLogout()) {
                return null;
            }
            RedisProvider.set(key, JsonUtils.convertObject2Json(response.getSimpleUserInfo()), qunarCouponConfig.getsCookieExpire());
            return response.getSimpleUserInfo();
        } catch (Exception e) {
            log.error("queryQunarUid", e);
            return null;
        }
    }

    public String doPostProxyWithProxy(String url, GetQuanarUserInfoReq req) {
        String res = "";
        try {
            QHttpOption option = new QHttpOption();
            option.addHeader("Content-Type", "application/json; charset=utf-8");
            option.setPostBodyData(JsonUtils.convertObject2Json(req));
            ListenableFuture<Response> responseFuture = qunarAsyncClient.post(url, option);
            Response response = responseFuture.get();
            if (response != null && response.getStatusCode() == 200) {
                res = response.getResponseBody("UTF-8");
            }
        } catch (Exception ex) {
            log.error("getQunarUser", ex + "\n" + url + "\n" + "cookie" + "\n" + req.getScookie());
        }
        log.info("getQunarUser", url + "\n" + req.getScookie() + "\n" + res);
        return res;
    }
}
