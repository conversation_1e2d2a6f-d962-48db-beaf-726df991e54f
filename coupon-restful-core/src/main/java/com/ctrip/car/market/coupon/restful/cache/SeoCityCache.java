package com.ctrip.car.market.coupon.restful.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@CreateCacheBean
public class SeoCityCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoCityCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_CITY_KEY)
    public Cache<Integer, List<SeoHotCityinfoDO>> cityCache;


    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoCity_CountryCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_CITY_COUNTRY_KEY)
    public Cache<Integer, List<SeoHotCityinfoDO>> countryCityCache;

    public SeoHotCityinfoDO queryByCity(Integer cityId) {
        List<SeoHotCityinfoDO> values = cityCache.get(cityId);
        return CollectionUtils.isNotEmpty(values) ? values.get(0) : null;
    }

    public List<SeoHotCityinfoDO> queryByCountry(Integer countryId) {
        return countryCityCache.get(countryId);
    }
}
