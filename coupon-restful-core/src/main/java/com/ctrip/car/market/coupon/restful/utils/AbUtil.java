package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.di.data.abtest.client.ABTestClient;
import com.ctrip.di.data.abtestservice.Alternative;
import com.google.common.collect.Maps;

import java.util.Map;

public class AbUtil {

    static {
        ABTestClient.load("250514_DSJT_qunar");
    }

    public static String getAlternative(String code, String uid) {
        try {
            Alternative result = ABTestClient.getAlternative(code, uid, null, null);
            String version = result == null ? null : result.getVersion();
            Map<String, String> tag = Maps.newHashMap();
            tag.put("uid", uid);
            LogUtil.info("getAlternative", "code:" + code + " uid:" + uid + " version:" + version, tag);
            return version;
        } catch (Exception e) {
            LogUtil.error("getAlternative", "code:" + code + " uid:" + uid, e);
            return null;
        }
    }
}
