package com.ctrip.car.market.coupon.restful.redis;

import com.ctrip.car.market.coupon.restful.qconfig.RedisConfig;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.framework.foundation.Foundation;
import credis.java.client.CacheProvider;
import credis.java.client.util.CacheFactory;
import org.apache.commons.lang3.StringUtils;

public class RedisProvider {
    private static final ILog log = LogManager.getLogger(RedisProvider.class);
    public static final String CAR_MARKET_REDIS_CLUSTER_NAME = getClusterName();
    private static final CacheProvider provider = CacheFactory.getProvider(CAR_MARKET_REDIS_CLUSTER_NAME);

    private static String getClusterName() {
        return StringUtils.equalsIgnoreCase(Foundation.server().getClusterName(), RedisConfig.getClusterName()) ? RedisConfig.getCacheName() : "car_market_cache";
    }

    public static String get(String key) {
        return provider.get(key);
    }

    public static void put(String key, String value) {
        provider.set(key, value);
    }

    public static void set(String key, String value, long second) {
        provider.set(key, value);
        provider.expire(key, second);
    }

    public static boolean putIfAbsent(String key, String value, int second) {
        return provider.set(key, value, "NX", "EX", second);
    }

    public static int lock_default_time = 60 * 1;

    public static void getLock(String key) {
        boolean result = provider.set(key, "true", "NX", "EX", lock_default_time);
        while (!result) {
            try {
                Thread.sleep(100l);
            } catch (InterruptedException e) {
                log.error(e);
            }
            result = provider.set(key, "true", "NX", "EX", lock_default_time);
        }
    }

    public static boolean releaseLock(String key) {
        return provider.del(key);
    }

    public static void setString(String key, String value, int second) {
        provider.setex(key, second, value);
    }

    public static Integer getInt(String key) {
        String value = provider.get(key);
        if (value != null && value.matches("\\d+")) {
            return Integer.parseInt(value);
        }
        return null;
    }

    public static Long getLong(String key) {
        String value = provider.get(key);
        if (value != null && value.matches("\\d+")) {
            return Long.parseLong(value);
        }
        return null;
    }

    public static Double getDouble(String key) {
        String value = provider.get(key);
        if (value != null) {
            Double val = null;
            try {
                val = Double.parseDouble(value);
            } catch (Exception e) {
                log.error(e);
            }
            return val;
        }
        return null;
    }
}
