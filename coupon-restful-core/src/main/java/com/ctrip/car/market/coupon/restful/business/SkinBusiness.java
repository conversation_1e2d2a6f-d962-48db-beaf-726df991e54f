package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.client.contract.SkinInfo;
import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.car.market.coupon.restful.contract.QuerySkinsRequestType;
import com.ctrip.car.market.coupon.restful.contract.QuerySkinsResponseType;
import com.ctrip.car.market.coupon.restful.contract.SkinConfig;
import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.SDMarketingServiceProxy;
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;
import com.ctrip.car.market.coupon.restful.utils.ResponseCodeUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Objects;

@Service
public class SkinBusiness {

    @Autowired
    private CouponServiceQConfig couponServiceQConfig;

    private static final ILog logger = LogManager.getLogger(SkinBusiness.class);

    @Autowired
    private SDMarketingServiceProxy sdMarketingServiceProxy;

    public QuerySkinsResponseType querySkins(QuerySkinsRequestType request) {
        long l = System.currentTimeMillis();
        QuerySkinsResponseType result = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, QuerySkinsResponseType.class);
        if (Objects.isNull(request.getBaseRequest()) || StringUtils.isEmpty(request.getBaseRequest().getSourceFrom())) {
            result.setResultCode(1);
            result.setResultMessage("sourceFrom not find");
            return result;
        }
        SkinInfo skinInfo = sdMarketingServiceProxy.getSkins(request.getBaseRequest().getSourceFrom());

        SkinConfig skinConfig = new SkinConfig();
        if (Objects.nonNull(skinInfo)) {
            logger.info("sdMarketingServiceProxy.getSkins", JsonUtil.toString(skinInfo));
            String jsonSkin = JsonUtil.toString(skinInfo.getSkinContent());
            skinConfig = JsonUtil.readNoError.readValue(jsonSkin, SkinConfig.class);

            if (Objects.nonNull(skinConfig)) {
                String skinSourceFroms = couponServiceQConfig.getValueByKeyFromQconfig("skinSourceFroms");
                boolean valid = Arrays.stream(skinSourceFroms.split(","))
                        .filter(org.apache.commons.lang.StringUtils::isNotBlank)
                        .anyMatch(x -> x.equals(request.getBaseRequest().getSourceFrom()));
                if (valid && (Objects.isNull(request.getTallVersion()) || request.getTallVersion().booleanValue() == false)) {
                    if (Objects.nonNull(skinConfig.getHomePage())){
                        skinConfig.getHomePage().setBottomBarButtons(new ArrayList<>());
                        skinConfig.getHomePage().setBottomBarBackgrounds(new ArrayList<>());
                    }
                    if (Objects.nonNull(skinConfig.getMergeHomePage())){
                        skinConfig.getMergeHomePage().setBottomBarButtons(new ArrayList<>());
                        skinConfig.getMergeHomePage().setBottomBarBackgroundUrl(null);
                    }
                }
            }
        } else {
            logger.warn("sdMarketingServiceProxy.getSkins", "skinInfo is null");
        }
        result.setSkinConfig(skinConfig);
        logger.warn("sdMarketingServiceProxy.cost", (System.currentTimeMillis() - l) + "");
        return result;
    }


}
