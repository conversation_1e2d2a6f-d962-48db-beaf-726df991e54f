
package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.basebiz.accounts.mobile.request.filter.util.AccountsMobileRequestUtils;
import com.ctrip.car.api8961.service.soa.gen.Api8961Client;
import com.ctrip.car.api8961.service.soa.gen.OfferOrderOAuthCheckRequestType;
import com.ctrip.car.api8961.service.soa.gen.OfferOrderOAuthCheckResponseType;
import com.ctrip.car.market.client.contract.CollectCouponCodeRequestType;
import com.ctrip.car.market.client.contract.CollectCouponCodeResponseType;
import com.ctrip.car.market.coupon.restful.contract.QueryCouponRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryCouponResponseType;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.dto.CouponConfigDTO;
import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.account.AccountInfoProxy;
import com.ctrip.car.market.coupon.restful.utils.*;
import com.ctrip.car.market.coupon.restful.utils.encrypt.EncryptHelper;
import com.ctrip.car.market.mergecouponservice.interfaces.common.RequestHeader;
import com.ctrip.car.market.mergecouponservice.interfaces.service.CarMergeCouponServiceClient;
import com.ctrip.car.market.mergecouponservice.interfaces.service.dto.*;
import com.ctrip.car.market.mergecouponservice.interfaces.service.message.*;
import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctriposs.baiji.rpc.common.util.ServiceUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


/**
 * Created by h_zhanga on 2018/12/20.
 * 这块处理所有优惠券相关内容
 */
@Service
public class CouponBusiness {


    @Autowired
    public AccountInfoProxy accountInfoProxy;

    @Autowired
    public RedirectUrlBusiness redirectUrlBusiness;


    @Autowired
    private CouponServiceQConfig couponServiceQConfig;

    public Api8961Client api8961Client = Api8961Client.getInstance();

    private final ILog log = LogManager.getLogger(this.getClass());
    public CarMergeCouponServiceClient carMergeCouponServiceClient
            = CarMergeCouponServiceClient.getInstance();
    SDMarketingServiceClient client = SDMarketingServiceClient.getInstance();




    /**
     * 查询优惠券
     *
     * @param request
     * @return
     */
    public QueryCouponResponseType queryCoupon(QueryCouponRequestType request) {
        return ResponseCodeUtils.res(ResultOpenStatus.TEL_NOINFO, QueryCouponResponseType.class);

    }

// 2024年12月5日17:49:00  不需要手机号发券能力， 只保留最基础能力即可，业务已经没有了
    public ReceiveCouponsByGroupIDResponseType receiveCouponsByGroupID(ReceiveCouponsByGroupIDRequestType request) {
        ReceiveCouponsByGroupIDResponseType result = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, ReceiveCouponsByGroupIDResponseType.class);
        String uid = BaseUtils.getUidByCommon(request.getHead());

        if (Optional.ofNullable(request.getUnionType()).orElse(0) <= 0) {
            if (StringUtils.isBlank(uid)) {
                uid = accountInfoProxy.getAccountInfoByTicket(request.getHead(), ServiceUtils.getExtensionData(request, AccountsMobileRequestUtils.MobileAuthTokenExtensionKey), request.getTicket());
            }
            if (StringUtils.isBlank(uid) && StringUtils.isBlank(request.getTel())) {
                return ResponseCodeUtils.res(ResultOpenStatus.NO_LOGIN, ReceiveCouponsByGroupIDResponseType.class);
            }
        } else {
            uid = BaseUtils.getQunarUidByCommon(request.getReqhead());
            request.setScene(38);
        }
        if (StringUtils.isBlank(uid)) {
            return ResponseCodeUtils.res(ResultOpenStatus.NO_LOGIN, ReceiveCouponsByGroupIDResponseType.class);
        }
        SendCouponsByGroupIdRequestType requestType = new SendCouponsByGroupIdRequestType();
        try {
            requestType = convertTOSendCouponsByGroupIdRequestType(request, uid);
            SendCouponsByGroupIdResponseType responseType = carMergeCouponServiceClient.sendCouponsByGroupId(requestType);
            Map<String, String> logTagMap = new HashMap<>();
            logTagMap.put("Gid", request.getGid());
            logTagMap.put("Tel", request.getTel());
            log.info("CarMergeCouponServiceClient.receiveCouponsByGroupID",
                    "request: " + JsonUtils.toJson(requestType) + "\r\n\r\n" + "response: " + JsonUtils.toJson(responseType),
                    logTagMap);
            if (responseType != null) {
                if (!CollectionUtils.isEmpty(responseType.getCouponItems())) {
                    result.setClist(responseType.getCouponItems().stream().map(x -> {
                        CouponInfos infos = new CouponInfos();
                        infos.setCcode(x.getCouponCode());
                        infos.setCid(x.getCouponID());
                        infos.setPid(x.getPromotionId());
                        infos.setRname(x.getRedPageName());
                        return infos;
                    }).collect(Collectors.toList()));
                }
                if (responseType.getResponseResult() != null) {
                    result.setResultCode(new Integer(responseType.getResponseResult().getReturnCode()));
                    result.setResultMessage(responseType.getResponseResult().getReturnMessage());
                }
                result.setAppUrl(result.getAppUrl());
                result.setUrl(responseType.getUrl());
            }

        } catch (Exception ex) {
            log.error("receiveCouponsByGroupID", ex);
            ResponseCodeUtils.res(ResultOpenStatus.SYSTEM_ECEPTION, ReceiveCouponsByGroupIDResponseType.class);
        }
        return result;
    }


    /**
     * new
     *
     * @param request
     * @return
     */
    public ReceiveImposingCouponResponseType receiveImposingCoupon(ReceiveImposingCouponRequestType request) {
        ReceiveImposingCouponResponseType result = new ReceiveImposingCouponResponseType();
        result.setIssuccess("1");
        result.setRencode("-1");
        result.setRenmessage("limit condition is null");
        return result;
        // 2024-12-5 13:51:02 业务已经下线了，此处注释即可
    }




    public COMInitRCByGroupIDResponseType cOMInitRCByGroupID(COMInitRCByGroupIDRequestType request) {
        COMInitRCByGroupIDResponseType result = new COMInitRCByGroupIDResponseType();

        result.setResultcode(3);
        result.setResultdesc("over");
        return result;
    }

    private SendCouponsByGroupIdRequestType convertTOSendCouponsByGroupIdRequestType(ReceiveCouponsByGroupIDRequestType request, String uid) {
        SendCouponsByGroupIdRequestType requestType = new SendCouponsByGroupIdRequestType();
        UserInfo userInfo = new UserInfo();
        requestType.setAuid(uid);
        requestType.setCityID(request.getCityID());
        requestType.setClientChannel(StringUtils.isBlank(request.getSn()) ? 0 : 1);
        requestType.setDisSource(request.getDistributid());
        if (Optional.ofNullable(request.getUnionType()).orElse(0) <= 0) {
            requestType.setGroupID(request.getGid());
        } else {
            requestType.setGroupID(request.getQgid());
        }
        RequestHeader header = new RequestHeader();
        header.setHost("ctrip");
        header.setLocale(request.getLocale());
        header.setBusinessType(35);
        header.setUid(uid);

        requestType.setScene(request.getScene());
        requestType.setUnionType(0);
        userInfo.setUseStation("0");
        if (Optional.ofNullable(request.getUnionType()).orElse(0) > 0) {
            requestType.setUnionType(1);
            userInfo.setUseStation("100");
            header.setHost("qunar");
        }
        requestType.setRequestHeader(header);
        userInfo.setClientChannel(userInfo.getClientChannel());
        userInfo.setClientId(userInfo.getClientId());
        userInfo.setClientIP(request.getCip());
        userInfo.setIdCardNo(request.getIcard());
        userInfo.setIntlCode(request.getIntlCode());
        userInfo.setSn(request.getSn());
        userInfo.setTel(request.getTel());
        userInfo.setUserID(uid);
        userInfo.setUserName(request.getUname());

        requestType.setUserInfo(userInfo);
        return requestType;
    }



    /**
     * 新增 encryptUid eid channel，
     *
     * @param request
     * @return
     * @throws Exception
     */
    public CollectCouponCodeByUidResponseType collectCouponCodeByUid(CollectCouponCodeByUidRequestType request) throws Exception {
        CollectCouponCodeByUidResponseType result = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, CollectCouponCodeByUidResponseType.class);
        String uid = "";
        if (Optional.ofNullable(request.getUnionType()).orElse(0) == 0) {
            uid = BaseUtils.getUidByCommon(request.getHead());
            if (StringUtils.isBlank(uid)) {
                uid = accountInfoProxy.getAccountInfoByTicket(request.getHead(), ServiceUtils.getExtensionData(request, AccountsMobileRequestUtils.MobileAuthTokenExtensionKey), request.getTicket());
            }
        } else {
            uid = BaseUtils.getQunarUidByCommon(request.getReqhead());
        }

        if (StringUtils.isBlank(uid)) {
            return ResponseCodeUtils.res(ResultOpenStatus.NO_LOGIN, CollectCouponCodeByUidResponseType.class);
        }
        if (StringUtils.isBlank(request.getCouponCode())) {
            return ResponseCodeUtils.res(ResultOpenStatus.ERROR_PARAMS, CollectCouponCodeByUidResponseType.class);
        }
        // 带加密uid标记的才走解密，其他都是正常业务逻辑
        if (!StringUtils.isBlank(request.getEncryptUid())) {
            OfferOrderOAuthCheckRequestType oAuthCheckRequestType = new OfferOrderOAuthCheckRequestType();
            oAuthCheckRequestType.setEid(request.getEid());
            oAuthCheckRequestType.setEncryptUid(request.getEncryptUid());
            oAuthCheckRequestType.setChannelId(request.getChannelId());
            oAuthCheckRequestType.setOperateType(request.getFromType());
            OfferOrderOAuthCheckResponseType restOAuthCheckResponseType = api8961Client.offerOrderOAuthCheck(oAuthCheckRequestType);
            if (Objects.isNull(restOAuthCheckResponseType)) {
                return ResponseCodeUtils.res(ResultOpenStatus.OAUTH_CHECK_FAIL, CollectCouponCodeByUidResponseType.class);
            }

            if (restOAuthCheckResponseType.isOauthAccess() == null || !restOAuthCheckResponseType.isOauthAccess()) {
                return ResponseCodeUtils.res(ResultOpenStatus.OAUTH_CHECK_FAIL, CollectCouponCodeByUidResponseType.class);
            }
            String decryptUid = EncryptHelper.decrypt(request.getEncryptUid());
            if (StringUtils.isBlank(decryptUid)) {
                result.setResultCode(1);
                result.setResultMessage("uid decrypt error");
                return result;
            }
            uid = decryptUid;
        }

        CollectCouponCodeRequestType requestType = new CollectCouponCodeRequestType();
        requestType.setClientId(Optional.ofNullable(request.getClientId()).orElse(request.getHead().getCid()));
        requestType.setCouponCode(request.getCouponCode());
        com.ctrip.car.market.client.contract.CouponRequestHead head = new com.ctrip.car.market.client.contract.CouponRequestHead();
        head.setUnionType(Optional.ofNullable(request.getUnionType()).orElse(0));
        head.setAuid(uid);
        requestType.setHead(head);
        requestType.setUseStation(Optional.ofNullable(requestType.getUseStation()).orElse("70"));
        if (head.getUnionType().equals(1)) {
            requestType.setUseStation("100");
        }
        try {
            CollectCouponCodeResponseType responseType = client.collectCouponCode(requestType);
            if (responseType != null) {
                result.setResultCode(responseType.getCode());
                result.setResultMessage(responseType.getMessage());
            }
        } catch (Exception ex) {
            log.warn("collectCouponCodeByUid", ex);
        }
        return result;
    }

    public QueryCouponByConfigResponseType queryCouponByConfig(QueryCouponByConfigRequestType request) {
        //验证用户信息
        QueryCouponByConfigResponseType result = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, QueryCouponByConfigResponseType.class);
        String section = couponServiceQConfig.getValueByKeyFromQconfig("inflow.section");
        result.setSection(1);
        if (org.apache.commons.lang.StringUtils.isNotEmpty(section)) {
            result.setSection(Integer.valueOf(section));
        }
        CouponConfigDTO couponConfigDTO = checkCouponStrategy(0);
        result.setDocuments(couponConfigDTO.getDocuments());
        result.setIsShow(couponConfigDTO.getIsShow());
        result.setButtonContent(couponConfigDTO.getButtonContent());
        result.setSendStatus(couponConfigDTO.getSendStatus());
        result.setDocument(couponConfigDTO.getDocument());
        result.setResultCode(200);
        return result;
    }


    /**
     * 0：3阶段暂停服务 1：未领取  2：领取未使用  3：领取已使用
     *
     * @param rule
     * @return
     */
    private CouponConfigDTO checkCouponStrategy(int rule) {
        CouponConfigDTO couponConfigDTO = null;
        List<CouponConfigDTO> couponConfigs = couponServiceQConfig.getCouponConfigs();
        if (CollectionUtils.isNotEmpty(couponConfigs)) {
            couponConfigDTO = couponConfigs.stream().filter(x -> x.getReceiveCouponStatus() == rule).findFirst().orElse(null);
        }
        return couponConfigDTO;
    }


}
