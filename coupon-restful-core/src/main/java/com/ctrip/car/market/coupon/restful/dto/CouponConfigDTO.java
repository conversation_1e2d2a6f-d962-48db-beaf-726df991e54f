package com.ctrip.car.market.coupon.restful.dto;

import java.util.List;

public class CouponConfigDTO {
    //是否领取
    private Integer receiveCouponStatus;
    //是否弹窗 0：不弹， 1：弹窗
    private Integer isShow;
    //按钮文案
    private String buttonContent;
    //是否发券 0:不发  1：发券
    private Integer sendStatus;
    //弹窗文案
    private List<String> documents;
    //弹窗文案
    private String document;

    public Integer getReceiveCouponStatus() {
        return receiveCouponStatus;
    }

    public void setReceiveCouponStatus(Integer receiveCouponStatus) {
        this.receiveCouponStatus = receiveCouponStatus;
    }

    public Integer getIsShow() {
        return isShow;
    }

    public void setIsShow(Integer isShow) {
        this.isShow = isShow;
    }

    public String getButtonContent() {
        return buttonContent;
    }

    public void setButtonContent(String buttonContent) {
        this.buttonContent = buttonContent;
    }

    public Integer getSendStatus() {
        return sendStatus;
    }

    public void setSendStatus(Integer sendStatus) {
        this.sendStatus = sendStatus;
    }

    public List<String> getDocuments() {
        return documents;
    }

    public void setDocuments(List<String> documents) {
        this.documents = documents;
    }

    public String getDocument() {
        return document;
    }

    public void setDocument(String document) {
        this.document = document;
    }
}
