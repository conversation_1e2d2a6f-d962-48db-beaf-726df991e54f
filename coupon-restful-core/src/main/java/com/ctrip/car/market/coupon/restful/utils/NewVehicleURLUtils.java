package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.car.market.coupon.restful.dto.TripUrlParams;
import com.ctrip.car.osd.shopping.api.entity.LocationRequestInfo;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.Objects;

public class NewVehicleURLUtils {

    private final static ILog log = LogManager.getLogger(NewVehicleURLUtils.class);

    public static String getServerTripPcUrlList(TripUrlParams params, LocationRequestInfo pLocation, LocationRequestInfo rLocation) {
        StringBuilder builder = new StringBuilder();
        try {
            if (Objects.nonNull(params)) {
                if (StringUtils.isNotBlank(params.getVehicleKey())) {
                    builder.append("&").append("vehicleKey=").append(params.getVehicleKey());
                }
                if (params.getPrice() != null && params.getPrice().compareTo(BigDecimal.ZERO) >= 0) {
                    builder.append("&").append("currentDailyPrice=").append(params.getPrice());
                }
                if (Objects.nonNull(params.getChannelId())) {
                    builder.append("&").append("channelid=").append(params.getChannelId());
                }
                if (StringUtils.isNotBlank(params.getLocale())) {
                    builder.append("&").append("locale=").append(params.getLocale());
                }
                if (StringUtils.isNotBlank(params.getCurr())) {
                    builder.append("&").append("curr=").append(params.getCurr());
                }
                if (Objects.nonNull(params.getSourceCountryId())) {
                    builder.append("&").append("scountry=").append(params.getSourceCountryId());
                }
            }
            if (Objects.nonNull(pLocation) && Objects.nonNull(rLocation)) {
                if (Objects.nonNull(pLocation.getDate())) {
                    builder.append("&").append("ptime=").append(DateUtil.toYYYY_MM_DDHHMM(pLocation.getDate()));
                }
                if (Objects.nonNull(rLocation.getDate())) {
                    builder.append("&").append("rtime=").append(DateUtil.toYYYY_MM_DDHHMM(rLocation.getDate()));
                }
                if (StringUtils.isNotBlank(pLocation.getLocationCode())) {
                    builder.append("&").append("pcode=").append(pLocation.getLocationCode());
                }
                if (StringUtils.isNotBlank(pLocation.getLocationName())) {
                    builder.append("&").append("paddress=").append(pLocation.getLocationName());
                }
                if (pLocation.getLocationType() != null) {
                    builder.append("&").append("ptype=").append(pLocation.getLocationType());
                }
                if (pLocation.getCityId() != null) {
                    builder.append("&").append("pcity=").append(pLocation.getCityId());
                }
                if (StringUtils.isNotBlank(params.getCityName())) {
                    builder.append("&").append("pcityname=").append(params.getCityName());
                }
                if (pLocation.getPoi() != null && pLocation.getPoi().getLatitude() != null) {
                    builder.append("&").append("plat=").append(pLocation.getPoi().getLatitude());
                }
                if (pLocation.getPoi() != null && pLocation.getPoi().getLongitude() != null) {
                    builder.append("&").append("plon=").append(pLocation.getPoi().getLongitude());
                }

                if (StringUtils.isNotBlank(rLocation.getLocationCode())) {
                    builder.append("&").append("rcode=").append(rLocation.getLocationCode());
                }
                if (StringUtils.isNotBlank(rLocation.getLocationName())) {
                    builder.append("&").append("raddress=").append(rLocation.getLocationName());
                }
                if (rLocation.getLocationType() != null) {
                    builder.append("&").append("rtype=").append(rLocation.getLocationType());
                }
                if (rLocation.getCityId() != null) {
                    builder.append("&").append("rcity=").append(rLocation.getCityId());
                }
                if (StringUtils.isNotBlank(params.getCityName())) {
                    builder.append("&").append("rcityname=").append(params.getCityName());
                }
                if (rLocation.getPoi() != null && rLocation.getPoi().getLatitude() != null) {
                    builder.append("&").append("rlat=").append(rLocation.getPoi().getLatitude());
                }
                if (rLocation.getPoi() != null && rLocation.getPoi().getLongitude() != null) {
                    builder.append("&").append("rlon=").append(rLocation.getPoi().getLongitude());
                }
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("getServerTripPcUrlList", e);
        }
        return "";
    }

    public static String getServerTripAppUrlList(TripUrlParams params, LocationRequestInfo pLocation, LocationRequestInfo rLocation) {
        StringBuilder builder = new StringBuilder();
        try {
            if (Objects.nonNull(params)) {
                if (StringUtils.isNotBlank(params.getVehicleKey())) {
                    builder.append("&").append("vehicleKey=").append(params.getVehicleKey());
                }
                if (params.getPrice() != null && params.getPrice().compareTo(BigDecimal.ZERO) >= 0) {
                    builder.append("&").append("currentDailyPrice=").append(params.getPrice());
                }
                if (Objects.nonNull(params.getChannelId())) {
                    builder.append("&").append("channelid=").append(params.getChannelId());
                }
                if (StringUtils.isNotBlank(params.getLocale())) {
                    builder.append("&").append("locale=").append(params.getLocale());
                }
                if (StringUtils.isNotBlank(params.getCurr())) {
                    builder.append("&").append("curr=").append(params.getCurr());
                }
                if (Objects.nonNull(params.getSourceCountryId())) {
                    builder.append("&").append("scountry=").append(params.getSourceCountryId());
                }
                if (Objects.nonNull(params.getPriceVersion())) {
                    builder.append("&").append("priceVersion=").append(params.getPriceVersion());
                }
             }
            if (Objects.nonNull(pLocation) && Objects.nonNull(rLocation)) {
                if (Objects.nonNull(pLocation.getDate())) {
                    builder.append("&").append("ptime=").append(DateUtil.toYYYYMMDDHHmMss(pLocation.getDate()));
                }
                if (Objects.nonNull(rLocation.getDate())) {
                    builder.append("&").append("rtime=").append(DateUtil.toYYYYMMDDHHmMss(rLocation.getDate()));
                }
                if (StringUtils.isNotBlank(pLocation.getLocationCode())) {
                    builder.append("&").append("pcode=").append(pLocation.getLocationCode());
                }
                if (pLocation.getLocationType() != null) {
                    builder.append("&").append("pltype=").append(pLocation.getLocationType());
                }
                if (pLocation.getCityId() != null) {
                    builder.append("&").append("pcity=").append(pLocation.getCityId());
                }
                if (StringUtils.isNotBlank(rLocation.getLocationCode())) {
                    builder.append("&").append("rcode=").append(rLocation.getLocationCode());
                }
                if (rLocation.getLocationType() != null) {
                    builder.append("&").append("rltype=").append(rLocation.getLocationType());
                }
                if (rLocation.getCityId() != null) {
                    builder.append("&").append("rcity=").append(rLocation.getCityId());
                }
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("getServerTripPcUrlList", e);
        }
        return "";
    }

    public static String getServerTripPcUrlListV2(TripUrlParams params, LocationRequestInfo pLocation, LocationRequestInfo rLocation) {
        StringBuilder builder = new StringBuilder();
        try {
            if (Objects.nonNull(params)) {
                if (StringUtils.isNotBlank(params.getVehicleKey())) {
                    builder.append("&").append("vehicleKey=").append(params.getVehicleKey());
                }
                if (params.getPrice() != null && params.getPrice().compareTo(BigDecimal.ZERO) >= 0) {
                    builder.append("&").append("currentDailyPrice=").append(params.getPrice());
                }
                if (Objects.nonNull(params.getChannelId())) {
                    builder.append("&").append("channelid=").append(params.getChannelId());
                }
                if (StringUtils.isNotBlank(params.getLocale())) {
                    builder.append("&").append("locale=").append(params.getLocale());
                }
                if (StringUtils.isNotBlank(params.getCurr())) {
                    builder.append("&").append("curr=").append(params.getCurr());
                }
                if (Objects.nonNull(params.getSourceCountryId())) {
                    builder.append("&").append("scountry=").append(params.getSourceCountryId());
                }
                if (Objects.nonNull(params.getPriceVersion())) {
                    builder.append("&").append("priceVersion=").append(params.getPriceVersion());
                }
            }
            if (Objects.nonNull(pLocation) && Objects.nonNull(rLocation)) {
                if (Objects.nonNull(pLocation.getDate())) {
                    builder.append("&").append("ptime=").append(DateUtil.toYYYY_MM_DDHHMM(pLocation.getDate()));
                }
                if (Objects.nonNull(rLocation.getDate())) {
                    builder.append("&").append("rtime=").append(DateUtil.toYYYY_MM_DDHHMM(rLocation.getDate()));
                }
                if (StringUtils.isNotBlank(pLocation.getLocationCode())) {
                    builder.append("&").append("pcode=").append(pLocation.getLocationCode());
                }
                if (StringUtils.isNotBlank(pLocation.getLocationName())) {
                    builder.append("&").append("paddress=").append(pLocation.getLocationName());
                }
                if (pLocation.getLocationType() != null) {
                    builder.append("&").append("pltype=").append(pLocation.getLocationType());
                }
                if (pLocation.getCityId() != null) {
                    builder.append("&").append("pcity=").append(pLocation.getCityId());
                }
                if (StringUtils.isNotBlank(params.getCityName())) {
                    builder.append("&").append("pcityname=").append(params.getCityName());
                }
                if (pLocation.getPoi() != null && pLocation.getPoi().getLatitude() != null) {
                    builder.append("&").append("plat=").append(pLocation.getPoi().getLatitude());
                }
                if (pLocation.getPoi() != null && pLocation.getPoi().getLongitude() != null) {
                    builder.append("&").append("plon=").append(pLocation.getPoi().getLongitude());
                }

                if (StringUtils.isNotBlank(rLocation.getLocationCode())) {
                    builder.append("&").append("rcode=").append(rLocation.getLocationCode());
                }
                if (StringUtils.isNotBlank(rLocation.getLocationName())) {
                    builder.append("&").append("raddress=").append(rLocation.getLocationName());
                }
                if (rLocation.getLocationType() != null) {
                    builder.append("&").append("rltype=").append(rLocation.getLocationType());
                }
                if (rLocation.getCityId() != null) {
                    builder.append("&").append("rcity=").append(rLocation.getCityId());
                }
                if (StringUtils.isNotBlank(params.getCityName())) {
                    builder.append("&").append("rcityname=").append(params.getCityName());
                }
                if (rLocation.getPoi() != null && rLocation.getPoi().getLatitude() != null) {
                    builder.append("&").append("rlat=").append(rLocation.getPoi().getLatitude());
                }
                if (rLocation.getPoi() != null && rLocation.getPoi().getLongitude() != null) {
                    builder.append("&").append("rlon=").append(rLocation.getPoi().getLongitude());
                }
            }
            return builder.toString();
        } catch (Exception e) {
            log.error("getServerTripPcUrlList", e);
        }
        return "";
    }
}
