package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.account;

import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.framework.apollo.core.utils.StringUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.soa.platform.members.userauthorization.v1.GetAccountInfoByTicketRequestType;
import com.ctrip.soa.platform.members.userauthorization.v1.GetAccountInfoByTicketResponseType;
import com.ctrip.soa.platform.members.userauthorization.v1.UserAuthorizationClient;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import org.springframework.stereotype.Service;

@Service
public class AccountInfoProxy {

    private static final UserAuthorizationClient Client = UserAuthorizationClient.getInstance();
    private static final ILog log = LogManager.getLogger("AccountInfoProxy");
    static
    {
        Client.setConnectTimeout(5*1000);
    }

    public String getAccountInfoByTicket(MobileRequestHead head, String ticket, String sourceTicket) {
        try {
//            if (!StringUtils.isBlank(ticket)) {
//                sourceTicket = ticket;
//            }
           // log.warn("AccountInfoProxy", ticket);
            GetAccountInfoByTicketRequestType requestType = new GetAccountInfoByTicketRequestType();
            requestType.setHead(head);
            requestType.setTicket(ticket);
            GetAccountInfoByTicketResponseType responseType = Client.getAccountInfoByTicket(requestType);
            if (responseType == null) {
                return "";
            }
            log.warn("AccountInfoProxy", JsonUtil.toString(requestType)+";"+JsonUtil.toString(responseType));
            return responseType.getUid();
        } catch (Exception ex) {
            log.error("AccountInfoProxy", ex);
        }
        return "";
    }

}
