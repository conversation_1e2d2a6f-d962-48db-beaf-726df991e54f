package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.seo.*;
import com.ctrip.car.market.coupon.restful.contract.seo.PriceInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.VehicleInfo;
import com.ctrip.car.market.coupon.restful.dto.TripUrlParams;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.NewVehicleURLUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.osd.shopping.api.entity.*;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SeoVehicleBusiness {

    @Resource
    private SeoService service;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    @Resource
    private TripConfig tripConfig;

    private final Set<String> labelSet = Sets.newHashSet("discount", "reduction");

    private final ILog logger = LogManager.getLogger(SeoVehicleBusiness.class);

    public QuerySeoVehicleListResponseType queryVehicleList(QuerySeoVehicleListRequestType request) {
        QuerySeoVehicleListResponseType response = new QuerySeoVehicleListResponseType();
        response.setVehicleList(Lists.newArrayList());
        if (!checkRequest(request)) {
            Metrics.build().withTag("errorType", "paraError").recordOne(MetricsEnum.SEO_VEHICLE.getTitle());
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        //优先使用qconfig配置
        SeoHotDestinatioinfoDO hotDestinationInfo = service.queryCityDefaultPoi(request.getPoiCode(), request.getCityId());
        if (hotDestinationInfo == null) {
            //热门poi
            hotDestinationInfo = service.queryHotDestinationFirst(request.getCountryId(), request.getCityId(), request.getPoiType(), request.getPoiCode());
        }
        if (hotDestinationInfo == null) {
            Metrics.build().withTag("errorType", "destinationError").recordOne(MetricsEnum.SEO_VEHICLE.getTitle());
            response.setBaseResponse(ResponseUtil.fail("destination error"));
            return response;
        }
        QueryRecomdProductsResponseType soaRes = queryVehicle(request.getBaseRequest(), request.getHead(), hotDestinationInfo, false);
        response.setVehicleList(resConvert(soaRes, request));
        if (soaRes != null && CollectionUtils.isNotEmpty(soaRes.getRecomdProductResList())) {
            response.setUrl(buildUrl(soaRes.getSourceCountryId(), soaRes.getRecomdProductResList().get(0), null, request.getBaseRequest()));
        }
        CityVehicleInfo cityVehicleInfo = new CityVehicleInfo();
        cityVehicleInfo.setCityId(hotDestinationInfo.getCityId());
        cityVehicleInfo.setUrl(response.getUrl());
        cityVehicleInfo.setVehicleList(response.getVehicleList());
        response.setCityVehicleList(Lists.newArrayList(cityVehicleInfo));
        response.setVendorList(service.getTopVendor(soaRes));
        Metrics.build().withTag("country", Optional.ofNullable(hotDestinationInfo.getCountryId()).orElse(0).toString())
                .withTag("city", Optional.ofNullable(hotDestinationInfo.getCityId()).orElse(0).toString())
                .withTag("result", CollectionUtils.isNotEmpty(response.getVehicleList()) ? "1" : "0")
                .withTag("locale", Optional.ofNullable(request.getBaseRequest().getLocale()).orElse("0").toLowerCase())
                .recordOne(MetricsEnum.SEO_VEHICLE.getTitle());

        Metrics.build().withTag("country", Optional.ofNullable(hotDestinationInfo.getCountryId()).orElse(0).toString())
                .withTag("city", Optional.ofNullable(hotDestinationInfo.getCityId()).orElse(0).toString())
                .withTag("result", CollectionUtils.isNotEmpty(response.getVehicleList()) ? "1" : "0")
                .withTag("locale", Optional.ofNullable(request.getBaseRequest().getLocale()).orElse("0").toLowerCase())
                .recordSize(MetricsEnum.SEO_VEHICLE.getTitle(), CollectionUtils.isNotEmpty(response.getVehicleList()) ? response.getVehicleList().size() : 0);
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private boolean checkRequest(QuerySeoVehicleListRequestType request) {
        if (request.getBaseRequest() == null) {
            return false;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) <= 0
                && Optional.ofNullable(request.getCountryId()).orElse(0) <= 0
                && StringUtils.isEmpty(request.getPoiCode())) {
            return false;
        }
        if (StringUtils.isEmpty(request.getBaseRequest().getRequestId())) {
            request.getBaseRequest().setRequestId(UUID.randomUUID().toString());
        }
        return true;
    }

    public QueryRecomdProductsResponseType queryVehicle(com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest, MobileRequestHead head, SeoHotDestinatioinfoDO hotDestinationInfo, boolean cache) {
        QueryRecomdProductsRequestType soaReq = reqConvert(baseRequest, head, hotDestinationInfo);
        return osdShoppingProxy.queryRecomdProducts(soaReq, cache);
    }


    private List<VehicleInfo> resConvert(QueryRecomdProductsResponseType soaRes, QuerySeoVehicleListRequestType request) {
        if (soaRes == null || CollectionUtils.isEmpty(soaRes.getRecomdProductResList())
                || CollectionUtils.isEmpty(soaRes.getRecomdProductResList().get(0).getProducts())
                || soaRes.getRecomdProductResList().get(0).getPickupLocation() == null
                || soaRes.getRecomdProductResList().get(0).getReturnLocation() == null) {
            return Lists.newArrayList();
        }
        LanguageLocaleEnum lEnum = LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(request.getBaseRequest().getLocale());
        CurrencyEnum cEnum = CurrencyEnum.getCurrrencyEnum(request.getBaseRequest().getCurrencyCode());
        return soaRes.getRecomdProductResList().get(0).getProducts().stream().map(l -> {
            if (l.getPrice() == null || l.getVehicle() == null) {
                return null;
            }
            VehicleInfo vehicleInfo = new VehicleInfo();
            vehicleInfo.setVehicleCode(l.getVehicle().getVehicleCode());
            vehicleInfo.setVehicleName(l.getVehicle().getVehicleName());
            vehicleInfo.setGroupCode(l.getVehicle().getGroupCode());
            vehicleInfo.setGroupName(l.getVehicle().getGroupName());
            vehicleInfo.setSimilarDesc(l.getVehicle().getSimilarDesc());
            vehicleInfo.setDoorNo(l.getVehicle().getDoorNo());
            vehicleInfo.setPassengerNo(l.getVehicle().getPassengerNo());
            vehicleInfo.setLuggageNo(l.getVehicle().getLuggageNo());
            vehicleInfo.setImageUrl(l.getVehicle().getImageUrl());
            vehicleInfo.setLabel(CollectionUtils.isNotEmpty(l.getLabels()) ? l.getLabels().stream().map(li -> {
                if (StringUtils.isEmpty(li.getCode()) || !labelSet.contains(li.getCode().toLowerCase())) {
                    return null;
                }
                LabelInfo labelInfo = new LabelInfo();
                labelInfo.setCode(li.getCode());
                labelInfo.setName(li.getName());
                labelInfo.setExtDesc(li.getExtDesc());
                return labelInfo;
            }).filter(Objects::nonNull).collect(Collectors.toList()) : Lists.newArrayList());
            vehicleInfo.setPrice(new PriceInfo());
            vehicleInfo.getPrice().setPrice(l.getPrice().getCurrentDailyPrice());
            vehicleInfo.getPrice().setOriginalPrice(l.getPrice().getCurrentOriginalDailyPrice());
            vehicleInfo.getPrice().setCurrency(l.getPrice().getCurrency());
            vehicleInfo.getPrice().setPrefix(l.getPrice().getPrefix());
            vehicleInfo.getPrice().setSuffix(l.getPrice().getSuffix());
            if (l.getPrice().getCurrentOriginalDailyPrice() != null) {
                vehicleInfo.getPrice().setOriginalPriceStr(service.currencyString(l.getPrice().getCurrentOriginalDailyPrice(), lEnum, cEnum));
            }
            vehicleInfo.getPrice().setPriceStr(service.currencyString(l.getPrice().getCurrentDailyPrice(), lEnum, cEnum));
            vehicleInfo.setUrl(buildUrl(soaRes.getSourceCountryId(), soaRes.getRecomdProductResList().get(0), l, request.getBaseRequest()));
            return vehicleInfo;
        }).filter(Objects::nonNull).limit(tripConfig.getMaxVehicleCount()).collect(Collectors.toList());
    }

    private QueryRecomdProductsRequestType reqConvert(com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest, MobileRequestHead head, SeoHotDestinatioinfoDO hotDestinationInfo) {
        QueryRecomdProductsRequestType soaReq = new QueryRecomdProductsRequestType();
        soaReq.setBaseRequest(new BaseRequest());
        soaReq.getBaseRequest().setRequestId(baseRequest.getRequestId());
        soaReq.getBaseRequest().setChannelId(baseRequest.getChannelId());
        soaReq.getBaseRequest().setLocale(baseRequest.getLocale());
        soaReq.getBaseRequest().setCurrencyCode(baseRequest.getCurrencyCode());
        soaReq.getBaseRequest().setUid(BaseUtils.getUidByCommon(head));
        soaReq.setSence(2);

        QueryParamDTO queryParamDTO = new QueryParamDTO();

        queryParamDTO.setPickupLocation(new LocationRequestInfo());
        queryParamDTO.getPickupLocation().setCityId(hotDestinationInfo.getCityId());
        queryParamDTO.getPickupLocation().setLocationCode(hotDestinationInfo.getPoiCode());
        queryParamDTO.getPickupLocation().setLocationName(hotDestinationInfo.getPoiName());
        queryParamDTO.getPickupLocation().setLocationType(hotDestinationInfo.getPoiType());
        queryParamDTO.getPickupLocation().setDate(getDate(7));

        soaReq.setQueryParams(Lists.newArrayList(queryParamDTO));
        return soaReq;
    }

    private Calendar getDate(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day);
        calendar.set(Calendar.HOUR_OF_DAY, 10);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    private String buildUrl(Integer sourceCountryId, RecomdProductRes productRes, RecomdProduct product, com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest) {
        TripUrlParams params = new TripUrlParams();
        //params.setChannelId(StringUtils.equalsIgnoreCase(baseRequest.getSourceFrom(), "OSD_T_ONLINE") ? 242796 : 242803);
        params.setChannelId(baseRequest.getChannelId());
        params.setLocale(baseRequest.getLocale());
        params.setCurr(baseRequest.getCurrencyCode());
        if (product != null) {
            params.setPrice(product.getPrice().getCurrentDailyPrice());
            params.setVehicleKey(product.getVehicle().getVehicleKey());
        }
        params.setCityName(productRes.getPickupLocation().getCityName());
        params.setSourceCountryId(sourceCountryId);
        return tripConfig.getPcUrl() + NewVehicleURLUtils.getServerTripPcUrlList(params, productRes.getPickupLocation(), productRes.getReturnLocation());
    }
}
