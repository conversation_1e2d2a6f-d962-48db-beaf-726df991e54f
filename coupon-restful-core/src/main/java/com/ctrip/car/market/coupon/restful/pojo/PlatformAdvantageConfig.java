package com.ctrip.car.market.coupon.restful.pojo;

import com.ctrip.car.market.coupon.restful.contract.seo.PlatformAdvantage;

import java.util.List;

public class PlatformAdvantageConfig {

    private String locale;
    private List<PlatformAdvantage> platformAdvantages;

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public List<PlatformAdvantage> getPlatformAdvantages() {
        return platformAdvantages;
    }

    public void setPlatformAdvantages(List<PlatformAdvantage> platformAdvantages) {
        this.platformAdvantages = platformAdvantages;
    }
}
