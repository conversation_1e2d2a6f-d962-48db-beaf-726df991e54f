package com.ctrip.car.market.coupon.restful.cache;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.alicp.jetcache.anno.CreateCacheBean;
import com.alicp.jetcache.anno.IncUpdateConsumerByRedis;
import com.ctrip.car.market.job.common.consts.BasicDataRedisKeyConst;
import com.ctrip.car.market.job.common.consts.CacheName;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCountryinfoDO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@CreateCacheBean
public class SeoCountryCache {

    @IncUpdateConsumerByRedis
    @CreateCache(name = CacheName.SeoCountryCacheName,
            area = "public",
            cacheType = CacheType.BOTH,
            redisHashKey = BasicDataRedisKeyConst.SEO_COUNTRY_KEY)
    public Cache<Integer, List<SeoHotCountryinfoDO>> countryCache;

    public SeoHotCountryinfoDO queryByCountry(Integer countryId) {
        List<SeoHotCountryinfoDO> values = countryCache.get(countryId);
        return CollectionUtils.isNotEmpty(values) ? values.get(0) : null;
    }
}
