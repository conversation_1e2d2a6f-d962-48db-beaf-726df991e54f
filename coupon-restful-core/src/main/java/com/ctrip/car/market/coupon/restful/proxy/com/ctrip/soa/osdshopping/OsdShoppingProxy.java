package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping;

import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.redis.RedisProvider;
import com.ctrip.car.market.coupon.restful.utils.DateUtil;
import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsRequestType;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdPriceDTO;
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct;
import com.ctrip.car.osd.shopping.api.service.CarOsdShoppingServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.google.common.collect.Maps;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Map;

@Service
public class OsdShoppingProxy {

    private final ILog logger = LogManager.getLogger(OsdShoppingProxy.class);

    private final CarOsdShoppingServiceClient carOsdShoppingServiceClient = CarOsdShoppingServiceClient.getInstance();

    //date.airport.locale.currency.uid.vendor
    private final String productCacheKeyFormat = "car.market.seo.product.cache.%s.%s.%s.%s.%s.%s";

    //date.airport.locale.currency.vendor
    private final String lowPriceCacheKeyFormat = "car.market.seo.airport.cache.%s.%s.%s.%s.%s";

    //date.airport.locale.currency.vendor
    private final String carCardCacheKeyFormat = "car.market.seo.car.card.cache.%s.%s.%s.%s.%s";

    //date.airport.locale.currency.vendor
    private final String noDataCacheKeyFormat = "car.market.seo.no.product.cache.%s.%s.%s.%s.%s";

    @Resource
    private TripConfig tripConfig;

    public QueryRecomdProductsResponseType queryRecomdProducts(QueryRecomdProductsRequestType request, boolean cache) {
        try {
            QueryRecomdProductsResponseType cacheResponse = getProductCache(request);
            if (cacheResponse != null && CollectionUtils.isNotEmpty(cacheResponse.getRecomdProductResList()) && CollectionUtils.isNotEmpty(cacheResponse.getRecomdProductResList().get(0).getProducts())) {
                return cacheResponse;
            }
            if (cache && tripConfig.getCacheReturn() && StringUtils.isEmpty(request.getBaseRequest().getUid())) {
                return cacheResponse;
            }
            if (tripConfig.getNoDataCache() && isNoData(request)) {
                return null;
            }
            Map<String, String> tags = Maps.newHashMap();
            tags.put("requestId", request.getBaseRequest().getRequestId());
            logger.info("queryRecomdProducts_req", JsonUtil.toString(request), tags);
            QueryRecomdProductsResponseType response = carOsdShoppingServiceClient.queryRecomdProducts(request);
            logger.info("queryRecomdProducts_res", JsonUtil.toString(response), tags);
            setProductCache(request, response);
            setLowPriceCache(request, response);
            setCarCardCache(request, response);
            seNoDataCache(request, response);
            return response;
        } catch (Exception e) {
            logger.error("queryRecomdProducts", e);
            return null;
        }
    }

    private String buildCacheKey(String airportCode, String locale, String currencyCode, String uid, String vendorCode) {
        String date = DateUtil.toYYYY_MM_DD(Calendar.getInstance());
        String UID = StringUtils.isEmpty(uid) ? "null" : uid;
        String VENDOR_CODE = StringUtils.isEmpty(vendorCode) ? "null" : vendorCode;
        return String.format(productCacheKeyFormat, date, airportCode, locale, currencyCode, UID, VENDOR_CODE).toLowerCase();
    }


    private String buildAirportCacheKey(String airportCode, String locale, String currencyCode, String vendorCode) {
        String date = DateUtil.toYYYY_MM_DD(Calendar.getInstance());
        String VENDOR_CODE = StringUtils.isEmpty(vendorCode) ? "null" : vendorCode;
        return String.format(lowPriceCacheKeyFormat, date, airportCode, locale, currencyCode, VENDOR_CODE).toLowerCase();
    }

    private String buildCarCardCacheKey(String airportCode, String locale, String currencyCode, String vendorCode) {
        String date = DateUtil.toYYYY_MM_DD(Calendar.getInstance());
        String VENDOR_CODE = StringUtils.isEmpty(vendorCode) ? "null" : vendorCode;
        return String.format(carCardCacheKeyFormat, date, airportCode, locale, currencyCode, VENDOR_CODE).toLowerCase();
    }

    private String buildNoDataCacheKey(String airportCode, String locale, String currencyCode, String vendorCode) {
        String date = DateUtil.toYYYY_MM_DD(Calendar.getInstance());
        String VENDOR_CODE = StringUtils.isEmpty(vendorCode) ? "null" : vendorCode;
        return String.format(noDataCacheKeyFormat, date, airportCode, locale, currencyCode, VENDOR_CODE).toLowerCase();
    }

    private boolean isNoData(QueryRecomdProductsRequestType request) {
        String cacheKey = buildNoDataCacheKey(request.getQueryParams().get(0).getPickupLocation().getLocationCode(), request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), request.getQueryParams().get(0).getVendorCode());
        return StringUtils.isNotEmpty(RedisProvider.get(cacheKey));
    }

    private QueryRecomdProductsResponseType getProductCache(QueryRecomdProductsRequestType request) {
        try {
            if (!tripConfig.getProductCache()) {
                return null;
            }
            if (!tripConfig.getUidCache() && StringUtils.isNotEmpty(request.getBaseRequest().getUid())) {
                return null;
            }
            String cacheKey = buildCacheKey(request.getQueryParams().get(0).getPickupLocation().getLocationCode(), request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), request.getBaseRequest().getUid(), request.getQueryParams().get(0).getVendorCode());
            String cacheValue = RedisProvider.get(cacheKey);
            if (StringUtils.isNotEmpty(cacheValue)) {
                return JsonUtils.convertJson2Object(cacheValue, QueryRecomdProductsResponseType.class);
            }
            return null;
        } catch (Exception e) {
            logger.warn("getProductCache", e);
            return null;
        }
    }

    private void setProductCache(QueryRecomdProductsRequestType request, QueryRecomdProductsResponseType response) {
        try {
            if (!tripConfig.getProductCache()) {
                return;
            }
            if (!tripConfig.getUidCache() && StringUtils.isNotEmpty(request.getBaseRequest().getUid())) {
                return;
            }
            if (response == null || CollectionUtils.isEmpty(response.getRecomdProductResList()) || CollectionUtils.isEmpty(response.getRecomdProductResList().get(0).getProducts())) {
                return;
            }
            String cacheKey = buildCacheKey(request.getQueryParams().get(0).getPickupLocation().getLocationCode(), request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), request.getBaseRequest().getUid(), request.getQueryParams().get(0).getVendorCode());
            String cacheValue = JsonUtils.convertObject2Json(response);
            RedisProvider.set(cacheKey, cacheValue, tripConfig.getProductCacheExpired() * 60);
        } catch (Exception e) {
            logger.warn("setProductCache", e);
        }
    }

    private void seNoDataCache(QueryRecomdProductsRequestType request, QueryRecomdProductsResponseType response) {
        if (response != null && CollectionUtils.isEmpty(response.getRecomdProductResList())) {
            String cacheKey = buildNoDataCacheKey(request.getQueryParams().get(0).getPickupLocation().getLocationCode(), request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), request.getQueryParams().get(0).getVendorCode());
            RedisProvider.set(cacheKey, "1", tripConfig.getNoDataCacheExpired() * 60);
        }
    }

    private void setLowPriceCache(QueryRecomdProductsRequestType request, QueryRecomdProductsResponseType response) {
        try {
            if (response == null || CollectionUtils.isEmpty(response.getRecomdProductResList()) || CollectionUtils.isEmpty(response.getRecomdProductResList().get(0).getProducts())) {
                return;
            }
            RecomdProduct recomdProduct = response.getRecomdProductResList().get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).min(Comparator.comparing(l -> l.getPrice().getCurrentDailyPrice())).orElse(null);
            if (recomdProduct != null) {
                String cacheKey = buildAirportCacheKey(request.getQueryParams().get(0).getPickupLocation().getLocationCode(), request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), request.getBaseRequest().getUid());
                String cacheValue = JsonUtils.convertObject2Json(recomdProduct.getPrice());
                RedisProvider.set(cacheKey, cacheValue, tripConfig.getAirportLowPriceCacheExpired() * 60);
            }
        } catch (Exception e) {
            logger.warn("setLowPriceCache", e);
        }
    }

    private void setCarCardCache(QueryRecomdProductsRequestType request, QueryRecomdProductsResponseType response) {
        try {
            if (response == null || CollectionUtils.isEmpty(response.getRecomdProductResList()) || CollectionUtils.isEmpty(response.getRecomdProductResList().get(0).getProducts())) {
                return;
            }
            if (response.getRecomdProductResList().get(0).getProducts().size() < 3) {
                return;
            }
            String cacheKey = buildCarCardCacheKey(request.getQueryParams().get(0).getPickupLocation().getLocationCode(), request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), request.getQueryParams().get(0).getVendorCode());
            RedisProvider.set(cacheKey, "1", tripConfig.getCarCardCacheExpired() * 60);
        } catch (Exception e) {
            logger.warn("setCarCardCache", e);
        }
    }

    public RecomdPriceDTO getAirportPrice(BaseRequest baseRequest, String airportCode) {
        try {
            String cacheKey = buildAirportCacheKey(airportCode, baseRequest.getLocale(), baseRequest.getCurrencyCode(), baseRequest.getUid());
            String cacheValue = RedisProvider.get(cacheKey);
            if (StringUtils.isNotEmpty(cacheValue)) {
                return JsonUtils.convertJson2Object(cacheValue, RecomdPriceDTO.class);
            }
            return null;
        } catch (Exception e) {
            logger.warn("getAirportPrice", e);
            return null;
        }
    }

    public boolean getCarCard(BaseRequest baseRequest, String airportCode, String vendorCode) {
        try {
            String cacheKey = buildCarCardCacheKey(airportCode, baseRequest.getLocale(), baseRequest.getCurrencyCode(), vendorCode);
            return StringUtils.equalsIgnoreCase(RedisProvider.get(cacheKey), "1");
        } catch (Exception e) {
            logger.warn("getCarCard", e);
            return false;
        }
    }

    public String getCacheTime() {
        //默认72小时，后续读shopping接口返回的
        return "72";
    }

    /**
     * trip订后火车票交叉组件
     */
    public QueryRecomdProductsResponseType queryRecommendProductsTripTrain(QueryRecomdProductsRequestType request) {
        Map<String, String> tags = Maps.newHashMap();
        tags.put("requestId", request.getBaseRequest().getRequestId());
        logger.info("queryRecomdProducts_req", JsonUtil.toString(request), tags);
        QueryRecomdProductsResponseType response = new QueryRecomdProductsResponseType();
        try {
            response = carOsdShoppingServiceClient.queryRecomdProducts(request);
        } catch (Exception e) {
            logger.error("queryRecomdProducts", e);
        }
        logger.info("queryRecomdProducts_res", JsonUtil.toString(response), tags);
        return response;
    }
}
