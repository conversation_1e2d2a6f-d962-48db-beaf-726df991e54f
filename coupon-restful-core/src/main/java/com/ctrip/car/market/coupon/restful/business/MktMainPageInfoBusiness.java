package com.ctrip.car.market.coupon.restful.business;


import com.ctrip.car.market.BaseRequest;
import com.ctrip.car.market.client.contract.QueryMktMainPageInfoRequestType;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.SDMarketingServiceProxy;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class MktMainPageInfoBusiness {

    @Autowired
    private SDMarketingServiceProxy sdMarketingServiceProxy;

    public com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoResponseType queryMktMainPageInfo(com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoRequestType queryMktMainPageInfoRequestType) {
        QueryMktMainPageInfoResponseType responseType = new QueryMktMainPageInfoResponseType();
        String uid = BaseUtils.getUidByCommon(queryMktMainPageInfoRequestType.getHead());
//        if (StringUtils.isEmpty(uid)) {
//            responseType.setBaseResponse(ResponseUtil.fail("no login"));
//            return responseType;
//        }
        if (!validRequest(queryMktMainPageInfoRequestType)) {
            responseType.setBaseResponse(ResponseUtil.fail("parameter invalid"));
            return responseType;
        }
        QueryMktMainPageInfoRequestType remoteRequest = new QueryMktMainPageInfoRequestType();
        remoteRequest.setBaseRequest(buildBaseRequest(uid, queryMktMainPageInfoRequestType));
        remoteRequest.setTaskId(queryMktMainPageInfoRequestType.getTaskId());
        remoteRequest.setProjectId(queryMktMainPageInfoRequestType.getProjectId());
        resultConvert(sdMarketingServiceProxy.queryMktMainPageInfo(remoteRequest), responseType);
        responseType.setBaseResponse(ResponseUtil.success());
        return responseType;
    }

    private Boolean validRequest(com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoRequestType remoteRequest) {
        return !Objects.isNull(remoteRequest.getHead())
                && !StringUtils.isBlank(remoteRequest.getHead().getCid())
                && !Objects.isNull(remoteRequest.getBaseRequest().getRequestId())
                && !Objects.isNull(remoteRequest.getBaseRequest().getChannelId())
                && !Objects.isNull(remoteRequest.getBaseRequest().getSourceFrom())
                && !Objects.isNull(remoteRequest.getProjectId())
                && !Objects.isNull(remoteRequest.getTaskId());
    }

    private com.ctrip.car.market.client.contract.BaseRequest buildBaseRequest(String uid, com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoRequestType queryMktMainPageInfoRequestType) {
        com.ctrip.car.market.client.contract.BaseRequest baseRequest = new com.ctrip.car.market.client.contract.BaseRequest();
        baseRequest.setRequestId(queryMktMainPageInfoRequestType.getBaseRequest().getRequestId());
        if (Objects.nonNull(queryMktMainPageInfoRequestType.getHead())) {
            baseRequest.setCid(queryMktMainPageInfoRequestType.getHead().getCid());
        }
        baseRequest.setSourceFrom(queryMktMainPageInfoRequestType.getBaseRequest().getSourceFrom());
        baseRequest.setUid(uid);
        baseRequest.setChannelId(queryMktMainPageInfoRequestType.getBaseRequest().getChannelId());
        return baseRequest;
    }

    private void resultConvert(
            com.ctrip.car.market.client.contract.QueryMktMainPageInfoResponseType remoteResponse,
            com.ctrip.car.market.coupon.restful.contract.QueryMktMainPageInfoResponseType responseType) {

        if (Objects.nonNull(remoteResponse)){

            responseType.setOrderId(remoteResponse.getOrderId());
            responseType.setOrderStatus(remoteResponse.getOrderStatus());
            responseType.setTaskReceiveTime(remoteResponse.getTaskReceiveTime());
            responseType.setCashbackIsAmount(remoteResponse.isCashbackIsAmount());
            responseType.setCashbackAmoutLabel(remoteResponse.getCashbackAmoutLabel());
            responseType.setCashbackLabel(remoteResponse.getCashbackLabel());
            responseType.setTaskStatusToast(remoteResponse.getTaskStatusToast());
            responseType.setInvitationProgress(remoteResponse.getInvitationProgress());

            ActivityStatusInfoDTO activityStatusInfoDTO = new ActivityStatusInfoDTO();
            if (Objects.nonNull(remoteResponse.getActivityStatusInfo())){
                activityStatusInfoDTO.setActivityStatus(remoteResponse.getActivityStatusInfo().getActivityStatus());
                activityStatusInfoDTO.setActivityButtonSubTip(remoteResponse.getActivityStatusInfo().getActivityButtonSubTip());
                activityStatusInfoDTO.setActivityEndTime(remoteResponse.getActivityStatusInfo().getActivityEndTime());
                activityStatusInfoDTO.setExternalJumpUrl(remoteResponse.getActivityStatusInfo().getExternalJumpUrl());
                activityStatusInfoDTO.setActivityAboveContent(remoteResponse.getActivityStatusInfo().getActivityAboveContent());
                activityStatusInfoDTO.setActivityAboveContentIsAmount(remoteResponse.getActivityStatusInfo().isActivityAboveContentIsAmount());
                if (Objects.nonNull(remoteResponse.getActivityStatusInfo().getCancelBindOrderInfo())) {
                    CancelBindOrderInfo cancelBindOrderInfo = new CancelBindOrderInfo();
                    cancelBindOrderInfo.setCancelBindOrderContent(remoteResponse.getActivityStatusInfo().getCancelBindOrderInfo().getCancelBindOrderContent());
                    cancelBindOrderInfo.setCancelBindOrderTitle(remoteResponse.getActivityStatusInfo().getCancelBindOrderInfo().getCancelBindOrderTitle());
                    activityStatusInfoDTO.setCancelBindOrderInfo(cancelBindOrderInfo);
                }
            }
            responseType.setActivityStatusInfo(activityStatusInfoDTO);
            LotteryProgressInfo lotteryProgressInfo = new LotteryProgressInfo();
            if (Objects.nonNull(remoteResponse.getLotteryProgressInfo())) {
                lotteryProgressInfo.setShowLotteryProgress(remoteResponse.getLotteryProgressInfo().isShowLotteryProgress());
                lotteryProgressInfo.setInvitedCount(remoteResponse.getLotteryProgressInfo().getInvitedCount());
                lotteryProgressInfo.setIsLotteryAgain(remoteResponse.getLotteryProgressInfo().isIsLotteryAgain());
            }
            responseType.setLotteryProgressInfo(lotteryProgressInfo);
            AnimationInfo animationInfo = new AnimationInfo();
            if (Objects.nonNull(remoteResponse.getAnimationInfo())) {
                animationInfo.setInvitedCount(remoteResponse.getAnimationInfo().getInvitedCount());
                animationInfo.setDisplayAchieveAnimation(remoteResponse.getAnimationInfo().isDisplayAchieveAnimation());
                animationInfo.setCurrentCashbackAmount(remoteResponse.getAnimationInfo().getCurrentCashbackAmount());
                animationInfo.setInvitedGap(remoteResponse.getAnimationInfo().getInvitedGap());
                animationInfo.setDisplayCashbackProgress(remoteResponse.getAnimationInfo().isDisplayCashbackProgress());
                animationInfo.setReachInvitationLimit(remoteResponse.getAnimationInfo().isReachInvitationLimit());
                animationInfo.setTotalCashbackAmount(remoteResponse.getAnimationInfo().getTotalCashbackAmount());
            }
            responseType.setAnimationInfo(animationInfo);
        }
    }
}
