package com.ctrip.car.market.coupon.restful.dto;

import java.util.List;

/**
 * @program: coupon-restful
 * @description: 会员特权
 * @author: yliu
 * @create: 2021-06-18 15:27
 */
public class MemberPrivilegeConfigDTO {
    private String appSkipLink;
    //未解锁跳转
    private String notDeblockingSkipLink;
    private String describe;
    //未解锁文案
    private String notDeblockingDescribe;
    //未开启状态的文案展示
    private String notOpenDescribe;
    private String name;
    private String code;
    //0-会员特权 1-渠道特权
    private int type;
    //前置渠道
    private String prepositivChannel;
    //排序
    private int index;
    //是否有效 0-无效 1-有效
    private boolean valid;
    //是否开启 0-不开启 1-开启
    private int openStatus;
    //业务类型，与CtripBusinessType中的枚举对应
    private Integer businessType;

    public String getAppSkipLink() {
        return appSkipLink;
    }

    public void setAppSkipLink(String appSkipLink) {
        this.appSkipLink = appSkipLink;
    }

    public String getDescribe() {
        return describe;
    }

    public void setDescribe(String describe) {
        this.describe = describe;
    }

    public String getNotDeblockingDescribe() {
        return notDeblockingDescribe;
    }

    public void setNotDeblockingDescribe(String notDeblockingDescribe) {
        this.notDeblockingDescribe = notDeblockingDescribe;
    }

    public String getNotOpenDescribe() {
        return notOpenDescribe;
    }

    public void setNotOpenDescribe(String notOpenDescribe) {
        this.notOpenDescribe = notOpenDescribe;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public String getPrepositivChannel() {
        return prepositivChannel;
    }

    public void setPrepositivChannel(String prepositivChannel) {
        this.prepositivChannel = prepositivChannel;
    }

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public boolean isValid() {
        return valid;
    }

    public void setValid(boolean valid) {
        this.valid = valid;
    }

    public int getOpenStatus() {
        return openStatus;
    }

    public void setOpenStatus(int openStatus) {
        this.openStatus = openStatus;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getNotDeblockingSkipLink() {
        return notDeblockingSkipLink;
    }

    public void setNotDeblockingSkipLink(String notDeblockingSkipLink) {
        this.notDeblockingSkipLink = notDeblockingSkipLink;
    }
}
