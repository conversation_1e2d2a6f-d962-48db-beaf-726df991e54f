package com.ctrip.car.market.coupon.restful.utils;

import java.lang.reflect.Field;

public class ReflectUtil {

    public static Object getFieldValue(Object obj, String fieldName) throws NoSuchFieldException, IllegalAccessException {
        if (obj == null) return null;
        Field field = getField(obj, fieldName);
        if (field != null) {
            field.setAccessible(true);
            return field.get(obj);
        }
        return null;
    }

    public static Field getField(Object obj, String fieldName) throws NoSuchFieldException {
        if (obj == null) return null;
        return obj.getClass().getDeclaredField(fieldName);
    }

    public static void setFieldValue(Object obj, String fieldName, Object fieldValue) throws NoSuchFieldException, IllegalAccessException {
        Field field = getField(obj, fieldName);
        field.setAccessible(true);
        field.set(obj, fieldValue);
    }
}
