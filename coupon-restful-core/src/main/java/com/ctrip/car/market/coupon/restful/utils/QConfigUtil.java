package com.ctrip.car.market.coupon.restful.utils;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.infosec.kms.KmsUtilCacheable;
import com.ctrip.infosec.kms.pojo.KmsKey;
import com.ctrip.infosec.kms.pojo.KmsResponse;
import qunar.tc.qconfig.client.MapConfig;

import java.util.Map;
import java.util.Objects;

public class QConfigUtil {

    private static final ILog logger = LogManager.getLogger(QConfigUtil.class);

    public static String getByFileAndKey(String fileName, String key) {
        try {
            MapConfig configMap = MapConfig.get(fileName);
            if (configMap != null) {
                Map<String, String> map = configMap.asMap();
                if (map.size() >= 1 && map.containsKey(key)) {
                    return map.get(key);
                }
            }
            return "";
        } catch (Exception e) {
            logger.error("QConfigUtil", e);
        }
        return "";
    }

}
