package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.qunar;

import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import okhttp3.*;
import okio.ByteString;
import org.apache.commons.io.output.StringBuilderWriter;
import org.apache.commons.lang3.StringUtils;

import java.io.PrintWriter;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.TimeUnit;
public class QunarUserProxy {
    public static final MediaType JSON_MEDIA = MediaType.parse("application/json");
    private static final ILog LOGGER = LogManager.getLogger(QunarUserProxy.class);
    private static final String QUNAR_USER_GET_ENDPOINT_KEY = "qunar.user.get";
    private static final OkHttpClient HTTP_CLIENT;

    static {
        HTTP_CLIENT = new OkHttpClient.Builder().readTimeout(2, TimeUnit.SECONDS).connectTimeout(1, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true).build();
    }


    public static String getUid(String uuid, String clientId, String oid) {
        try {
            QunarTokenRequest request = new QunarTokenRequest();
            request.setUuid(uuid);
            request.setGid(clientId);
            request.setOid(oid);
            request.setTimestamp(System.currentTimeMillis());

            String endpoint = "http://igt.corp.qunar.com/service/open/getuser";
            // couponServiceQConfig.getValueByKeyFromQconfig(QUNAR_USER_GET_ENDPOINT_KEY);
            if (StringUtils.isEmpty(endpoint)) {
                return StringUtils.EMPTY;
            }
            QunarUserInfo userInfo = post(endpoint, request, QunarUserInfo.class);

            return userInfo == null ? StringUtils.EMPTY : userInfo.getUserId();
        } catch (Exception ex) {
            LOGGER.error("getUid", ex);
        }
        return null;
    }

    private static <T> T post(String url, QunarTokenRequest request, Class<T> tClass) {
        String requestPayLoad = null;
        String responsePayLoad = null;
        Exception exception = null;

        try {
            requestPayLoad = JsonUtil.toString(request);
            RequestBody requestBody = RequestBody.create(JSON_MEDIA, ByteString.encodeUtf8(requestPayLoad));
            Request httpRequest = new Request.Builder().url(url).post(requestBody).build();
            Response response = HTTP_CLIENT.newCall(httpRequest).execute();
            responsePayLoad = response.body().string();
            QunarTokenResponse entity =JsonUtils.convertJson2Object(responsePayLoad, QunarTokenResponse.class);
            if (entity.getBstatus() != null && 0 == entity.getBstatus().getCode() && entity.getData() != null) {
                return JsonUtils.convertJson2Object(JsonUtils.toJson(entity.getData()), tClass);
            }

            return null;
        } catch (Exception e) {
            exception = e;
            return null;
        } finally {
            StringBuilder builder = new StringBuilder(128);
            if (StringUtils.isNotEmpty(requestPayLoad)) {
                builder.append("[request message]:\n\t");
                builder.append(requestPayLoad);
            }
            if (StringUtils.isNotEmpty(responsePayLoad)) {
                builder.append("\n[response message]:\n\t");
                builder.append(responsePayLoad);
            }
            if (exception != null) {
                StringBuilderWriter sw = new StringBuilderWriter(512);
                PrintWriter pw = new PrintWriter(sw);
                exception.printStackTrace(pw);
                builder.append("\n[Exception information]:\n\t");
                builder.append(sw.toString());
            }

            LOGGER.info("qunar get uid call information", builder.toString());
        }
    }

    static class QunarTokenResponse {
        Object data;
        Bstatus bstatus = new Bstatus();
        String key;
        ArrayList<Map<String, String>> res;

        public Object getData() {
            return data;
        }

        public void setData(Object data) {
            this.data = data;
        }

        public Bstatus getBstatus() {
            return bstatus;
        }

        public ArrayList<Map<String, String>> getRes() {
            return res;
        }

        public void setRes(ArrayList<Map<String, String>> res) {
            this.res = res;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }


    static class Bstatus {
        int code = 0;
        String des = "";

        public Bstatus() {
        }

        public Bstatus(int code, String des) {
            this.code = code;
            this.des = des;
        }

        public int getCode() {
            return code;
        }

        public String getDes() {
            return des;
        }

        public void setDes(String des) {
            this.des = des;
        }
    }

    static class QunarTokenRequest {
        private static final long serialVersionUID = 1L;

        protected String vendorkey = "ctrip";
        protected String version = "1";
        protected Long timestamp;
        private String uuid;
        private String oid;
        private String gid;

        public String getVendorkey() {
            return vendorkey;
        }

        public void setVendorkey(String vendorkey) {
            this.vendorkey = vendorkey;
        }

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public Long getTimestamp() {
            return timestamp;
        }

        public void setTimestamp(Long timestamp) {
            this.timestamp = timestamp;
        }

        public String getUuid() {
            return uuid;
        }

        public void setUuid(String uuid) {
            this.uuid = uuid;
        }

        public String getOid() {
            return oid;
        }

        public void setOid(String oid) {
            this.oid = oid;
        }

        public String getGid() {
            return gid;
        }

        public void setGid(String gid) {
            this.gid = gid;
        }
    }


    static class QunarUserInfo {
        private String userName;
        private String userId;
        private String phoneNum;
        private String email;
        private String expiredTime;
        private Integer status;
        private Integer isActive;
        private String phoneCode;

        public String getUserName() {
            return userName;
        }

        public void setUserName(String userName) {
            this.userName = userName;
        }

        public String getPhoneNum() {
            return phoneNum;
        }

        public void setPhoneNum(String phoneNum) {
            this.phoneNum = phoneNum;
        }

        public String getEmail() {
            return email;
        }

        public void setEmail(String email) {
            this.email = email;
        }

        public String getExpiredTime() {
            return expiredTime;
        }

        public void setExpiredTime(String expiredTime) {
            this.expiredTime = expiredTime;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }

        public Integer getActive() {
            return isActive;
        }

        public void setActive(Integer active) {
            isActive = active;
        }

        public String getUserId() {
            return userId;
        }

        public void setUserId(String userId) {
            this.userId = userId;
        }

        public String getPhoneCode() {
            return phoneCode;
        }

        public void setPhoneCode(String phoneCode) {
            this.phoneCode = phoneCode;
        }

        @Override
        public String toString() {
            return "QunarUserInfo [userName=" + userName + ", userId=" + userId + ", phoneNum=" + phoneNum + ", email="
                    + email + ", expiredTime=" + expiredTime + ", status=" + status + ", isActive=" + isActive + "]";
        }
    }
}
