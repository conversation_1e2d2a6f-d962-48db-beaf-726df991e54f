package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.mkt;

import com.ctrip.car.market.client.contract.QueryMktMainProcessInfoRequestType;
import com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType;
import com.ctrip.car.market.client.contract.QueryMktSupportDetailsRequestType;
import com.ctrip.car.market.client.contract.QueryMktSupportDetailsResponseType;
import com.ctrip.car.market.service.entity.contract.SDMarketingServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
public class MktSupportProxy {

    public SDMarketingServiceClient sdMarketingServiceClient = SDMarketingServiceClient.getInstance();

    private static final ILog LOG = LogManager.getLogger(MktSupportProxy.class);

    public QueryMktSupportDetailsResponseType getMktSupportDetails(QueryMktSupportDetailsRequestType request) {
        try {
            return sdMarketingServiceClient.queryMktSupportDetails(request);
        } catch (Exception e) {
            LOG.error("MktSupportProxy getMktSupportDetails error", e);
            return null;
        }
    }

    public QueryMktMainProcessInfoResponseType getMktMainProcessInfo(QueryMktMainProcessInfoRequestType request) {
        try {
            return sdMarketingServiceClient.queryMktMainProcessInfo(request);
        } catch (Exception e) {
            LOG.error("MktSupportProxy getMktMainProcessInfo error", e);
            return null;
        }
    }
}
