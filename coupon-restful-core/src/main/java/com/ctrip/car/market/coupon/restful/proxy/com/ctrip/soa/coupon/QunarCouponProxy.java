package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon;

import com.ctrip.car.market.coupon.restful.dto.*;
import com.ctrip.car.market.coupon.restful.enums.QunarTag;
import com.ctrip.car.market.coupon.restful.qconfig.PiaoQunarCouponConfig;
import com.ctrip.car.market.coupon.restful.redis.RedisProvider;
import com.ctrip.car.market.coupon.restful.utils.JsonUtils;
import com.ctrip.car.market.coupon.restful.utils.LogUtil;
import com.ctrip.framework.foundation.Foundation;
import com.ctrip.framework.triplog.client.tag.TagMarker;
import com.ctrip.framework.triplog.client.tag.TagMarkerBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListenableFuture;
import com.ning.http.client.Response;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import qunar.hc.QHttpOption;
import qunar.hc.QunarAsyncClient;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

@Component
public class QunarCouponProxy {

    private static final Logger log = LoggerFactory.getLogger(QunarCouponProxy.class);

    private final QunarAsyncClient qunarAsyncClient = new QunarAsyncClient();

    @Resource
    private PiaoQunarCouponConfig piaoQunarCouponConfig;

    private final static String oldCustomerKeyFormat = "car.market.coupon.qunar.old.customer.%s";

    private QunarParam requestConvert(QunarUserInfo userInfo) {
        QunarParam param = new QunarParam();
        param.setSource("100014699");
        param.setUserId(userInfo.getUid().toString());
        param.setUserName(userInfo.getUserName());
        return param;
    }

    public boolean isNewCustomer(QunarUserInfo userInfo) throws Exception {
        String key = String.format(oldCustomerKeyFormat, userInfo.getUid());
        String cacheValue = RedisProvider.get(key);
        //命中老客缓存直接返回
        if (StringUtils.isNotEmpty(cacheValue)) {
            return false;
        }
        String response = formPost(QunarTag.NewUser.getName(), requestConvert(userInfo));
        if (StringUtils.isEmpty(response)) {
            return false;
        }
        QunarCustomerResponse qunarCustomerResponse = JsonUtils.convertJson2Object(response, QunarCustomerResponse.class);
        //老客添加缓存
        if (qunarCustomerResponse != null && qunarCustomerResponse.getResult() != null && qunarCustomerResponse.getResult().getResult() != null
                && StringUtils.equalsIgnoreCase(qunarCustomerResponse.getResult().getResult().get("value"), "WIRELESS_PIAO_OLD")) {
            RedisProvider.set(key, userInfo.getUid().toString(), 60 * 60);
        }
        return qunarCustomerResponse != null && qunarCustomerResponse.getResult() != null && qunarCustomerResponse.getResult().getResult() != null
                && StringUtils.equalsIgnoreCase(qunarCustomerResponse.getResult().getResult().get("value"), "WIRELESS_PIAO_NEW");
    }

    public boolean canSendCoupon(QunarUserInfo userInfo) throws Exception {
        String response = formPost(QunarTag.CanSend.getName(), requestConvert(userInfo));
        if (StringUtils.isEmpty(response)) {
            return false;
        }
        QunarCanSendResponse qunarCanSendResponse = JsonUtils.convertJson2Object(response, QunarCanSendResponse.class);
        return qunarCanSendResponse != null && qunarCanSendResponse.getResult() != null && qunarCanSendResponse.getResult().getCode() == 0;
    }

    public boolean sendCoupon(QunarUserInfo userInfo) throws Exception {
        String response = formPost(QunarTag.SendCoupon.getName(), requestConvert(userInfo));
        if (StringUtils.isEmpty(response)) {
            return false;
        }
        QunarSendResponse qunarSendResponse = JsonUtils.convertJson2Object(response, QunarSendResponse.class);
        if (qunarSendResponse != null && qunarSendResponse.getResult() != null && CollectionUtils.isNotEmpty(qunarSendResponse.getResult().getResult())) {
            esLog(userInfo.getUid().toString(), response);
        }
        return qunarSendResponse != null && qunarSendResponse.getResult() != null && qunarSendResponse.getResult().getCode() == 0;
    }

    private String formPost(String tag, QunarParam params) {
        String res = "";
        try {
            String paramsString = JsonUtils.convertObject2Json(params);
            Long createTime = System.currentTimeMillis();
            QHttpOption option = new QHttpOption();
            option.addHeader("Content-Type", "application/x-www-form-urlencoded");
            option.addPostFormData("tag", tag);
            option.addPostFormData("token", piaoQunarCouponConfig.getT());
            option.addPostFormData("createTime", createTime.toString());
            option.addPostFormData("sign", getSign(tag, createTime, paramsString));
            option.addPostFormData("params", paramsString);
            long s0 = System.currentTimeMillis();
            ListenableFuture<Response> responseFuture = qunarAsyncClient.post(piaoQunarCouponConfig.getQunarUrl(), option);
            Response response = responseFuture.get(piaoQunarCouponConfig.getHttpTimeOut(), TimeUnit.MILLISECONDS);
            long s1 = System.currentTimeMillis();
            if (response != null && response.getStatusCode() == 200) {
                res = response.getResponseBody("UTF-8");
            }
            Map<String, String> logTag = Maps.newHashMap();
            logTag.put("uid", params.getUserId());
            LogUtil.info("formPost", s1 - s0 + "\n" + piaoQunarCouponConfig.getQunarUrl() + "\n" + tag + "\n" + params.getUserId() + "\n" + paramsString + "\n" + res, logTag);
        } catch (Exception ex) {
            LogUtil.error("formPost", piaoQunarCouponConfig.getQunarUrl() + "\n" + tag + "\n" + params.getUserId() + "\n", ex);
        }
        return res;
    }

    private String getSign(String tag, Long createTime, String params) {
        List<String> list = Lists.newArrayList();
        list.add("token=" + piaoQunarCouponConfig.getT());
        list.add("tag=" + tag);
        list.add("createTime=" + createTime);
        list.add("params=" + params);
        list.add("key=" + piaoQunarCouponConfig.getK());
        Collections.sort(list);
        StringBuffer sb = new StringBuffer();
        for (String param : list) {
            sb.append(param);
        }
        return DigestUtils.md5Hex(sb.toString());
    }

    public static void esLog(String uid, String content) {
        CompletableFuture.runAsync(() -> {
            try {
                TagMarker marker = TagMarkerBuilder.newBuilder().scenario("car-common-log")
                        .add("timestamp", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()))
                        .add("cat_client_appid", Foundation.app().getAppId())
                        .add("operation_type", "qunarSendCoupon")
                        .add("content", content)
                        .add("primary_key", uid)
                        .build();
                log.info(marker, "qunarSendCoupon");
            } catch (Exception e) {
                log.warn("commonLog", e);
            }
        });
    }
}
