package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.piao.QueryQunarCouponPopupRequestType;
import com.ctrip.car.market.coupon.restful.contract.piao.QueryQunarCouponPopupResponseType;
import com.ctrip.car.market.coupon.restful.dto.QunarUserInfo;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.QunarCouponProxy;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.coupon.QunarUidServiceProxy;
import com.ctrip.car.market.coupon.restful.qconfig.PiaoQunarCouponConfig;
import com.ctrip.car.market.coupon.restful.utils.AbUtil;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class QunarCouponPopupBusiness {

    @Resource
    private QunarCouponProxy qunarCouponProxy;

    @Resource
    private QunarUidServiceProxy qunarUidServiceProxy;

    @Resource
    private PiaoQunarCouponConfig piaoQunarCouponConfig;

    public QueryQunarCouponPopupResponseType queryPopup(QueryQunarCouponPopupRequestType request) {
        QueryQunarCouponPopupResponseType response = new QueryQunarCouponPopupResponseType();
        if (StringUtils.isEmpty(request.getScookie())) {
            return response;
        }
        try {
            //查询去哪儿用户信息
            QunarUserInfo userInfo = qunarUidServiceProxy.queryQunarUid(request.getScookie());
            if (userInfo == null) {
                response.setUserType(0);
                return response;
            }
            //判断新老客
            if (!qunarCouponProxy.isNewCustomer(userInfo)) {
                response.setUserType(2);
                response.setImageUrl(piaoQunarCouponConfig.getOldMaterialUrl());
                Metrics.build().withTag("result", "2").recordOne("qunarCouponPopup");
                return response;
            }
            //判断是否允许发券
            if (qunarCouponProxy.canSendCoupon(userInfo)) {
                //增加ab测试，命中B版本发券+弹新客窗，其他版本不发券不弹窗
                String version = AbUtil.getAlternative("250514_DSJT_qunar", userInfo.getUid().toString());
                if (!StringUtils.equalsIgnoreCase(version, "B")) {
                    response.setUserType(0);
                    Metrics.build().withTag("result", "0").recordOne("qunarCouponPopup");
                    return response;
                }
                //发券
                if (qunarCouponProxy.sendCoupon(userInfo)) {
                    response.setUserType(1);
                    response.setImageUrl(piaoQunarCouponConfig.getNewMaterialUrl());
                    Metrics.build().withTag("result", "1").recordOne("qunarCouponPopup");
                    return response;
                }
            }
            response.setUserType(2);
            response.setImageUrl(piaoQunarCouponConfig.getOldMaterialUrl());
            Metrics.build().withTag("result", "2").recordOne("qunarCouponPopup");
            return response;
        } catch (Exception ex) {
            response.setUserType(0);
            return response;
        }
    }
}
