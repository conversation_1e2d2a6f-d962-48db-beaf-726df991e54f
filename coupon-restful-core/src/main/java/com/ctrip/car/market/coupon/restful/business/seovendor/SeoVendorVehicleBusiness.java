package com.ctrip.car.market.coupon.restful.business.seovendor;

import com.ctrip.car.market.coupon.restful.business.SeoService;
import com.ctrip.car.market.coupon.restful.cache.SeoVendorCache;
import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.contract.seo.*;
import com.ctrip.car.market.coupon.restful.contract.seo.PriceInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.VehicleInfo;
import com.ctrip.car.market.coupon.restful.dto.TripUrlParams;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.NewVehicleURLUtils;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorCityDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotVendorInformationDO;
import com.ctrip.car.osd.shopping.api.entity.*;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SeoVendorVehicleBusiness {

    @Resource
    private SeoVendorCache seoVendorCache;

    @Resource
    private SeoService service;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    @Resource
    private TripConfig tripConfig;

    private final Set<String> labelSet = Sets.newHashSet("discount", "reduction");

    public QuerySeoVehicleListResponseType queryVehicleList(QuerySeoVehicleListRequestType request) {
        QuerySeoVehicleListResponseType response = new QuerySeoVehicleListResponseType();
        SeoHotVendorCityDO vendorCityDO = Optional.ofNullable(request.getCityId()).orElse(0) > 0 ? seoVendorCache.queryVendorCity(request.getVendorCode(), request.getCityId()) : null;
        SeoHotVendorDO vendorDO = seoVendorCache.queryVendor(request.getVendorCode());
        if (vendorDO == null) {
            return response;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) > 0 && vendorCityDO == null) {
            return response;
        }
        List<SeoHotVendorInformationDO> informationList = Optional.ofNullable(request.getCityId()).orElse(0) > 0
                ? Lists.newArrayList(service.queryVendorCity(request.getVendorCode(), request.getCityId()))
                : service.queryVendorTop3City(request.getVendorCode());

        List<QueryRecomdProductsResponseType> responseType = queryVehicle(request.getBaseRequest(), request.getHead(), informationList, false);
        response.setCityVehicleList(resConvert(responseType, request));
        response.setVendorList(service.getTopVendor(CollectionUtils.isNotEmpty(responseType) ? responseType.getFirst() : null));
        return response;
    }

    public List<QueryRecomdProductsResponseType> queryVehicle(BaseRequest baseRequest, MobileRequestHead head, List<SeoHotVendorInformationDO> informationList, boolean cache) {
        List<QueryRecomdProductsResponseType> soaRes = Lists.newArrayList();
        for (SeoHotVendorInformationDO info : informationList) {
            if (info == null) {
                continue;
            }
            QueryRecomdProductsRequestType soaReq = reqConvert(baseRequest, head, info);
            QueryRecomdProductsResponseType res = osdShoppingProxy.queryRecomdProducts(soaReq, cache);
            if (res != null && CollectionUtils.isNotEmpty(res.getRecomdProductResList())
                    && CollectionUtils.isNotEmpty(res.getRecomdProductResList().getFirst().getProducts())
                    && res.getRecomdProductResList().getFirst().getPickupLocation() != null) {
                soaRes.add(res);
            }
        }
        return soaRes;
    }

    private QueryRecomdProductsRequestType reqConvert(BaseRequest baseRequest, MobileRequestHead head, SeoHotVendorInformationDO info) {
        QueryRecomdProductsRequestType soaReq = new QueryRecomdProductsRequestType();
        soaReq.setBaseRequest(new com.ctriposs.baiji.rpc.common.types.BaseRequest());
        soaReq.getBaseRequest().setRequestId(baseRequest.getRequestId());
        soaReq.getBaseRequest().setChannelId(baseRequest.getChannelId());
        soaReq.getBaseRequest().setLocale(baseRequest.getLocale());
        soaReq.getBaseRequest().setCurrencyCode(baseRequest.getCurrencyCode());
        soaReq.getBaseRequest().setUid(BaseUtils.getUidByCommon(head));
        soaReq.setSence(7);

        QueryParamDTO item = new QueryParamDTO();
        item.setPickupLocation(new LocationRequestInfo());
        item.getPickupLocation().setCityId(info.getCityId());
        item.getPickupLocation().setLocationCode(info.getPoiCode());
        item.getPickupLocation().setLocationName(info.getPoiName());
        item.getPickupLocation().setLocationType(info.getPoiType());
        item.getPickupLocation().setDate(getDate(7));
        item.setVendorCode(info.getVendorCode());
        soaReq.setQueryParams(Lists.newArrayList(item));

        return soaReq;
    }

    private Calendar getDate(int day) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, day);
        calendar.set(Calendar.HOUR_OF_DAY, 10);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar;
    }

    private List<CityVehicleInfo> resConvert(List<QueryRecomdProductsResponseType> soaRes, QuerySeoVehicleListRequestType request) {
        if (CollectionUtils.isEmpty(soaRes)) {
            return Lists.newArrayList();
        }
        LanguageLocaleEnum lEnum = LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(request.getBaseRequest().getLocale());
        CurrencyEnum cEnum = CurrencyEnum.getCurrrencyEnum(request.getBaseRequest().getCurrencyCode());

        return soaRes.stream().map(l -> {
            CityVehicleInfo item = new CityVehicleInfo();
            item.setCityId(l.getRecomdProductResList().get(0).getPickupLocation().getCityId());
            item.setCityName(l.getRecomdProductResList().get(0).getPickupLocation().getCityName());
            item.setUrl(buildUrl(l.getSourceCountryId(), l.getRecomdProductResList().get(0), null, request.getBaseRequest()));
            item.setVehicleList(l.getRecomdProductResList().get(0).getProducts().stream().map(li -> {
                VehicleInfo vehicleInfo = new VehicleInfo();
                vehicleInfo.setVehicleCode(li.getVehicle().getVehicleCode());
                vehicleInfo.setVehicleName(li.getVehicle().getVehicleName());
                vehicleInfo.setGroupCode(li.getVehicle().getGroupCode());
                vehicleInfo.setGroupName(li.getVehicle().getGroupName());
                vehicleInfo.setSimilarDesc(li.getVehicle().getSimilarDesc());
                vehicleInfo.setDoorNo(li.getVehicle().getDoorNo());
                vehicleInfo.setPassengerNo(li.getVehicle().getPassengerNo());
                vehicleInfo.setLuggageNo(li.getVehicle().getLuggageNo());
                vehicleInfo.setImageUrl(li.getVehicle().getImageUrl());
                vehicleInfo.setLabel(CollectionUtils.isNotEmpty(li.getLabels()) ? li.getLabels().stream().map(label -> {
                    if (StringUtils.isEmpty(label.getCode()) || !labelSet.contains(label.getCode().toLowerCase())) {
                        return null;
                    }
                    LabelInfo labelInfo = new LabelInfo();
                    labelInfo.setCode(label.getCode());
                    labelInfo.setName(label.getName());
                    labelInfo.setExtDesc(label.getExtDesc());
                    return labelInfo;
                }).filter(Objects::nonNull).collect(Collectors.toList()) : Lists.newArrayList());
                vehicleInfo.setPrice(new PriceInfo());
                vehicleInfo.getPrice().setPrice(li.getPrice().getCurrentDailyPrice());
                vehicleInfo.getPrice().setOriginalPrice(li.getPrice().getCurrentOriginalDailyPrice());
                vehicleInfo.getPrice().setCurrency(li.getPrice().getCurrency());
                vehicleInfo.getPrice().setPrefix(li.getPrice().getPrefix());
                vehicleInfo.getPrice().setSuffix(li.getPrice().getSuffix());
                if (li.getPrice().getCurrentOriginalDailyPrice() != null) {
                    vehicleInfo.getPrice().setOriginalPriceStr(service.currencyString(li.getPrice().getCurrentOriginalDailyPrice(), lEnum, cEnum));
                }
                vehicleInfo.getPrice().setPriceStr(service.currencyString(li.getPrice().getCurrentDailyPrice(), lEnum, cEnum));
                vehicleInfo.setUrl(buildUrl(l.getSourceCountryId(), l.getRecomdProductResList().get(0), li, request.getBaseRequest()));
                return vehicleInfo;
            }).limit(tripConfig.getMaxVehicleCount()).collect(Collectors.toList()));
            return item;
        }).collect(Collectors.toList());
    }

    private String buildUrl(Integer sourceCountryId, RecomdProductRes productRes, RecomdProduct product, com.ctrip.car.market.coupon.restful.contract.BaseRequest baseRequest) {
        TripUrlParams params = new TripUrlParams();
        params.setChannelId(baseRequest.getChannelId());
        params.setLocale(baseRequest.getLocale());
        params.setCurr(baseRequest.getCurrencyCode());
        if (product != null) {
            params.setPrice(product.getPrice().getCurrentDailyPrice());
            params.setVehicleKey(product.getVehicle().getVehicleKey());
        }
        params.setCityName(productRes.getPickupLocation().getCityName());
        params.setSourceCountryId(sourceCountryId);
        return tripConfig.getPcUrl() + NewVehicleURLUtils.getServerTripPcUrlList(params, productRes.getPickupLocation(), productRes.getReturnLocation());
    }
}
