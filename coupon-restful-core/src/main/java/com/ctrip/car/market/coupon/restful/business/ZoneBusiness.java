package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.BaseResponse;
import com.ctrip.car.market.coupon.restful.contract.QueryZonesRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryZonesResponseType;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend.CarCrossRecommendedServiceProxy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Objects;

@Service
public class ZoneBusiness {

    @Autowired
    private CarCrossRecommendedServiceProxy carCrossRecommendedServiceProxy;

    public QueryZonesResponseType queryZones(QueryZonesRequestType request) {
        if (Objects.isNull(request.getBaseRequest())) {
            QueryZonesResponseType response = new QueryZonesResponseType();
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setCode("-1");
            baseResponse.setMessage("baseRequest is null");
            response.setBaseResponse(baseResponse);
            return response;
        }
        return carCrossRecommendedServiceProxy.queryZones(request);
    }
}
