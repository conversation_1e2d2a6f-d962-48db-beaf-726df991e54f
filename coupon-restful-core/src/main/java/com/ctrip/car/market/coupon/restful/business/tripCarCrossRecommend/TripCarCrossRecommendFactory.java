package com.ctrip.car.market.coupon.restful.business.tripCarCrossRecommend;

import com.ctrip.car.market.coupon.restful.contract.ServiceType;
import com.ctrip.car.market.coupon.restful.contract.TripRecommendSceneEnum;

import java.util.Collections;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
public class TripCarCrossRecommendFactory {

    private static Map<TripRecommendSceneEnum, Map<ServiceType, TripCarCrossRecommend>> strategyMap = new ConcurrentHashMap<>();

    // 自动注册策略
    public static void registerStrategy(TripCarCrossRecommend strategy) {
        TripCrossRecommendAnnotation meta = strategy.getClass().getAnnotation(TripCrossRecommendAnnotation.class);
        strategyMap.computeIfAbsent(meta.targetDepartment(), k -> new ConcurrentHashMap<>()).put(meta.serviceType(), strategy);
    }

    // 获取推荐策略
    public static TripCarCrossRecommend routeStrategy(TripRecommendSceneEnum scene, ServiceType serviceType) {
        Map<ServiceType, TripCarCrossRecommend> sceneStrategies = strategyMap.getOrDefault(scene, Collections.emptyMap());
        if (sceneStrategies.isEmpty()) {
            return null;
        }
        // 默认策略兜底
        return sceneStrategies.get(serviceType);
    }
}
