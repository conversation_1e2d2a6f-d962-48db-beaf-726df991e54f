package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.crossrecommend;

import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.crossrecommend.service.contract.CarCrossRecommendedServiceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.springframework.stereotype.Service;

@Service
public class CarCrossRecommendedServiceProxy {

    public CarCrossRecommendedServiceClient sdMarketingServiceClient = CarCrossRecommendedServiceClient.getInstance();
    private static final ILog log = LogManager.getLogger("CarCrossRecommendedServiceProxy");

    public QueryTripSeoProductsResponseType queryTripSeoProducts(QueryTripSeoProductsRequestType request) {
        try {
            com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryTripSeoProductsRequestType requestType = convertRequest(request);
            com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryTripSeoProductsResponseType responseType = sdMarketingServiceClient.queryTripSeoProducts(requestType);
            QueryTripSeoProductsResponseType response = CarCrossRecommendedConverter.INSTANCE.converterQueryTripSeoProductsResponseType(responseType);
            return response;
        } catch (Exception e) {
            log.error("CarCrossRecommendedServiceProxy::queryTripSeoProducts", e);
            QueryTripSeoProductsResponseType response = new QueryTripSeoProductsResponseType();
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setCode("500");
            baseResponse.setMessage("SYSTEM_EXCEPTION");
            response.setBaseResponse(baseResponse);
            return response;
        }
    }

    public QueryZonesResponseType queryZones(QueryZonesRequestType request) {
        try {
            com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryZonesRequestType requestType = convertRequest(request);
            com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryZonesResponseType responseType = sdMarketingServiceClient.queryZones(requestType);
            QueryZonesResponseType response = CarCrossRecommendedConverter.INSTANCE.converterQueryZonesResponseType(responseType);
            return response;
        } catch (Exception e) {
            log.error("CarCrossRecommendedServiceProxy::queryZones", e);
            QueryZonesResponseType response = new QueryZonesResponseType();
            BaseResponse baseResponse = new BaseResponse();
            baseResponse.setCode("500");
            baseResponse.setMessage("SYSTEM_EXCEPTION");
            response.setBaseResponse(baseResponse);
            return response;
        }
    }

    private com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryTripSeoProductsRequestType convertRequest(QueryTripSeoProductsRequestType request) {
        com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryTripSeoProductsRequestType requestType = new com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryTripSeoProductsRequestType();
        requestType.setSort(request.getSort());
        requestType.setCityId(request.getCityId());
        requestType.setTopNum(request.getTopNum());
        com.ctrip.car.market.BaseRequest baseRequest = CarCrossRecommendedConverter.INSTANCE.converterBaseRequest(request.getBaseRequest());
        requestType.setBaseRequest(baseRequest);
        return requestType;
    }

    private com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryZonesRequestType convertRequest(QueryZonesRequestType request) {
        com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryZonesRequestType requestType = new com.ctrip.car.market.crossrecommend.service.contract.servicetype.QueryZonesRequestType();
        requestType.setAirportCode(request.getAirportCode());
        requestType.setCityId(request.getCityId());
        requestType.setType(request.getType());
        com.ctrip.car.market.BaseRequest baseRequest = CarCrossRecommendedConverter.INSTANCE.converterBaseRequest(request.getBaseRequest());
        requestType.setBaseRequest(baseRequest);
        return requestType;
    }
}
