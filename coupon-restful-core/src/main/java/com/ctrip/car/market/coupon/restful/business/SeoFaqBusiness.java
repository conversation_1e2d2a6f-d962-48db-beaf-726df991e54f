package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.contract.seo.FaqInfo;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.QuerySeoFaqResponseType;
import com.ctrip.car.market.coupon.restful.dto.KeyValueDto;
import com.ctrip.car.market.coupon.restful.dto.LocaleSortDto;
import com.ctrip.car.market.coupon.restful.enums.MetricsEnum;
import com.ctrip.car.market.coupon.restful.enums.SeoShark;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping.OsdShoppingProxy;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.utils.LanguageUtils;
import com.ctrip.car.market.coupon.restful.utils.Metrics;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCityinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotCountryinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotDestinatioinfoDO;
import com.ctrip.car.market.job.common.entity.seo.SeoHotInformationDO;
import com.ctrip.car.osd.shopping.api.entity.QueryRecomdProductsResponseType;
import com.ctrip.car.osd.shopping.api.entity.RecomdProduct;
import com.ctrip.car.osd.shopping.api.entity.RecomdProductRes;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.ctriposs.baiji.rpc.mobile.common.types.MobileRequestHead;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SeoFaqBusiness {

    @Resource
    private SeoService service;

    @Resource
    private SeoVehicleBusiness vehicleBusiness;

    @Resource
    private TripConfig tripConfig;

    @Resource
    private OsdShoppingProxy osdShoppingProxy;

    public QuerySeoFaqResponseType queryFaq(QuerySeoFaqRequestType request) {
        QuerySeoFaqResponseType response = new QuerySeoFaqResponseType();
        if (!checkRequest(request)) {
            Metrics.build().withTag("errorType", "paraError").withTag("result", "0").recordOne(MetricsEnum.SEO_FAQ.getTitle());
            response.setBaseResponse(ResponseUtil.fail("para error"));
            return response;
        }
        //热门poi
        SeoHotDestinatioinfoDO hotDestinationInfo = service.queryHotDestinationFirst(request.getCountryId(), request.getCityId(), request.getPoiType(), request.getPoiCode());
        if (hotDestinationInfo == null) {
            //没有机场时，使用城市默认配置
            PlaceDetailsDTO globalInfo = service.queryCityDefaultPoi(request.getPoiCode(), request.getCityId(), request.getBaseRequest().getLocale());
            if (globalInfo != null) {
                hotDestinationInfo = new SeoHotDestinatioinfoDO();
                hotDestinationInfo.setPoiCode(globalInfo.getCarPlaceId());
                hotDestinationInfo.setPoiType(0);
                hotDestinationInfo.setCityId(request.getCityId());
            }
        }
        if (hotDestinationInfo == null) {
            Metrics.build().withTag("errorType", "destinationError").withTag("result", "0").recordOne(MetricsEnum.SEO_FAQ.getTitle());
            response.setBaseResponse(ResponseUtil.fail("destination error"));
            return response;
        }
        //热门信息
        SeoHotInformationDO information = service.queryInformationByPoi(hotDestinationInfo.getPoiType(), hotDestinationInfo.getPoiCode());
        response.setFaqList(getFaq(hotDestinationInfo, information, request, response));
        response.setCarCard(osdShoppingProxy.getCarCard(request.getBaseRequest(), hotDestinationInfo.getPoiCode(), null));
        response.setCacheTime(osdShoppingProxy.getCacheTime());
        //供应商数量
        response.setVendorNum(service.getVendorCount(request));
        response.setBaseResponse(ResponseUtil.success());
        Metrics.build().withTag("result", CollectionUtils.isNotEmpty(response.getFaqList()) ? "1" : "0").recordOne(MetricsEnum.SEO_FAQ.getTitle());
        return response;
    }

    private List<FaqInfo> getFaq(SeoHotDestinatioinfoDO destinatioinfo, SeoHotInformationDO information, QuerySeoFaqRequestType request, QuerySeoFaqResponseType response) {
        String locale = request.getBaseRequest().getLocale();
        String poiName = getPoiName(request);
        List<RecomdProductRes> productList = queryProduct(request.getBaseRequest(), request.getHead(), destinatioinfo);
        //最低价
        String lowPrice = getLowPrice(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode());
        response.setLowPrice(lowPrice);
        response.setLowPriceValue(getLowPriceValue(productList));
        //站点url
        String webSite = getWebSite(request);
        if (StringUtils.isNotEmpty(webSite)) {
            webSite = "<a href='" + webSite + "'>" + LanguageUtils.sharkValFormat(SeoShark.CarRental.getValue(request.getBaseRequest().getLocale()), poiName) + "</a>";
        }
        //热门供应商
        String vendorName = information != null ? information.getVendorName() : null;
        //热门供应商车型报价
        String vendorPrice = information != null && information.getVendorId() != null ? getVendorPrice(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), information.getVendorId().toString()) : null;
        String vehicleGroupId = information != null && information.getVehicleGroupId() != null ? information.getVehicleGroupId().toString() : null;
        //热门车型组
        String vehicleGroupName = information != null && StringUtils.isNotEmpty(information.getVehicleGroupName()) ? service.getVehicleGroupName(vehicleGroupId, information.getVehicleGroupName(), locale) : null;
        //经济型报价
        String smallPrice = getVehicleGroupPrice(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("small"));
        //舒适型报价
        String mediumPrice = getVehicleGroupPrice(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("medium"));
        //高端车型报价
        String premiumPrice = getVehicleGroupPrice(productList, request.getBaseRequest().getLocale(), request.getBaseRequest().getCurrencyCode(), getVehicleGroupSet("premium"));
        //机场名称
        String airportName = service.getAirportName(destinatioinfo.getPoiCode(), locale);
        //低价供应商
        String lowVendorName = getLowVendor(productList);
        //trip首页url
        String tripUrl = "<a href='https://www.trip.com/carhire/'>trip.com</a>";
        //机场对应的城市名称
        String cityName = service.getCityName(destinatioinfo.getCityId(), locale);
        //页面类型 1:国家 2:城市 3:机场
        int pageType = StringUtils.isNotEmpty(request.getPoiCode()) ? 3 : Optional.ofNullable(request.getCityId()).orElse(0) > 0 ? 2 : 1;

        List<FaqInfo> result = Lists.newArrayList();
        List<Integer> sortList = getFaqSort(pageType, request.getBaseRequest().getLocale());
        for (Integer num : sortList) {
            String q = SeoShark.FaqQ.getFaq(num, locale);
            String a = SeoShark.FaqA.getFaq(num, locale);
            if (StringUtils.isEmpty(q) || StringUtils.isEmpty(a)) {
                continue;
            }
            switch (num) {
                case 1:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(lowPrice)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, lowPrice)));
                    }
                    break;
                case 2:
                case 3:
                case 6:
                case 7:
                case 8:
                case 10:
                case 14:
                case 19:
                case 20:
                case 27:
                case 35:
                case 37:
                case 40:
                    if (StringUtils.isNotEmpty(poiName)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), a));
                    }
                    break;
                case 4:
                case 9:
                case 28:
                case 41:
                    result.add(buildFaq(q, a));
                    break;
                case 5:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(vehicleGroupId)) {
                        String vehicleGroupA = getVehicleGroupFaq(vehicleGroupId, poiName, locale);
                        if (StringUtils.isNotEmpty(q) && StringUtils.isNotEmpty(vehicleGroupA)) {
                            result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), vehicleGroupA));
                        }
                    }
                    break;
                case 11:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(webSite)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, webSite)));
                    }
                    break;
                case 12:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(webSite) && StringUtils.isNotEmpty(vendorName) && StringUtils.isNotEmpty(vendorPrice)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, webSite, vendorName, vendorPrice)));
                    }
                    break;
                case 13:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(vehicleGroupName)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, vehicleGroupName)));
                    }
                    break;
                case 15:
                case 18:
                case 24:
                case 25:
                case 26:
                case 29:
                case 30:
                    if (StringUtils.isNotEmpty(poiName)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName)));
                    }
                    break;
                case 16:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(lowVendorName)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, lowVendorName)));
                    }
                    break;
                case 17:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(airportName)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, airportName)));
                    }
                    break;
                case 21:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(smallPrice)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, smallPrice)));
                    }
                    break;
                case 22:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(mediumPrice)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, mediumPrice)));
                    }
                    break;
                case 23:
                    if (StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(premiumPrice)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, premiumPrice)));
                    }
                    break;
                case 38:
                    result.add(buildFaq(q, LanguageUtils.sharkValFormat(a, tripUrl)));
                    break;
                case 42:
                    if (pageType == 3 && StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(cityName)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName, cityName), LanguageUtils.sharkValFormat(a, poiName)));
                    }
                    break;
                case 43:
                    if (pageType == 3 && StringUtils.isNotEmpty(poiName) && StringUtils.isNotEmpty(lowPrice)) {
                        result.add(buildFaq(LanguageUtils.sharkValFormat(q, poiName), LanguageUtils.sharkValFormat(a, poiName, lowPrice, poiName, poiName)));
                    }
                    break;
            }
        }
        return result.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private String getPoiName(QuerySeoFaqRequestType request) {
        if (Objects.equals(request.getPoiType(), 1) && StringUtils.isNotEmpty(request.getPoiCode())) {
            return service.getAirportName(request.getPoiCode(), request.getBaseRequest().getLocale());
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) > 0) {
            return service.getCityName(request.getCityId(), request.getBaseRequest().getLocale());
        }
        if (Optional.ofNullable(request.getCountryId()).orElse(0) > 0) {
            return service.getCountryName(request.getCountryId(), request.getBaseRequest().getLocale());
        }
        return null;
    }

    private String getWebSite(QuerySeoFaqRequestType request) {
        if (Objects.equals(request.getPoiType(), 1) && StringUtils.isNotEmpty(request.getPoiCode())) {
            SeoHotDestinatioinfoDO hotDestinatioinfoDO = service.queryHotDestinationFirst(null, null, 1, request.getPoiCode());
            return hotDestinatioinfoDO != null ? service.getSiteUrl(hotDestinatioinfoDO.getUrl(), request.getBaseRequest().getLocale()) : null;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) > 0) {
            SeoHotCityinfoDO hotCityinfoDO = service.queryHotCityByCity(request.getCityId());
            return hotCityinfoDO != null ? service.getSiteUrl(hotCityinfoDO.getUrl(), request.getBaseRequest().getLocale()) : null;
        }
        if (Optional.ofNullable(request.getCountryId()).orElse(0) > 0) {
            SeoHotCountryinfoDO hotCountryinfoDO = service.queryHotCountryByCountry(request.getCountryId());
            return hotCountryinfoDO != null ? service.getSiteUrl(hotCountryinfoDO.getUrl(), request.getBaseRequest().getLocale()) : null;
        }
        return null;
    }

    private String getVehicleGroupFaq(String vehicleGroupCode, String cityName, String locale) {
        if (StringUtils.isEmpty(vehicleGroupCode)) {
            return null;
        }
        KeyValueDto keyValue = tripConfig.getVehicleGroupMappingList().stream().filter(l -> StringUtils.equalsIgnoreCase(l.getKey(), vehicleGroupCode)).findFirst().orElse(null);
        if (keyValue == null) {
            return null;
        }
        switch (Optional.ofNullable(keyValue.getValue()).orElse("").toLowerCase()) {
            case "small":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_Small.getValue(locale), cityName);
            case "medium":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_MediumLarge.getValue(locale), cityName);
            case "premium":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_Premium.getValue(locale), cityName);
            case "suv":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_SUV.getValue(locale), cityName);
            case "van":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_VAN.getValue(locale), cityName);
            case "coupe":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_CoupeCabrio.getValue(locale), cityName);
            case "pickup":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_Pickup.getValue(locale), cityName);
            case "high":
                return LanguageUtils.sharkValFormat(SeoShark.FaqA5_HighEnd.getValue(locale), cityName);
            default:
                return null;
        }
    }

    private FaqInfo buildFaq(String q, String a) {
        if (StringUtils.isEmpty(q) || StringUtils.isEmpty(a)) {
            return null;
        }
        FaqInfo result = new FaqInfo();
        result.setQuestion(q);
        result.setAnswer(a);
        return result;
    }

    private List<Integer> getFaqSort(int pageType, String locale) {
        LocaleSortDto localeSortDto = tripConfig.getLocaleSortList().stream().filter(l -> Objects.equals(pageType, l.getType()) && StringUtils.equalsIgnoreCase(l.getLocale(), locale)).findFirst().orElse(null);
        if (localeSortDto == null || CollectionUtils.isEmpty(localeSortDto.getFaqSort())) {
            localeSortDto = tripConfig.getLocaleSortList().stream().filter(l -> Objects.equals(pageType, l.getType()) && StringUtils.equalsIgnoreCase(l.getLocale(), "en-US")).findFirst().orElse(null);
        }
        return localeSortDto == null ? Lists.newArrayList() : localeSortDto.getFaqSort();
    }

    private List<RecomdProductRes> queryProduct(BaseRequest baseRequest, MobileRequestHead head, SeoHotDestinatioinfoDO hotDestinatioinfo) {
        QueryRecomdProductsResponseType response = vehicleBusiness.queryVehicle(baseRequest, head, hotDestinatioinfo, true);
        if (response == null || CollectionUtils.isEmpty(response.getRecomdProductResList())) {
            return Lists.newArrayList();
        }
        return response.getRecomdProductResList();
    }

    private String getLowPrice(List<RecomdProductRes> productList, String locale, String currencyCode) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        BigDecimal price = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return service.currencyString(price, LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(locale), CurrencyEnum.getCurrrencyEnum(currencyCode));
    }

    private BigDecimal getLowPriceValue(List<RecomdProductRes> productList) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        return productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
    }

    private String getLowVendor(List<RecomdProductRes> productList) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        RecomdProduct product = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null).min(Comparator.comparing(l -> l.getPrice().getCurrentDailyPrice())).orElse(null);
        if (product == null) {
            return null;
        }
        return service.getVendorName(product.getVehicle().getVendorCode());
    }

    private String getVendorPrice(List<RecomdProductRes> productList, String locale, String currencyCode, String vendorCode) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        if (StringUtils.isEmpty(vendorCode)) {
            return null;
        }
        Set<String> smallCodeSet = getVehicleGroupSet("small");
        if (CollectionUtils.isEmpty(smallCodeSet)) {
            return null;
        }
        BigDecimal price = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null
                && l.getVehicle() != null && StringUtils.isNotEmpty(l.getVehicle().getGroupCode()) && smallCodeSet.contains(l.getVehicle().getGroupCode())
                && StringUtils.equalsIgnoreCase(l.getVehicle().getVendorCode(), vendorCode)).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return service.currencyString(price, LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(locale), CurrencyEnum.getCurrrencyEnum(currencyCode));
    }

    private String getVehicleGroupPrice(List<RecomdProductRes> productList, String locale, String currencyCode, Set<String> vehicleGroupSet) {
        if (CollectionUtils.isEmpty(productList) || CollectionUtils.isEmpty(productList.get(0).getProducts())) {
            return null;
        }
        if (CollectionUtils.isEmpty(vehicleGroupSet)) {
            return null;
        }
        BigDecimal price = productList.get(0).getProducts().stream().filter(l -> l.getPrice() != null && l.getPrice().getCurrentDailyPrice() != null
                && l.getVehicle() != null && StringUtils.isNotEmpty(l.getVehicle().getGroupCode()) && vehicleGroupSet.contains(l.getVehicle().getGroupCode())).map(l -> l.getPrice().getCurrentDailyPrice()).min(BigDecimal::compareTo).orElse(null);
        if (price == null || price.compareTo(BigDecimal.ZERO) < 0) {
            return null;
        }
        return service.currencyString(price, LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(locale), CurrencyEnum.getCurrrencyEnum(currencyCode));
    }

    private Set<String> getVehicleGroupSet(String vehicleGroup) {
        return tripConfig.getVehicleGroupMappingList().stream().filter(l -> StringUtils.equalsIgnoreCase(vehicleGroup, l.getValue())).map(KeyValueDto::getKey).collect(Collectors.toSet());
    }


    private boolean checkRequest(QuerySeoFaqRequestType request) {
        if (request.getBaseRequest() == null) {
            return false;
        }
        if (Optional.ofNullable(request.getCityId()).orElse(0) <= 0
                && Optional.ofNullable(request.getCountryId()).orElse(0) <= 0
                && StringUtils.isEmpty(request.getPoiCode())) {
            return false;
        }
        if (StringUtils.isEmpty(request.getBaseRequest().getRequestId())) {
            request.getBaseRequest().setRequestId(UUID.randomUUID().toString());
        }
        return true;
    }
}
