package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.osdshopping;

import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.car.osd.basicdataservice.dto.DataSource;
import com.ctrip.car.osd.basicdataservice.dto.GetAirportsRequestType;
import com.ctrip.car.osd.basicdataservice.dto.GetAirportsResponseType;
import com.ctrip.car.osd.basicdataservice.methodtype.CarosdbasicdataserviceClient;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.model.QueryPoiDetailsRequestType;
import com.ctrip.model.QueryPoiDetailsResponseType;
import com.ctriposs.baiji.rpc.common.types.BaseRequest;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Component
public class BasicDataProxy {

    private final ILog logger = LogManager.getLogger(BasicDataProxy.class);

    private final CarosdbasicdataserviceClient basicDataClient = CarosdbasicdataserviceClient.getInstance();

    @NCache(holdMinute = 300, ignoreNull = false)
    public GetAirportsResponseType getAirport(String airportCode, String locale) {
        GetAirportsRequestType requestType = new GetAirportsRequestType();
        requestType.setBaseRequest(new BaseRequest());
        requestType.getBaseRequest().setLocale(locale);
        requestType.setAirportCode(airportCode);
        requestType.setDataSource(DataSource.OCH);
        try {
            logger.info("getAirport_req", JsonUtil.toString(requestType));
            GetAirportsResponseType responseType = basicDataClient.getAirports(requestType);
            logger.info("getAirport_res", JsonUtil.toString(responseType));
            return responseType;
        } catch (Exception e) {
            return null;
        }
    }

    public QueryPoiDetailsResponseType getTrainPoi(String trainCode, String locale) {
        QueryPoiDetailsRequestType queryPoiDetailsRequestType = new QueryPoiDetailsRequestType();
        queryPoiDetailsRequestType.setBaseRequest(new BaseRequest());
        queryPoiDetailsRequestType.getBaseRequest().setLocale(locale);
        queryPoiDetailsRequestType.setTrainCodes(Collections.singletonList(trainCode));
        try {
            logger.info("queryPoiDetails_req", JsonUtil.toString(queryPoiDetailsRequestType));
            QueryPoiDetailsResponseType responseType = basicDataClient.queryPoiDetails(queryPoiDetailsRequestType);
            logger.info("queryPoiDetails_res", JsonUtil.toString(responseType));
            return responseType;
        } catch (Exception e) {
            logger.error("getTrainPoi", e);
            return  new QueryPoiDetailsResponseType();
        }
    }
}
