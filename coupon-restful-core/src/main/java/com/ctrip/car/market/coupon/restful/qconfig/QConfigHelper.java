package com.ctrip.car.market.coupon.restful.qconfig;

import qunar.tc.qconfig.client.MapConfig;

import java.util.Map;

public class QConfigHelper {

    public static String getChineseTips(String key){
        return getQConfigString("chinesetips.properties",key);
    }

    private static Map<String,String> getQConfigMap(String filename){

        MapConfig mapConfig = MapConfig.get(filename);
        if (mapConfig == null){
            return  null;
        }
        return mapConfig.asMap();

    }

    private static String getQConfigString(String filename,String key){

        Map<String,String> qConfigMap = getQConfigMap(filename);

        if (qConfigMap ==null){
            return "";
        }

        return qConfigMap.get(key);
    }
}
