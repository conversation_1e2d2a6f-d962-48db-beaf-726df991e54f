package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.seo.GetTripHomepageDevInfoRequestType;
import com.ctrip.car.market.coupon.restful.contract.seo.GetTripHomepageDevInfoResponseType;
import com.ctrip.car.market.coupon.restful.contract.seo.PlatformAdvantage;
import com.ctrip.car.market.coupon.restful.contract.seo.HotVendor;
import com.ctrip.car.market.coupon.restful.dto.CustomerConfigDTO;
import com.ctrip.car.market.coupon.restful.dto.VendorLogoItem;
import com.ctrip.car.market.coupon.restful.pojo.PlatformAdvantageConfig;
import com.ctrip.car.market.coupon.restful.qconfig.TripConfig;
import com.ctrip.car.market.coupon.restful.qconfig.TripHomeDevInfoConfig;
import com.ctrip.car.market.coupon.restful.utils.CarMarketReadCache;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.ibu.platform.shark.sdk.api.Shark;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Component
public class SeoHomepageDevInfoBusiness {

    private final ILog logger = LogManager.getLogger(SeoHomepageDevInfoBusiness.class);

    @Resource
    TripHomeDevInfoConfig configs;

    @Resource
    private TripConfig tripConfig;

    //默认站点
    private static final String DEFAULT_LOCALE = "en-US";

    public GetTripHomepageDevInfoResponseType getTripHomepageDevInfo(GetTripHomepageDevInfoRequestType request) {
        return buildResponse(request);
    }

    public GetTripHomepageDevInfoResponseType buildResponse(GetTripHomepageDevInfoRequestType request) {
        String locale = request.getBaseRequest().getLocale();
        GetTripHomepageDevInfoResponseType response = new GetTripHomepageDevInfoResponseType();
        response.setHotVendors(getHotVendor(Optional.ofNullable(request.getCountryId()).orElse(0), Optional.ofNullable(request.getCityId()).orElse(0)));
        if (configs.getTripHomeDevInfo() == null || configs.getTripHomeDevInfo().getPlatformAdvantageConfigs() == null) {
            return response;
        }
        List<PlatformAdvantageConfig> platformAdvantageConfigs = new ArrayList<>(configs.getTripHomeDevInfo().getPlatformAdvantageConfigs());
        Optional<PlatformAdvantageConfig> platformAdvantageConfig = platformAdvantageConfigs.stream().filter(p -> Objects.equal(p.getLocale(), locale)).findFirst();
        if (!platformAdvantageConfig.isPresent()) {
            platformAdvantageConfig = platformAdvantageConfigs.stream().filter(p -> Objects.equal(p.getLocale(), DEFAULT_LOCALE)).findFirst();
        }
        //排序
        List<PlatformAdvantage> platformAdvantages = platformAdvantageConfig.get().getPlatformAdvantages();
        List<PlatformAdvantage> resList = new ArrayList<>();
        platformAdvantages = platformAdvantages.stream().sorted(Comparator.comparingInt(PlatformAdvantage::getSortNum)).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(platformAdvantages)) {
            for (PlatformAdvantage advantage : platformAdvantages) {
                PlatformAdvantage res = new PlatformAdvantage();
                res.setSortNum(advantage.getSortNum());
                res.setIcon(advantage.getIcon());
                res.setTitle(Shark.getByLocale(advantage.getTitle(), locale));
                res.setContent(Shark.getByLocale(advantage.getContent(), locale));
                resList.add(res);
            }
        }
        response.setPlatformAdvantage(resList);
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    public List<HotVendor> getHotVendor(Integer countryId, Integer cityId) {
        try {
            if (tripConfig.getSeoHotVendor() == null) {
                return Lists.newArrayList();
            }
            //优先读取city
            CustomerConfigDTO customerConfig = Optional.ofNullable(tripConfig.getSeoHotVendor().getCustomerConfig()).orElse(Lists.newArrayList()).stream().filter(l -> Objects.equal(l.getType(), 2) && Objects.equal(l.getId(), cityId)).findFirst().orElse(null);
            if (customerConfig == null) {
                //读取国家
                customerConfig = Optional.ofNullable(tripConfig.getSeoHotVendor().getCustomerConfig()).orElse(Lists.newArrayList()).stream().filter(l -> Objects.equal(l.getType(), 1) && Objects.equal(l.getId(), countryId)).findFirst().orElse(null);
            }
            String vendorStr = customerConfig == null ? tripConfig.getSeoHotVendor().getDefaultConfig() : customerConfig.getVendorList();
            List<String> vendorList = Lists.newArrayList(vendorStr.replace("，", ",").split(","));
            List<HotVendor> result = Lists.newArrayList();
            for (String l : vendorList) {
                String vendorLogo = getVendorLogo(l);
                if (StringUtils.isEmpty(vendorLogo)) {
                    continue;
                }
                HotVendor item = new HotVendor();
                item.setSortNum(result.size() + 1);
                item.setVendorName(l);
                item.setVendorPicLink(vendorLogo);
                result.add(item);
            }
            return result;
        } catch (Exception e) {
            logger.warn("getHotVendor", e);
            return Lists.newArrayList();
        }
    }

    private String getVendorLogo(String vendorCode) {
        VendorLogoItem logoItem = tripConfig.getVendorLogoList().stream().filter(li -> StringUtils.equalsIgnoreCase(li.getVendorCode(), vendorCode.trim())).findFirst().orElse(null);
        if (logoItem != null) {
            return logoItem.getLogo();
        }
        return CarMarketReadCache.get("car.market.seo.vendor.logo." + vendorCode.toLowerCase());
    }
}
