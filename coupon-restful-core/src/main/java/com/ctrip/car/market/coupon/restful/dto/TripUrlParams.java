package com.ctrip.car.market.coupon.restful.dto;


import java.math.BigDecimal;

public class TripUrlParams {

    private String vehicleKey;

    private BigDecimal price;

    private Integer channelId;

    private String locale;

    private String curr;

    private String cityName;

    private Integer sourceCountryId;

    private String priceVersion;

    public String getPriceVersion() {
        return priceVersion;
    }

    public void setPriceVersion(String priceVersion) {
        this.priceVersion = priceVersion;
    }

    public String getVehicleKey() {
        return vehicleKey;
    }

    public void setVehicleKey(String vehicleKey) {
        this.vehicleKey = vehicleKey;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getChannelId() {
        return channelId;
    }

    public void setChannelId(Integer channelId) {
        this.channelId = channelId;
    }

    public String getLocale() {
        return locale;
    }

    public void setLocale(String locale) {
        this.locale = locale;
    }

    public String getCurr() {
        return curr;
    }

    public void setCurr(String curr) {
        this.curr = curr;
    }

    public String getCityName() {
        return cityName;
    }

    public void setCityName(String cityName) {
        this.cityName = cityName;
    }

    public Integer getSourceCountryId() {
        return sourceCountryId;
    }

    public void setSourceCountryId(Integer sourceCountryId) {
        this.sourceCountryId = sourceCountryId;
    }
}
