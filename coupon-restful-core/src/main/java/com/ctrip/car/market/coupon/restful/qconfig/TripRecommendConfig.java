package com.ctrip.car.market.coupon.restful.qconfig;

import com.ctrip.car.market.coupon.restful.dto.TrainPointOfInterest;
import lombok.Getter;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class TripRecommendConfig {
    @QConfig("tripRecommend.properties")
    public void onChange(Map<String, String> map) {
        this.tripAbExpCodeApp = map.getOrDefault("trip_ab_expCode_app", "");
        this.tripAbExpCodeH5 = map.getOrDefault("trip_ab_expCode_h5", "");
        this.tripAbExpCodePc = map.getOrDefault("trip_ab_expCode_pc", "");
        this.trainPriorityLabelId = map.getOrDefault("train_priority_label_id", "3862");
        this.trainFamilyPeopleNum = Integer.valueOf(map.getOrDefault("train_family_people_num", "5"));
        this.trainFamilyLuggageNum = Integer.valueOf(map.getOrDefault("train_family_luggage_num", "2"));
        this.queryRentalCarNewGuestLabelId = map.getOrDefault("query_rental_car_new_guest_label_id", "300046");
        this.freeCancelLabelCode = map.getOrDefault("store_service_free_cancel_label_code", "3563");
        this.tripPcListUrl = map.getOrDefault("tripPcListUrl", "");
        this.tripAppListUrl = map.getOrDefault("tripAppListUrl", "");
        this.discountLabel = map.getOrDefault("store_service_discount_label_code", "4145,4138,4131,4124,4117,4110");
        this.onlyHasPriority = map.getOrDefault("onlyHasPriority", "18");
        this.hasPriorityAndNew = map.getOrDefault("hasPriorityAndNew", "25");
        this.blankList = Arrays.asList(map.getOrDefault("blankList", "A,C,D").split(","));
        this.trainPrivilege = Integer.parseInt(map.getOrDefault("trainPrivilege", "1"));
        this.newGuest = Integer.parseInt(map.getOrDefault("newGuest", "2"));
        this.europeCity = Integer.parseInt(map.getOrDefault("europeCity", "3"));
        this.defaultCode = Integer.parseInt(map.getOrDefault("defaultCode", "4"));
    }

    @QConfig("trainPointOfInterest.json")
    private List<TrainPointOfInterest> trainPointOfInterests;

    public TrainPointOfInterest findTrainPointOfInterestByPriority(Integer id) {
        if (CollectionUtils.isEmpty(this.trainPointOfInterests)) {
            return null;
        }
        return trainPointOfInterests.stream().filter(x -> Objects.equals(id, x.getPriority())).findFirst().orElse(null);
    }

    private String tripAbExpCodeApp;
    private String tripAbExpCodeH5;
    private String tripAbExpCodePc;
    @Getter
    private String trainPriorityLabelId;
    @Getter
    private Integer trainFamilyPeopleNum;
    @Getter
    private Integer trainFamilyLuggageNum;
    @Getter
    private String queryRentalCarNewGuestLabelId;
    @Getter
    private String freeCancelLabelCode;
    @Getter
    private String tripPcListUrl;
    @Getter
    private String tripAppListUrl;

    private String discountLabel;

    @Getter
    private String hasPriorityAndNew;

    @Getter
    private String onlyHasPriority;
    @Getter
    private List<String> blankList;
    @Getter
    private int trainPrivilege;
    @Getter
    private int newGuest;
    @Getter
    private int europeCity;
    @Getter
    private int defaultCode;

    public String getAbTestExpCode(String sourceFrom) {
        if ("OSD_T_APP".equalsIgnoreCase(sourceFrom)) {
            return tripAbExpCodeApp;
        } else if ("OSD_T_ONLINE".equalsIgnoreCase(sourceFrom)) {
            return tripAbExpCodePc;
        } else if ("OSD_T_H5".equalsIgnoreCase(sourceFrom)) {
            return tripAbExpCodeH5;
        }
        return null;
    }

    public List<String> getDiscountLabels() {
        if (StringUtils.isEmpty(discountLabel)) {
            return null;
        }
        return Arrays.stream(discountLabel.split(",")).collect(Collectors.toList());
    }

}
