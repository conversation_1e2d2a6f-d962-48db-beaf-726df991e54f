package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.cache.LabelCache;
import com.ctrip.car.market.coupon.restful.contract.*;
import com.ctrip.car.market.coupon.restful.dto.LabelDto;
import com.ctrip.car.market.coupon.restful.dto.QualityServicesDto;
import com.ctrip.car.market.coupon.restful.enums.ResultOpenStatus;
import com.ctrip.car.market.coupon.restful.utils.ResponseCodeUtils;
import com.ctrip.car.market.job.common.entity.CpnLabelDO;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import qunar.tc.qconfig.client.spring.QConfig;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class QueryQualityServicesInfoBusiness {
    private final ILog logger = LogManager.getLogger(this.getClass());

    @QConfig("QualityServicesInfo.json")
    private QualityServicesDto configs;

    @QConfig("QualityServicesInfoNew.json")
    private QualityServicesDto configNew;

    @Resource
    private LabelCache labelCache;

    public QueryQualityServicesInfoResponseType queryQualityServicesInfo(QueryQualityServicesInfoRequestType request) {
        QueryQualityServicesInfoResponseType response = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, QueryQualityServicesInfoResponseType.class);
        try {
            QualityServicesInfo qualityServicesInfo = new QualityServicesInfo();
            BeanUtils.copyProperties(configs, qualityServicesInfo);
            List<EasyLifeLabel> easyLifeLabels = findLabelList(0);
            qualityServicesInfo.setEasyLifeLabels(easyLifeLabels);
            response.setQualityServicesInfo(qualityServicesInfo);
        } catch (Exception e) {
            logger.error("QueryQualityServicesInfoBusiness.queryQualityServicesInfo", e);
            return ResponseCodeUtils.res(500, e.getMessage(), QueryQualityServicesInfoResponseType.class);
        }
        return response;
    }

    public QueryLabelListResponseType queryLabelList(QueryLabelListRequestType request) {
        QueryLabelListResponseType response = ResponseCodeUtils.res(ResultOpenStatus.SUCCESS, QueryLabelListResponseType.class);
        try {
            List<EasyLifeLabel> labelList = findLabelList(request.getPType());
            response.setLabelList(labelList);
        } catch (SQLException e) {
            logger.error("QueryQualityServicesInfoBusiness.queryLabelList");
            return ResponseCodeUtils.res(500, e.getMessage(), QueryLabelListResponseType.class);
        }
        return response;
    }


 /*   @NCache(holdMinute = 30, log = true, reHoldMinute = 20)
    public List<EasyLifeLabel> findLabelList() throws SQLException {
        List<EasyLifeLabel> easyLifeLabels = new ArrayList<>();
        List<LabelDto> labels = config.getLabels();
        for (LabelDto dto : labels) {
            CpnLabel sample = new CpnLabel();
            sample.setCode(dto.getLabelId());
            sample.setIsActive(true);
            List<CpnLabel> cpnLabelList = cpnLabelDao.queryBy(sample);
            if (!CollectionUtils.isEmpty(cpnLabelList)) {
                CpnLabel cpnLabel = cpnLabelList.get(0);
                EasyLifeLabel easyLifeLabel = getEasyLifeLabel(cpnLabel, dto);
                easyLifeLabel.setIsShow(cpnLabel.getIsDisplay());
                easyLifeLabels.add(easyLifeLabel);
            }
        }
        easyLifeLabels.sort(Comparator.comparing(EasyLifeLabel::getSortIndex));
        return easyLifeLabels;
    }*/

    public List<EasyLifeLabel> findLabelList(Integer pType) throws SQLException {
        List<EasyLifeLabel> easyLifeLabels = new ArrayList<>();
        List<LabelDto> labels = Optional.ofNullable(pType).orElse(0).equals(1) ? configNew.getLabels() : configs.getLabels();
        for (LabelDto dto : labels) {
            CpnLabelDO cpnLabel = getLabel(dto.getLabelId());
            if (cpnLabel != null && BooleanUtils.toBoolean(cpnLabel.getIsActive())) {
                EasyLifeLabel easyLifeLabel = getEasyLifeLabel(cpnLabel, dto);
                easyLifeLabel.setIsShow(cpnLabel.getIsDisplay());
                easyLifeLabels.add(easyLifeLabel);
            }
        }
        easyLifeLabels.sort(Comparator.comparing(EasyLifeLabel::getSortIndex));
        return easyLifeLabels;
    }

    private EasyLifeLabel getEasyLifeLabel(CpnLabelDO cpnLabel, LabelDto labelDto) {
        EasyLifeLabel easyLifeLabel = new EasyLifeLabel();
        easyLifeLabel.setTitle(cpnLabel.getName());
        easyLifeLabel.setSubTitle(cpnLabel.getDescription());
        easyLifeLabel.setDesc(cpnLabel.getExtraDescription());
        easyLifeLabel.setSortIndex(cpnLabel.getSort());
        easyLifeLabel.setIcon(labelDto.getIcon());
        easyLifeLabel.setIconDesc(labelDto.getIconDesc());
        List<LabelImage> descImages = new ArrayList<>();
        if (!CollectionUtils.isEmpty(labelDto.getDescImages())) {
            descImages = labelDto.getDescImages().stream().map(x -> {
                LabelImage labelImage = new LabelImage();
                BeanUtils.copyProperties(x, labelImage);
                return labelImage;
            }).collect(Collectors.toList());
        }
        easyLifeLabel.setDescImages(descImages);
        return easyLifeLabel;
    }

    /**
     * 获取标签缓存
     */
    public CpnLabelDO getLabel(Long code) {
        try {
            return labelCache.getLabel(code);
        } catch (Exception e) {
            logger.error("getLabels", e);
            return null;
        }
    }
}
