package com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.poi;

import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.igt.RequestHeader;
import com.ctrip.igt.geo.interfaces.OchGeoServiceClient;
import com.ctrip.igt.geo.interfaces.dto.PlaceDetailsDTO;
import com.ctrip.igt.geo.interfaces.message.QueryPlaceDetailsRequestType;
import com.ctrip.igt.geo.interfaces.message.QueryPlaceDetailsResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

@Component
public class GeoProxy {

    private final ILog log = LogManager.getLogger(GeoProxy.class);

    private final OchGeoServiceClient client = OchGeoServiceClient.getInstance();

    public PlaceDetailsDTO getPoiDetail(String poiCode, String locale) {
        QueryPlaceDetailsRequestType requestType = new QueryPlaceDetailsRequestType();
        requestType.setRequestHeader(new RequestHeader());
        requestType.getRequestHeader().setLocale(locale);
        requestType.setCarPlaceIds(Lists.newArrayList(poiCode));
        try {
            QueryPlaceDetailsResponseType responseType = client.queryPlaceDetails(requestType);
            if (CollectionUtils.isNotEmpty(responseType.getPlaceDetails())) {
                return responseType.getPlaceDetails().get(0);
            }
            return null;
        } catch (Exception e) {
            log.warn("getPoiDetail", e);
            return null;
        }
    }
}
