package com.ctrip.car.market.coupon.restful.qconfig;

import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Map;

@Component
public class QunarCouponConfig {

    @QConfig("qunarCoupon.properties")
    public void onChange(Map<String, String> map) {
        this.serviceUrl = map.getOrDefault("serviceUrl", "http://carinner.qunar.com/osdmobileservice/getUserInfo");
        this.sCookieExpire = Long.parseLong(map.getOrDefault("sCookieExpire", "1800"));
    }

    private String serviceUrl;

    private long sCookieExpire;

    public String getServiceUrl() {
        return serviceUrl;
    }

    public long getsCookieExpire() {
        return sCookieExpire;
    }
}
