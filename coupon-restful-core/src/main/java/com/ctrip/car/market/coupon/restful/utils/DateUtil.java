package com.ctrip.car.market.coupon.restful.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Optional;

public class DateUtil {
    public static final String OSD_DEFAULT_FORMAT_SLASH = "yyyyMMddHHmmss";

    public static String toYYYY_MM_DDHHMM(Calendar calendar) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy/MM/dd HH:mm");
        return format.format(calendar.getTime());
    }

    public static String toYYYY_MM_DD(Calendar calendar) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.format(calendar.getTime());
    }

    public static int getDifference(Calendar startDate, Calendar endDate) {
        if (startDate == null || endDate == null) {
            return 0;
        }
        long days = (endDate.getTimeInMillis() - startDate.getTimeInMillis()) / (1000 * 3600 * 24);
        return (int) Math.ceil(days);
    }

    public static String toYYYY_MM_DD_HH_MM(Calendar calendar) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        return format.format(calendar.getTime());
    }

    public static String toYYYYMMDDHHmMss(Calendar calendar ) {
        SimpleDateFormat format = new SimpleDateFormat(OSD_DEFAULT_FORMAT_SLASH);
        return format.format(calendar.getTime());
    }

    public static Calendar roundToNextHalfHour(Calendar calendar, int m) {
        Calendar cal = Optional.ofNullable(calendar).map(t -> (Calendar) t.clone()).orElseGet(Calendar::getInstance);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.MINUTE, m);
        int minute = cal.get(Calendar.MINUTE);
        if (minute % 30 != 0) {
            cal.add(Calendar.MINUTE, 30 - (minute % 30));
        }
        return cal;
    }

    public static Calendar roundToBeforeHalfHour(Calendar calendar, int hour) {
        Calendar cal = Optional.ofNullable(calendar).map(t -> (Calendar) t.clone()).orElseGet(Calendar::getInstance);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        cal.add(Calendar.HOUR_OF_DAY, hour);
        int minute = cal.get(Calendar.MINUTE);
        if (minute % 30 != 0) {
            cal.add(Calendar.MINUTE, - (minute % 30));
        }
        return cal;
    }

    public static Calendar setTenHour(Calendar calendar) {
        Calendar cal = Optional.ofNullable(calendar).map(t -> (Calendar) t.clone()).orElseGet(Calendar::getInstance);
        cal.set(Calendar.HOUR_OF_DAY, 10);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal;
    }

    public static Calendar addSevenDay(Calendar calendar) {
        Calendar cal = Optional.ofNullable(calendar).map(t -> (Calendar) t.clone()).orElseGet(Calendar::getInstance);
        cal.add(Calendar.DAY_OF_YEAR, 7);
        return cal;
    }
}
