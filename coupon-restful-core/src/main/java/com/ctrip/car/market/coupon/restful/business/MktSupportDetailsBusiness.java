package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.coupon.restful.contract.MktMainSupportInfo;
import com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryMktSupportDetailsResponseType;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.mkt.MktSupportProxy;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class MktSupportDetailsBusiness {
    @Autowired
    private MktSupportProxy mktSupportProxy;

    public QueryMktSupportDetailsResponseType getMktSupportDetails(QueryMktSupportDetailsRequestType request) {
        QueryMktSupportDetailsResponseType response = new QueryMktSupportDetailsResponseType();
        response.setBaseResponse(ResponseUtil.success());
        if (!valid(request, response)) {
            return response;
        }
        String uid = BaseUtils.getUidByCommon(request.getHead());
        if (StringUtils.isEmpty(uid)) {
            response.setBaseResponse(ResponseUtil.fail("no login"));
            return response;
        }
        com.ctrip.car.market.client.contract.QueryMktSupportDetailsRequestType requestType = this.convertRequest(request, uid);
        com.ctrip.car.market.client.contract.QueryMktSupportDetailsResponseType mktSupportDetails = mktSupportProxy.getMktSupportDetails(requestType);
        if (mktSupportDetails != null) {
            response.setBottomButtonText(mktSupportDetails.getBottomButtonText());
            response.setTaskStatus(mktSupportDetails.getTaskStatus());
            response.setCashbackStatus(mktSupportDetails.getCashbackStatus());
            response.setProjectId(request.getProjectId());
            response.setTaskId(request.getTaskId());
            response.setMktMainSupportInfos(mktSupportDetails.getMktMainSupportInfos() != null ? mktSupportDetails.getMktMainSupportInfos().stream().map(this::convertSupport).toList() : null);
            response.setUrl(mktSupportDetails.getUrl());
        }
        return response;
    }
    private boolean valid(QueryMktSupportDetailsRequestType request, QueryMktSupportDetailsResponseType response) {
        if (request.getTaskId() == null || request.getProjectId() == null) {
            response.setBaseResponse(ResponseUtil.fail("TaskId and ProjectId cannot be null"));
            return false;
        }
        return true;
    }

    private com.ctrip.car.market.client.contract.QueryMktSupportDetailsRequestType convertRequest(QueryMktSupportDetailsRequestType request, String uid) {
        com.ctrip.car.market.client.contract.QueryMktSupportDetailsRequestType convertedRequest = new com.ctrip.car.market.client.contract.QueryMktSupportDetailsRequestType();
        convertedRequest.setUid(uid);
        convertedRequest.setProjectId(request.getProjectId());
        convertedRequest.setTaskId(request.getTaskId());
        return convertedRequest;
    }

    private MktMainSupportInfo convertSupport(com.ctrip.car.market.client.contract.MktMainSupportInfo mktMainSupportInfo) {
        MktMainSupportInfo convertedSupport = new MktMainSupportInfo();
        convertedSupport.setSupportTime(mktMainSupportInfo.getAssistTime());
        convertedSupport.setCashbackStatus(mktMainSupportInfo.getCashbackStatus());
        convertedSupport.setAvatarPictureURL(mktMainSupportInfo.getAvatarPictureURL());
        convertedSupport.setBeInvitedUid(mktMainSupportInfo.getBeInvitedUid());
        convertedSupport.setCurrentProcess(mktMainSupportInfo.getCurrentProcess());
        convertedSupport.setCashbackMoney(mktMainSupportInfo.getCashbackMoney());
        convertedSupport.setCashbackRatio(mktMainSupportInfo.getCashbackRatio());
        convertedSupport.setNickname(mktMainSupportInfo.getNickname());
        convertedSupport.setEventTarget(mktMainSupportInfo.getEventTarget());
        convertedSupport.setCashbackTime(mktMainSupportInfo.getExpectedCashbackTime());
        convertedSupport.setUserId(mktMainSupportInfo.getUserId());
        return convertedSupport;
    }

}
