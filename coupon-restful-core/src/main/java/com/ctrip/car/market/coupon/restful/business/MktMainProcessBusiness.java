package com.ctrip.car.market.coupon.restful.business;

import com.ctrip.car.market.client.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.contract.MktMainProcessInfo;
import com.ctrip.car.market.coupon.restful.contract.QueryMktMainProcessInfoRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryMktMainProcessInfoResponseType;
import com.ctrip.car.market.coupon.restful.proxy.com.ctrip.soa.mkt.MktSupportProxy;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.ResponseUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MktMainProcessBusiness {

    @Resource
    private MktSupportProxy mktSupportProxy;

    public QueryMktMainProcessInfoResponseType queryMktMainProcessInfo(QueryMktMainProcessInfoRequestType request) {
        QueryMktMainProcessInfoResponseType response = new QueryMktMainProcessInfoResponseType();
        response.setMktMainProcessInfo(new MktMainProcessInfo());
        response.getMktMainProcessInfo().setShowPopup(false);
        String uid = BaseUtils.getUidByCommon(request.getHead());
        if (StringUtils.isEmpty(uid)) {
            response.setBaseResponse(ResponseUtil.fail("no login"));
            return response;
        }
        if (request.getScenarioCode() == null) {
            response.setBaseResponse(ResponseUtil.fail("no scenario code"));
            return response;
        }
        com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType soaResponse = mktSupportProxy.getMktMainProcessInfo(this.reqConvert(request,uid));
        response.setMktMainProcessInfo(this.resConvert(soaResponse));
        response.setBaseResponse(ResponseUtil.success());
        return response;
    }

    private com.ctrip.car.market.client.contract.QueryMktMainProcessInfoRequestType reqConvert(QueryMktMainProcessInfoRequestType request, String uid) {
        com.ctrip.car.market.client.contract.QueryMktMainProcessInfoRequestType soaReq = new com.ctrip.car.market.client.contract.QueryMktMainProcessInfoRequestType();
        soaReq.setBaseRequest(new BaseRequest());
        soaReq.getBaseRequest().setUid(uid);
        soaReq.getBaseRequest().setCid(getCid(request));
        soaReq.setScenarioCode(request.getScenarioCode());
        return soaReq;
    }

    private String getCid(QueryMktMainProcessInfoRequestType request) {
        if (request.getHead() != null && StringUtils.isNotEmpty(request.getHead().getCid())) {
            return request.getHead().getCid();
        }
        return request.getCid();
    }

    private MktMainProcessInfo resConvert(com.ctrip.car.market.client.contract.QueryMktMainProcessInfoResponseType soaResponse) {
        MktMainProcessInfo mktMainProcessInfo = new MktMainProcessInfo();
        mktMainProcessInfo.setShowPopup(false);
        if(soaResponse == null || soaResponse.getMktMainProcessInfo() == null) {
            return mktMainProcessInfo;
        }
        mktMainProcessInfo.setShowPopup(BooleanUtils.isTrue(soaResponse.getMktMainProcessInfo().isShowPopup()));
        mktMainProcessInfo.setUrl(soaResponse.getMktMainProcessInfo().getUrl());
        mktMainProcessInfo.setImageUrl(soaResponse.getMktMainProcessInfo().getImageUrl());
        return mktMainProcessInfo;
    }
}
