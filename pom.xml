<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.ctrip</groupId>
        <artifactId>super-pom</artifactId>
        <version>1.0.7</version>
    </parent>
    <groupId>com.ctrip.car.market</groupId>
    <artifactId>coupon-restful</artifactId>
    <packaging>pom</packaging>
    <version>0.0.2</version>
    <modules>
        <module>coupon-restful-service</module>
        <module>coupon-restful-core</module>
    </modules>

    <properties>

        <java.version>21</java.version>
        <!--framework bom-->
        <bom.version>8.34.0</bom.version>
        <!--各模块公共版本-->
        <module.version>0.0.7</module.version>
        <!-- Maven Release Repository URL -->
        <releases.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/carrelease
        </releases.repo>
        <!-- Maven Snapshot Repository URL -->
        <snapshots.repo>http://maven.release.ctripcorp.com/nexus/content/repositories/carsnapshot
        </snapshots.repo>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <springBoot.version>1.5.6.RELEASE</springBoot.version>
        <coupon-client>3.2.19</coupon-client>
        <mergecoupon-client>1.4.34</mergecoupon-client>
        <promocode.version>2.3.12</promocode.version>
        <mockito-all.version>1.10.19</mockito-all.version>
        <log4j2.version>2.17.1</log4j2.version>
        <org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
        <geo.version>1.3.6</geo.version>
        <basicdata.version>0.0.44</basicdata.version>
        <translate.version>1.1.2</translate.version>
        <common.translation>1.2.1</common.translation>
        <okhttp.version>3.14.9</okhttp.version>
        <ucp.version>0.5.37</ucp.version>
        <geolocation.version>1.1.40</geolocation.version>
        <seo.platform.version>0.1.68</seo.platform.version>

        <carcache.version>1.0.24</carcache.version>
        <market.job.version>1.0.11</market.job.version>
        <caffeine.version>2.6.2</caffeine.version>
        <geo.service.version>1.4.25</geo.service.version>
        <ckafka.version>0.2.0</ckafka.version>
        <lombok.version>1.18.32</lombok.version>
        <jmockit.version>1.52.0-jdk21</jmockit.version>
        <jacoco.version>0.8.11</jacoco.version>
        <powermock.version>1.7.4</powermock.version>
        <sonar.jacoco.reportPaths>${project.basedir}/code-coverage/jacoco.exec</sonar.jacoco.reportPaths>
        <maven.compiler.source>21</maven.compiler.source>
        <maven.compiler.target>21</maven.compiler.target>
        <carcommodity.common.version>0.0.73</carcommodity.common.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>5.11.0</version>
            <scope>test</scope>
        </dependency>
        <!-- spock 核心包 -->
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>2.3-groovy-4.0</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>groovy-json</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-macro</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-nio</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-sql</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-templates</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-test</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-xml</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.groovy</groupId>
                    <artifactId>groovy</artifactId>
                </exclusion>
            </exclusions>

        </dependency>
        <!-- groovy -->
        <dependency>
            <groupId>org.apache.groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>4.0.19</version>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>${okhttp.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>${mockito-all.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.objenesis</groupId>
                    <artifactId>objenesis</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>net.minidev</groupId>
                    <artifactId>json-smart</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.di.data</groupId>
            <artifactId>abtestclient</artifactId>
            <version>4.6.6</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okio</groupId>
                    <artifactId>okio</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.jmockit</groupId>
                <artifactId>jmockit</artifactId>
                <version>${jmockit.version}</version>
            </dependency>
            <dependency>
                <groupId>javax.annotation</groupId>
                <artifactId>javax.annotation-api</artifactId>
                <version>1.3.2</version>
            </dependency>
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <scope>runtime</scope>
            </dependency>
            <dependency>
                <groupId>org.jacoco</groupId>
                <artifactId>org.jacoco.agent</artifactId>
                <classifier>runtime</classifier>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.ckafka</groupId>
                <artifactId>client</artifactId>
                <version>${ckafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework.ckafka</groupId>
                <artifactId>codec</artifactId>
                <version>${ckafka.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.25057</groupId>
                <artifactId>carcommoditycommonservice</artifactId>
                <version>${carcommodity.common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.tour.ai</groupId>
                <artifactId>userlabelservice</artifactId>
                <version>1.0.4</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.igt.geo</groupId>
                <artifactId>geo-service-client</artifactId>
                <version>${geo.service.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.car.sd.carosdshoppingserviceapi.v1</groupId>
                <artifactId>carosdshoppingserviceapi</artifactId>
                <version>0.0.119</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.21394</groupId>
                <artifactId>seo-platform-manager</artifactId>
                <version>${seo.platform.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.platform.members.geolocation</groupId>
                <artifactId>geolocationservice</artifactId>
                <version>${geolocation.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.tour.tripservice</groupId>
                <artifactId>ucp-biz-systemservice-client</artifactId>
                <version>${ucp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car</groupId>
                <artifactId>maiar-client</artifactId>
                <version>1.1.0</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.sd</groupId>
                <artifactId>carcache-all</artifactId>
                <version>${carcache.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>cjetcache-qschedule</artifactId>
                        <groupId>com.ctrip.car.sd</groupId>
                    </exclusion>
                    <exclusion>
                        <groupId>commons-logging</groupId>
                        <artifactId>commons-logging</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>car-market-job-common</artifactId>
                <version>${market.job.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>${caffeine.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.corp.foundation</groupId>
                <artifactId>common-translation</artifactId>
                <version>${common.translation}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.osd.cartranslateservice</groupId>
                <artifactId>cartranslateservice</artifactId>
                <version>${translate.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.dcs.geo</groupId>
                <artifactId>geo-platform-sdk</artifactId>
                <version>${geo.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.codehaus.groovy</groupId>
                        <artifactId>groovy-all</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>com.ctrip.dcs.common</groupId>
                        <artifactId>dcs-common-component-common-sdk</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.ctrip.soa.car.osd.carosdbasicdataservice.v1</groupId>
                <artifactId>carosdbasicdataservice</artifactId>
                <version>${basicdata.version}</version>
            </dependency>

            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>coupon-restful-contract</artifactId>
                <version>0.1.37</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>coupon-restful-core</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>coupon-restful-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.isd.restful</groupId>
                <artifactId>isd-restful-client</artifactId>
                <version>1.0.9</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>common</artifactId>
                <version>1.2.9</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.framework</groupId>
                <artifactId>framework-bom</artifactId>
                <version>${bom.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.ctrip.car</groupId>-->
<!--                <artifactId>coupon-client</artifactId>-->
<!--                <version>${coupon-client}</version>-->
<!--            </dependency>-->
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>coupon-contract</artifactId>
                <version>4.0.29</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${springBoot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter</artifactId>
                <version>${springBoot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-web</artifactId>
                <version>${springBoot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-thymeleaf</artifactId>
                <version>${springBoot.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-aop</artifactId>
                <version>${springBoot.version}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>mergecouponservice-client</artifactId>
                <version>${mergecoupon-client}</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.basebiz</groupId>
                <artifactId>basebiz-bom</artifactId>
                <version>1.1.4</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.ctrip.market.v1</groupId>
                <artifactId>promocode-client-contract</artifactId>
                <version>${promocode.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>${springBoot.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mockito</groupId>
                        <artifactId>mockito-core</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.ctrip.soa.car.sd.api8961.v1</groupId>
                <artifactId>api8961</artifactId>
                <version>0.6.64</version>
            </dependency>


            <dependency>
                <groupId>com.ctrip.car.sd</groupId>
                <artifactId>dynamic-tp-starter</artifactId>
                <version>1.1.7</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.credis</groupId>
                <artifactId>credis</artifactId>
                <version>4.3.69</version>
            </dependency>
            <dependency>
                <groupId>com.ctrip.car.market</groupId>
                <artifactId>market-crossrecommend-contract</artifactId>
                <version>1.0.12</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>snapshots</id>
            <url>${snapshots.repo}</url>
        </snapshotRepository>
        <repository>
            <id>releases</id>
            <url>${releases.repo}</url>
        </repository>
    </distributionManagement>

    <build>
        <plugins>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>${jacoco.version}</version>
                <executions>
                    <execution>
                        <id>prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>post-unit-test</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.codehaus.gmavenplus</groupId>
                <artifactId>gmavenplus-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>compile</goal>
                            <goal>compileTests</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>1.2.0.Final</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                    <forceJavacCompilerUse>true</forceJavacCompilerUse>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.1</version>
                <configuration>
                    <forkCount>8</forkCount>
                    <reuseForks>true</reuseForks>
                    <argLine>-Xmx2048m</argLine>
                    <argLine>
                        @{argLine}
                        -javaagent:"${settings.localRepository}"/org/jmockit/jmockit/${jmockit.version}/jmockit-${jmockit.version}.jar
                        --add-opens java.base/java.text=ALL-UNNAMED
                        --add-opens java.base/java.lang=ALL-UNNAMED
                        --add-opens java.base/java.lang.reflect=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED
                        --add-opens java.base/java.math=ALL-UNNAMED
                        --add-opens java.base/java.util=ALL-UNNAMED
                        --add-opens java.base/sun.util.calendar=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent=ALL-UNNAMED
                        --add-opens java.base/java.util.concurrent.atomic=ALL-UNNAMED
                        --add-opens java.base/java.io=ALL-UNNAMED
                        --add-opens java.base/java.net=ALL-UNNAMED
                        --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED
                        --add-opens java.management/sun.management=ALL-UNNAMED
                        --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED
                        --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.vm.annotation=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.access=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.util.random=ALL-UNNAMED
                        --add-opens java.base/jdk.internal.misc=ALL-UNNAMED
                        --add-opens jdk.jfr/jdk.jfr.internal.tool=ALL-UNNAMED
                    </argLine>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.ctrip.ibu.platform</groupId>
                <artifactId>shark-maven-plugin</artifactId>
                <version>1.1.1</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>pack-download</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
