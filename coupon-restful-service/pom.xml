<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>coupon-restful</artifactId>
        <groupId>com.ctrip.car.market</groupId>
        <version>0.0.2</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>war</packaging>
    <artifactId>coupon-restful-service</artifactId>
    <!--<properties>-->
    <!--<sonar.coverage.exclusions>com/ctrip/car/market/caractivitysystemservice/contract/**</sonar.coverage.exclusions>-->
    <!--</properties>-->
    <dependencies>
        <dependency>
            <groupId>com.ctrip.car</groupId>
            <artifactId>maiar-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>coupon-restful-contract</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>accounts-mobile-request-filter</artifactId>
                    <groupId>com.ctrip.basebiz</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.jacoco</groupId>
                    <artifactId>org.jacoco.agent</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>net.minidev</groupId>
                    <artifactId>json-smart</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.objenesis</groupId>
                    <artifactId>objenesis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>coupon-restful-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>accounts-mobile-request-filter</artifactId>
                    <groupId>com.ctrip.basebiz</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>logback-classic</artifactId>
                    <groupId>ch.qos.logback</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>groovy-all</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>common</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>groovy</artifactId>
                    <groupId>org.codehaus.groovy</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>-->
        <!--<groupId>org.springframework.boot</groupId>-->
        <!--<artifactId>spring-boot-starter-web</artifactId>-->
        <!--</dependency>-->

        <!--<dependency>-->
        <!--<groupId>org.springframework</groupId>-->
        <!--<artifactId>spring-webmvc</artifactId>-->
        <!--</dependency>-->


        <dependency>
            <groupId>com.ctrip.credis</groupId>
            <artifactId>credis</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>


        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.0.1</version>
            <!--  <scope>provided</scope>-->
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>framework-related</artifactId>
            <version>1.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-server</artifactId>
        </dependency>

        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-extensions</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctriposs.baiji</groupId>
            <artifactId>baiji-rpc-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.framework.apollo</groupId>
            <artifactId>apollo-client</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.ctrip.car</groupId>-->
        <!--            <artifactId>coupon-client</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>coupon-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>mergecouponservice-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>soa-common</artifactId>
                    <groupId>com.ctrip.igt.framework</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.basebiz</groupId>
            <artifactId>accounts-mobile-request-filter</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.ctrip.market.v1</groupId>
            <artifactId>promocode-client-contract</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jmockit</groupId>
            <artifactId>jmockit</artifactId>
            <version>1.38</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.ctrip.soa.car.sd.api8961.v1</groupId>
            <artifactId>api8961</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>accounts-mobile-request-filter</artifactId>
                    <groupId>com.ctrip.basebiz</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>coupon-restful-utils</artifactId>
            <version>0.0.7</version>
        </dependency>


        <dependency>
            <groupId>com.ctrip.car.sd</groupId>
            <artifactId>dynamic-tp-starter</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>transmittable-thread-local</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.ctrip.car.market</groupId>
            <artifactId>market-crossrecommend-contract</artifactId>
        </dependency>
    </dependencies>
</project>
