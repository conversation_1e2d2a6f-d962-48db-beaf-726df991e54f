JAVA_OPTS="$JAVA_OPTS -XX:-UseZGC -XX:+UseG1GC"

version=$(java -version 2>&1 | awk -F[\"_] 'NR==1{print $2}' | cut -d '.' -f 1)

if [[ $version == "21" ]]; then
    JAVA_OPTS="${JAVA_OPTS} \
        --add-opens java.base/java.lang=ALL-UNNAMED \
        --add-opens java.base/java.lang.reflect=ALL-UNNAMED \
        --add-opens java.base/sun.reflect.annotation=ALL-UNNAMED \
        --add-opens java.base/java.math=ALL-UNNAMED \
        --add-opens java.base/java.util=ALL-UNNAMED \
        --add-opens java.base/sun.util.calendar=ALL-UNNAMED \
        --add-opens java.base/java.io=ALL-UNNAMED \
        --add-opens java.base/java.net=ALL-UNNAMED \
        --add-opens java.base/java.nio=ALL-UNNAMED \
        --add-opens java.xml/com.sun.org.apache.xerces.internal.jaxp.datatype=ALL-UNNAMED \
        --add-opens java.management/sun.management=ALL-UNNAMED \
        --add-opens java.base/sun.reflect.generics.reflectiveObjects=ALL-UNNAMED \
        --add-opens jdk.compiler/com.sun.tools.javac.processing=ALL-UNNAMED"
fi