package com.ctrip.car.market.coupon.restful;

import com.ctrip.basebiz.accounts.mobile.request.filter.AccountsMobileRequestFilter;
import com.ctrip.car.market.common.log.TigerLogProxy;
import com.ctriposs.baiji.rpc.extensions.springboot.BaijiRegistrationBean;
import com.ctriposs.baiji.rpc.server.BaijiListener;
import com.ctriposs.baiji.rpc.server.HostConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
//@Order(Ordered.HIGHEST_PRECEDENCE)
public class BaijiServletConfiguration extends BaijiRegistrationBean {

    protected BaijiServletConfiguration() {
        super("/api/*", new WraperBaijiListener(CarMarketingCouponRestfulImpl.class));
    }

    private static class WraperBaijiListener extends BaijiListener {
        public WraperBaijiListener(Class<?>... serviceClasses) {
            super(serviceClasses);
        }

        @Override
        protected void configure(HostConfig hostConfig) {
            super.configure(hostConfig);
            AccountsMobileRequestFilter.registerFilter(hostConfig);
         //   MobileRequestFilter.registerFilter(hostConfig);
        }
    }


    private static final String LOG_FORMAT = "json";
    private static final String APPID = "*********";
    private static final String BU = "car";
    private static final String SERVICE_NAME = "CarMarketingCouponRestful";

    @Bean
    public TigerLogProxy getTigerLogProxy() {
        TigerLogProxy result = new TigerLogProxy(LOG_FORMAT, APPID, BU, SERVICE_NAME);
        return result;
    }

    //@Bean
    //public ExecutorService TriggerExecutorService() {
    //    return DtpRegistry.getDtpExecutor("maiar-log-pool");
    //}

}