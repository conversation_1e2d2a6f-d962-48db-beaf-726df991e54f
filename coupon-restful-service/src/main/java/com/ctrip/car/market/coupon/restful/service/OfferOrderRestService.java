package com.ctrip.car.market.coupon.restful.service;

import com.ctrip.basebiz.membersinfo.service.GetUserInfoRequestType;
import com.ctrip.basebiz.membersinfo.service.GetUserInfoResponseType;
import com.ctrip.basebiz.membersinfo.service.MembersinfoJServiceClient;
import com.ctrip.basebiz.membersinfo.service.ParameterItem;
import com.ctrip.basebiz.membersinfo.service.QueryCondition;
import com.ctrip.basebiz.membersinfo.service.QueryUserInfo;
import com.ctrip.basebiz.membersinfo.service.SelectUserNameRequestType;
import com.ctrip.basebiz.membersinfo.service.SelectUserNameResponseType;
import com.ctrip.basebiz.membersinfo.service.UserNameItem;
import com.ctrip.car.api8961.service.soa.gen.Api8961Client;
import com.ctrip.car.api8961.service.soa.gen.OfferOrderMemberRequestType;
import com.ctrip.car.api8961.service.soa.gen.OfferOrderMemberResponseType;
import com.ctrip.car.api8961.service.soa.gen.OfferOrderOAuthCheckRequestType;
import com.ctrip.car.api8961.service.soa.gen.OfferOrderOAuthCheckResponseType;
import com.ctrip.car.market.coupon.restful.qconfig.QConfigHelper;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.coupon.restful.utils.CouponServiceQConfig;

import com.ctrip.car.market.coupon.restful.utils.EncryptHelper;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
/**
 * <AUTHOR>
 * @date 2020/12/25
 */
@Service
public class OfferOrderRestService {

    private static final ILog logger = LogManager.getLogger(OfferOrderRestService.class);

    private final CouponServiceQConfig couponServiceQConfig;

    private final Api8961Client api8961Client = Api8961Client.getInstance();

    private final MembersinfoJServiceClient membersinfoJServiceClient = MembersinfoJServiceClient.getInstance();

    public OfferOrderRestService(CouponServiceQConfig couponServiceQConfig) {
        this.couponServiceQConfig = couponServiceQConfig;
    }

    public com.ctrip.car.market.coupon.restful.contract.OfferOrderOAuthCheckResponseType offerOrderOAuthCheck(com.ctrip.car.market.coupon.restful.contract.OfferOrderOAuthCheckRequestType requestType) {

        logger.info("REST_offerOrderOAuthCheck.re", Optional.ofNullable(requestType.toString()).orElse(""));
        com.ctrip.car.market.coupon.restful.contract.OfferOrderOAuthCheckResponseType responseType = new com.ctrip.car.market.coupon.restful.contract.OfferOrderOAuthCheckResponseType();
        try {

            // 1. 参数校验, 客户的 加密 uid
            String encryptUid = requestType.getEncryptUid();
            if (StringUtils.isBlank(encryptUid)) {
                responseType.setOauthAccess(false);
                responseType.setResultMessage("encryptUid not allow blank");
                responseType.setResultCode(1);
                return responseType;
            }
            encryptUid = encryptUid.trim();
            String eid = requestType.getEid();
            if (StringUtils.isBlank(eid)) {
                responseType.setOauthAccess(false);
                responseType.setResultMessage("op eid not allow blank");
                responseType.setResultCode(1);
                return responseType;
            }
            eid = eid.trim();
            Integer operateType = requestType.getFromType();
            if (operateType == null || (operateType != 1 && operateType != 2)) {
                responseType.setOauthAccess(false);
                responseType.setResultMessage("operateType params error");
                responseType.setResultCode(1);
                return responseType;
            }
            // 只校验代客下单
            Integer channelId = requestType.getChannelId();
            if (operateType == 2) {
                String supportChannelId = couponServiceQConfig.getValueByKeyFromQconfig("coupon.restful.offer.order.support.channel.id");
                if (StringUtils.isBlank(supportChannelId)) {
                    logger.warn("offer.order.rest.channelId.need.config", supportChannelId);
                    responseType.setOauthAccess(false);
                    responseType.setResultMessage("channelId not allow blank");
                    responseType.setResultCode(1);
                    return responseType;
                }
                if (channelId == null) {
                    responseType.setOauthAccess(false);
                    responseType.setResultMessage("channelId not allow blank");
                    responseType.setResultCode(1);
                    return responseType;
                }
                List<String> offerOrderSupportChannelIds
                        = Arrays.stream(supportChannelId.split(",")).collect(Collectors.toList());

                if (!offerOrderSupportChannelIds.contains(channelId.toString())) {
                    responseType.setOauthAccess(false);
                    responseType.setResultMessage("channelId not allow blank");
                    responseType.setResultCode(1);
                    return responseType;
                }
            }

            // 从 cookies 获取 H5 登录账号公共的 uid 校验 uid 是否被篡改,即校验当前 统一账号 uid 是否有权限
            String uid = BaseUtils.getUidByCommon(requestType.getHead());
            boolean checkCommonsUid = couponServiceQConfig.checkCommonUid(uid);
            logger.info("rest.oauth", "uid" + uid + ": checkUidResult: " + checkCommonsUid + ": decrypyUid:" + encryptUid);
            if (!checkCommonsUid) {
                responseType.setOauthAccess(false);
                responseType.setResultMessage(QConfigHelper.getChineseTips("CUR_LOGIN_OPERATE_DENY"));
                responseType.setResultCode(422);
                return responseType;
            }

            // 2. 数据获取
            OfferOrderOAuthCheckRequestType soaRequestType = new OfferOrderOAuthCheckRequestType();
            soaRequestType.setEncryptUid(encryptUid);
            soaRequestType.setEid(eid);
            soaRequestType.setOperateType(operateType);
            soaRequestType.setChannelId(channelId);
            OfferOrderOAuthCheckResponseType oAuthCheckResponseType = api8961Client.offerOrderOAuthCheck(soaRequestType);
            if (oAuthCheckResponseType!=null && oAuthCheckResponseType.isOauthAccess() != null && oAuthCheckResponseType.isOauthAccess()) {
                responseType.setResultCode(oAuthCheckResponseType.getResultCode());
                responseType.setOauthAccess(oAuthCheckResponseType.isOauthAccess());
                responseType.setResultMessage(oAuthCheckResponseType.getResultMessage());
            } else {
                responseType.setResultCode(421);
                responseType.setResultMessage( QConfigHelper.getChineseTips("CUR_OP_OPERATE_DENY"));
                responseType.setOauthAccess(false);
            }
            responseType.setResponseStatus(oAuthCheckResponseType.getResponseStatus());
        } catch (Exception e) {
            logger.error("offer.order.oauth.check.occur.error", e);
            responseType.setOauthAccess(false);
            responseType.setResultCode(1);
            responseType.setResultMessage("please try again later");
        }
        logger.info("offer.order.oauth.response.success", Optional.ofNullable(responseType.toString()).orElse(""));
        return responseType;
    }

    public com.ctrip.car.market.coupon.restful.contract.OfferOrderMemberResponseType offerOrderMember(com.ctrip.car.market.coupon.restful.contract.OfferOrderMemberRequestType requestType) {
        logger.info("offer.order.member.request", Optional.ofNullable(requestType.toString()).orElse(""));
        com.ctrip.car.market.coupon.restful.contract.OfferOrderMemberResponseType responseType = new com.ctrip.car.market.coupon.restful.contract.OfferOrderMemberResponseType();
        try {
            String realUid = BaseUtils.getUidByCommon(requestType.getHead());
            // 1. 参数校验

            String uid = requestType.getEncryptUid();
            if (StringUtils.isBlank(uid)||StringUtils.isBlank(realUid)) {
                responseType.setResultMessage("uid not allow blank");
                responseType.setResultCode(1);
                return responseType;
            }
            uid = uid.trim();
            String eid = requestType.getEid();
            if (StringUtils.isBlank(eid)) {
                responseType.setResultMessage("eid not allow blank");
                responseType.setResultCode(1);
                return responseType;
            }
            Integer channelId = requestType.getChannelId();
            boolean specificChannelExist = couponServiceQConfig.findSpecificChannelExist(channelId);
            if (!specificChannelExist) {
                responseType.setResultMessage("channelId not allow access");
                responseType.setResultCode(1);
                return responseType;
            }
            Integer operateType = requestType.getFromType();
            if (operateType == null || (operateType != 1 && operateType != 2)) {
                responseType.setResultMessage("fromType not allow access");
                responseType.setResultCode(1);
                return responseType;
            }

            // 2. 权限校验
            OfferOrderOAuthCheckRequestType oAuthCheckRequestType = new OfferOrderOAuthCheckRequestType();
            oAuthCheckRequestType.setOperateType(operateType);
            oAuthCheckRequestType.setChannelId(channelId);
            oAuthCheckRequestType.setEid(eid);
            oAuthCheckRequestType.setEncryptUid(uid);
            OfferOrderOAuthCheckResponseType oAuthCheckResponseType = api8961Client.offerOrderOAuthCheck(oAuthCheckRequestType);
            if (!oAuthCheckResponseType.getResultMessage().equals("success") || !oAuthCheckResponseType.isOauthAccess()) {
                responseType.setResultMessage("current uid not  allow access");
                responseType.setResultCode(1);
                return responseType;
            }

            // 3. 获取用户名
            String decryptUid = EncryptHelper.decrypt(uid);
            logger.warn("offerOrderMember:decryptUid:"+decryptUid);
            if (StringUtils.isNotBlank(decryptUid)) {
                responseType.setUid(decryptUid);
                SelectUserNameRequestType selectUserNameRequestType = new SelectUserNameRequestType();
                selectUserNameRequestType.setUids(Collections.singletonList(decryptUid));
                SelectUserNameResponseType selectUserNameResponseType = membersinfoJServiceClient.selectUserName(selectUserNameRequestType);
                List<UserNameItem> userNameList = selectUserNameResponseType.getUserNameList();
                if (CollectionUtils.isNotEmpty(userNameList)) {
                    UserNameItem userNameItem = userNameList.get(0);
                    if (Objects.nonNull(userNameItem)) {
                        responseType.setCustomerName(userNameItem.getUserNameC());
                    }
                }
            }

            // 4. 获取手机号
            GetUserInfoRequestType getUserInfoRequestType = new GetUserInfoRequestType();
            getUserInfoRequestType.setQueryKey(decryptUid);
            ParameterItem parameterItem = new ParameterItem();
            parameterItem.setKey("bizType");
            parameterItem.setValue("CAR");
            getUserInfoRequestType.setParameterList(Arrays.asList(parameterItem));
            QueryCondition queryCondition = new QueryCondition();
            queryCondition.setKey("needbindinfo");
            GetUserInfoResponseType userInfo = membersinfoJServiceClient.getUserInfo(getUserInfoRequestType);
            QueryUserInfo thisUserInfo = userInfo.getThisUserInfo();
            if (Objects.nonNull(thisUserInfo)) {
                responseType.setPhoneNumber(thisUserInfo.getMobilePhone());
            }

            // 5. 获取剩余用户画像的属性
            OfferOrderMemberRequestType offerOrderMemberRequestType = new OfferOrderMemberRequestType();
            offerOrderMemberRequestType.setEncryptUid(uid);
            OfferOrderMemberResponseType offerOrderMemberResponseType = api8961Client.offerOrderMember(offerOrderMemberRequestType);
            responseType.setResultMessage("success");
            responseType.setResultCode(0);
            responseType.setCertificateNumber(offerOrderMemberResponseType.getCertificateNumber());
            responseType.setCertificateType(offerOrderMemberResponseType.getCertificateType());
            responseType.setMemberLevel(offerOrderMemberResponseType.getMemberLevel());
            responseType.setPremiumCarPercentage(offerOrderMemberResponseType.getPremiumCarPercentage());
            responseType.setPriceSensitivity(offerOrderMemberResponseType.getPriceSensitivity());
            responseType.setSuperMember(offerOrderMemberResponseType.isSuperMember());
            responseType.setRentCarAppPreferences(offerOrderMemberResponseType.getRentCarAppPreferences());

        } catch (Exception e) {
            logger.error("offer.order.member.info.occur.error", e);
            responseType.setResultCode(1);
            responseType.setResultMessage("please try again later");
        }
        logger.info("offer.order.member.response.success", Optional.ofNullable(responseType.toString()).orElse(""));
        return responseType;

    }

}
