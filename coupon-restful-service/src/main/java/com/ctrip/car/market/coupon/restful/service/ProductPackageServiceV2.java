package com.ctrip.car.market.coupon.restful.service;

import com.ctrip.car.market.BaseRequest;
import com.ctrip.car.market.KeyValueDTO;
import com.ctrip.car.market.common.cache.annotations.NCache;
import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.car.market.coupon.restful.contract.CarProductPackage;
import com.ctrip.car.market.coupon.restful.contract.QueryCarProductsRequestType;
import com.ctrip.car.market.coupon.restful.contract.QueryCarProductsResponseType;
import com.ctrip.car.market.coupon.restful.utils.BaseUtils;
import com.ctrip.car.market.crossrecommend.service.contract.CarCrossRecommendedServiceClient;
import com.ctrip.car.market.crossrecommend.service.contract.dto.RecommendProductDTO;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductForLegaoRequestType;
import com.ctrip.car.market.crossrecommend.service.contract.servicetype.CarRecommendProductForLegaoResponseType;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ProductPackageServiceV2 {


    private static final ILog logger = LogManager.getLogger(ProductPackageServiceV2.class);
    private  CarCrossRecommendedServiceClient carCrossRecommendedServiceClient = CarCrossRecommendedServiceClient.getInstance();

    @NCache(holdMinute = 10, log = true, reHoldMinute = 5)
    public QueryCarProductsResponseType queryCarProducts(QueryCarProductsRequestType request) {

        QueryCarProductsResponseType response = new QueryCarProductsResponseType();
        try {
            return getCarProductsV2(convertTORequest(request));
        } catch (Exception e) {
            logger.error("queryCarProducts", e);
            response.setResultCode(500);
        }
        return response;
    }


    public QueryCarProductsResponseType getCarProductsV2(CarRecommendProductForLegaoRequestType request) {
        QueryCarProductsResponseType result = new QueryCarProductsResponseType();
        try {
            Map<String, String> tags = new HashMap<String, String>();
            tags.put("uid", request.getBaseRequest().getUid());
            tags.put("channelId", "" + request.getBaseRequest().getChannelId());
            tags.put("cityId", "" + request.getPickUpCityId());
            CarRecommendProductForLegaoResponseType responseType = carCrossRecommendedServiceClient.carRecommendProductforLegao(request);
            if (responseType != null) {
                result.setMoreAppUrl(responseType.getAppUrl());
                result.setMoreH5Url(responseType.getH5Url());
                result.setMorePcUrl(responseType.getPcUrl());
                result.setMoreAliPayUrl(responseType.getAlipayUrl());
                result.setMoreWeChatUrl(responseType.getWechatUrl());
                result.setMoreBaiduUrl(responseType.getBaiduUrl());
                // 已经和产品确认 没有无忧租和信用租落地页了，此处无用了
            }
            if (responseType != null && CollectionUtils.isNotEmpty(responseType.getRecommendProducts())) {
                result.setProductPackageInfos(convertToResponse(responseType.getRecommendProducts()));
                result.setMoreAppUrl(responseType.getRecommendProducts().get(0).getAppUrl());
                result.setMoreH5Url(responseType.getRecommendProducts().get(0).getH5Url());
                result.setMorePcUrl(responseType.getRecommendProducts().get(0).getPcUrl());
                result.setResultCode(200);
            } else {
                logger.warn("getCarProducts", JsonUtil.toString(request) + "; no data", tags);
                result.setResultCode(0);
            }
        } catch (Exception ex) {
            logger.error("getCarProducts", ex);
            result.setResultCode(500);
        }
        return result;
    }

    private List<CarProductPackage> convertToResponse(List<RecommendProductDTO> productPackageInfos) {
        List<CarProductPackage> result = new ArrayList<>();
        result.addAll(productPackageInfos.stream().map(x -> {
            CarProductPackage item = new CarProductPackage();
            item.setAutomatic(("自动挡").equalsIgnoreCase(x.getTrans())?1:0);
            item.setCommentCount(Optional.ofNullable(x.getCommentCount()).orElse(28L).intValue() );
            item.setCouponAmount(x.getCouponAmount());
         //   item.setCouponRate(x.getCouponRate());
         //   item.setCurrency(x.getCurrency());
           // item.setDisplacement(x.getDisplacement());

            item.setListPageAppUrl(x.getAppUrl());
            item.setListPageH5Url(x.getH5Url());
            item.setListPageWeChatUrl(x.getWeChatUrl());
            item.setListPagePcUrl(x.getPcUrl());
            if(StringUtils.isNotEmpty( x.getLuggageNo())) {
                item.setLuggageCount(new Integer(x.getLuggageNo()));
            }
            if(x.getCommentCount()!=null) {
                item.setNewComment("" + x.getCommentCount());
            }
            item.setOrderCount(Optional.ofNullable(x.getCommentCount()).orElse(28L).intValue() );
            item.setProductname(x.getProductName());
            if(StringUtils.isNotEmpty(x.getSeat())) {
                item.setSeating(new Integer(x.getSeat()));
            }
        /*    item.setServicelabels(x.getServicelabels());
            item.setStock(x.getst);*/
            item.setStoreId(""+x.getStoreId());
            item.setStoreName(x.getStoreName());
            item.setVehicleGroupId(x.getVehicleGroupCode());
            item.setVehicleGroupName(x.getVehicleGroupName());
            item.setVehicleImg(x.getProductImg());
            item.setVehicleName(x.getProductName());
            item.setVehiclePrice(x.getPrice());
            if (x.getProductScore() != null) {
                item.setVehicleScore(""+x.getProductScore());
            }

            item.setVehicleId(x.getProductId());
           // item.setVehicleScoreDesc(x.getVehicleScoreDesc());
            item.setVendorLogo(x.getVendorLogo());
            item.setVendorName(x.getVendorName());
            item.setVehicleOriginPrice(x.getOriginPrice());
          //  item.setVehicleLabelIds(x.getVehicleLabel());
            item.setListPageAliPayUrl(x.getAlipayUrl());
            return item;
        }).collect(Collectors.toList()));

        return result;
    }

    private CarRecommendProductForLegaoRequestType convertTORequest(QueryCarProductsRequestType request) {
        BaseRequest baseRequest = new BaseRequest();
        CarRecommendProductForLegaoRequestType req = new CarRecommendProductForLegaoRequestType();
        if (request.getStartDate() != null) {
            req.setPickUpTime(request.getStartDate());
        }
        if (request.getEndDate() != null) {
            req.setReturnTime(request.getEndDate());
        }
        baseRequest.setBizType(35);
        baseRequest.setRequestId(UUID.randomUUID().toString());
        baseRequest.setChannelId(request.getChannelId());
        baseRequest.setUid(BaseUtils.getUidByCommon(request.getHead()));
        if (CollectionUtils.isNotEmpty(request.getActIds())) {
            req.setLabelIds(request.getActIds().stream().map(Long::valueOf).collect(Collectors.toList()));
        }

        req.setPickUpAirportcode(request.getAirportcode());
        req.setPickUpCityId(Optional.ofNullable(request.getCityId()).orElse(request.getRcityId()).longValue());
        req.setReturnCityId(Optional.ofNullable(request.getCityId()).orElse(request.getRcityId()).longValue());
        req.setPageSize(request.getCount());
        req.setPageIndex(1);
        req.setSort(0);
        if (StringUtils.isNotEmpty(request.getVehicleGroupId())) {
            req.setVehiclegroupIds(Arrays.asList(request.getVehicleGroupId()));
        }

        if (StringUtils.isNotEmpty(request.getVendorId())) {
            req.setVendorIds(Arrays.asList(Long.getLong(request.getVendorId())));
        }
        if (StringUtils.isNotEmpty(request.getVehicleId())) {
            req.setProductIds(Arrays.asList(Long.parseLong(request.getVehicleId())));
        }
        req.setPackageType(request.getPackageTypes());
        req.setAppType("ISD_C_APP");
        baseRequest.setChannelId(14256);
        baseRequest.setExtList(Arrays.asList(new KeyValueDTO(){{
            this.setDisplayName("1");
            this.setKey("orignScenes");
        }}));
        if (request.getChannelId() != null && request.getChannelId() > 0) {
            baseRequest.setChannelId(request.getChannelId());
        }
        if(StringUtils.isNotEmpty(request.getAppType()))
        {
            req.setAppType(request.getAppType());
        }
        req.setBaseRequest(baseRequest);
        req.setVehicleLabelIds(request.getVehicleLabelIds());
        return req;
    }
}
