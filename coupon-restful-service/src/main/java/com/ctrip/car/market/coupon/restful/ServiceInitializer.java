package com.ctrip.car.market.coupon.restful;

import com.ctrip.car.market.common.cache.annotations.MktCacheCovery;
import com.dtp.core.spring.EnableDynamicTp;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.support.SpringBootServletInitializer;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import qunar.tc.qmq.consumer.annotation.EnableQmq;

@SpringBootApplication(exclude = DataSourceAutoConfiguration.class, scanBasePackages = {"com.ctrip.car.market.coupon.restful", "com.ctrip.car.maiar", "com.ctrip.car.jetcache"})
@EnableAspectJAutoProxy(proxyTargetClass = true)
@EnableDynamicTp
@MktCacheCovery(localCacheMinite = 1)
@EnableQmq
public class ServiceInitializer extends SpringBootServletInitializer {
    /**
     * Configure your application when it’s launched by the servlet container
     */
    @Override
    protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
        return application.sources(ServiceInitializer.class);
    }

    /**
     * 错误拦截
     */
    public ServiceInitializer() {
        super();
        setRegisterErrorPageFilter(false); // <- this one
    }

}
