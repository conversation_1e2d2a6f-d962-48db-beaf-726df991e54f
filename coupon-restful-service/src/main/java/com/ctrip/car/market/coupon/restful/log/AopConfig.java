package com.ctrip.car.market.coupon.restful.log;

import com.ctrip.car.market.common.log.Tiger;
import com.ctrip.car.market.common.log.TigerLogProxy;
import com.ctrip.car.market.common.util.JsonUtil;
import com.ctrip.car.market.common.utils.GsonUtils;
import com.ctrip.car.market.coupon.restful.contract.BaseRequest;
import com.ctrip.car.market.coupon.restful.utils.ReflectUtil;
import com.ctrip.framework.clogging.agent.log.ILog;
import com.ctrip.framework.clogging.agent.log.LogManager;
import com.ctrip.soa.car.arch.logservice.v1.CMNBusinessLogDTO;
import com.dianping.cat.Cat;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.ReentrantLock;

/**
 * Created by zmqu on 2018/1/2.
 */
@Aspect
@Component
public class AopConfig {

    public static final String BASE_REQUEST_NAME = "baseRequest";

    private final ILog log = LogManager.getLogger(AopConfig.class);

    @Resource
    private TigerLogProxy tigerLogProxy;

    @Resource(name = "maiar-log-pool")
    private ExecutorService TriggerExecutorService;

    @Pointcut("execution(* com.ctrip.car.market.coupon.restful.CarMarketingCouponRestfulImpl.*(..))")
    public void executeService() {

    }

    //埋点
    @Around("executeService()")
    public Object doConcurrentOperation(ProceedingJoinPoint pjp) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        Method method = methodSignature.getMethod();
        Tiger tiger = method.getDeclaredAnnotation(Tiger.class);
        Calendar requestTime = Calendar.getInstance();
        // 深拷贝请求体
        Object[] request = pjp.getArgs();
        try {
            for (int i = 0; i < request.length; i++) {
                Object clone = GsonUtils.deepClone(request[i], request[i].getClass());
                request[i] = clone;
            }
        } catch (Exception ignore) {}
        Object response = pjp.proceed();
        Calendar responseTime = Calendar.getInstance();
        if (tiger != null) {
            String messageID = Cat.getCurrentMessageId();
         //   ReentrantLock reentrantLock = new ReentrantLock(true);
            TriggerExecutorService.execute(() -> {
                try {
                   // reentrantLock.tryLock(100, TimeUnit.MILLISECONDS);
                  // CMNBusinessLogDTO logDTO = tigerLogProxy.parseTigerAnnotation(method, tiger, pjp.getArgs(), response, messageID);
                    BaseRequest baseRequest = getBaseRequest(request[0]);
                    String requestId = baseRequest != null ? baseRequest.getRequestId() : null;
                    CMNBusinessLogDTO carCMNCommonInfo=new CMNBusinessLogDTO();
                    carCMNCommonInfo.setTraceId(messageID);
                    carCMNCommonInfo.setMobile(requestId);
                    carCMNCommonInfo.setAppID("100014699");
                    carCMNCommonInfo.setBu("car");
                    carCMNCommonInfo.setServiceName(Optional.ofNullable(tiger.actionName()).orElse("14699"));
                    carCMNCommonInfo.setActionName(Optional.ofNullable(tiger.actionName()).orElse("14699"));
                    carCMNCommonInfo.setProcessDesc(tiger.processDesc());
                    carCMNCommonInfo.setLogType((short) 0);
                    carCMNCommonInfo.setOperationTime(Calendar.getInstance());
                    carCMNCommonInfo.setRequestTime(requestTime);
                    carCMNCommonInfo.setResponseTime(responseTime);
                    carCMNCommonInfo.setRequestContent(JsonUtil.toString(request));
                    carCMNCommonInfo.setResponseContent(JsonUtil.toString(response));
                    tigerLogProxy.writeCarCMNCommonInfo(carCMNCommonInfo);
                  //  reentrantLock.unlock();
                } catch (Exception e) {
                    log.warn("AopConfig.doConcurrentOperation", e);
                } finally {
                  //  reentrantLock.unlock();
                }
            });
        }
        return response;
    }
    private static final ExpressionParser parser = new SpelExpressionParser();
    private static <T> T parseSafe(String expression, Object context, Class<T> clazz, T defaultValue) {
        if (StringUtils.isEmpty(expression)) {
            return defaultValue;
        } else {
            try {
                return parser.parseExpression(expression).getValue(context, clazz);
            } catch (Exception var5) {
                return defaultValue;
            }
        }
    }

    private BaseRequest getBaseRequest(Object requestObj) {
        try {
            Object baseRequestObj = ReflectUtil.getFieldValue(requestObj, BASE_REQUEST_NAME);
            if (baseRequestObj instanceof BaseRequest) {
                return (BaseRequest) baseRequestObj;
            }
        } catch (Exception e) {
        }
        return null;
    }
}
