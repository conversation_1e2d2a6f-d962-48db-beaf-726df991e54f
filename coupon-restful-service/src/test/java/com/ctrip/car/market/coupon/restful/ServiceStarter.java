package com.ctrip.car.market.coupon.restful;

import java.awt.Desktop;
import java.net.URI;

import org.springframework.boot.SpringApplication;

public class ServiceStarter {

    public static void main(String[] args) throws Exception {
        System.setProperty("java.awt.headless", "false");

        SpringApplication.run(ServiceInitializer.class);

        // port 9090 is configured in src/test/resources/application.properties(key: server.port)
        Desktop.getDesktop().browse(new URI("http://127.0.0.1:8080/api"));
    }
}
